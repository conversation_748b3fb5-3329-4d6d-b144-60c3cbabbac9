{
  "name": "@bybeam/{{service}}-server",
  "version": "0.0.0",
  "private": true,
  "type": "module",
  "scripts": {
    "build": "rm -rf dist && tsc --project tsconfig.build.json",
    "nest": "nest",
    "start": "nest start",
    "start:dev": "nest start --watch",
    "start:debug": "nest start --debug --watch",
    "start:prod": "node dist/main",
    "test": "vitest"
  },
  "devDependencies": {
    "@nestjs/cli": "^10.2.1",
    "@nestjs/schematics": "^10.0.3",
    "@nestjs/testing": "^10.3.0",
    "@tsconfig/node20": "20.1.2",
    "@types/node": "20.12.7",
    "@vitest/coverage-v8": "2.1.2",
    "source-map-support": "0.5.21",
    "ts-loader": "^9.5.1",
    "ts-node": "10.9.2",
    "tsconfig-paths": "^4.2.0",
    "typescript": "5.3.3",
    "unplugin-swc": "^1.4.4",
    "vitest": "1.3.1"
  },
  "dependencies": {
    "@bybeam/common-proto": "workspace:^",
    "@bybeam/infrastructure-lib": "workspace:*",
    "@bybeam/{{service}}-client": "workspace:^",
    "@grpc/grpc-js": "1.10.9",
    "@grpc/proto-loader": "0.7.10",
    "@nestjs/common": "^10.3.0",
    "@nestjs/config": "^3.1.1",
    "@nestjs/core": "^10.3.0",
    "@nestjs/microservices": "^10.3.0",
    "@nestjs/terminus": "^10.2.0",
    {{#if database.database}}"@nestjs/typeorm": "^10.0.1",{{/if}}
    "@opentelemetry/api": "1.7.0",
    "@opentelemetry/auto-instrumentations-node": "0.40.2",
    "@opentelemetry/exporter-metrics-otlp-http": "0.45.1",
    "@opentelemetry/exporter-trace-otlp-http": "0.45.1",
    "@opentelemetry/resources": "^1.21.0",
    "@opentelemetry/sdk-metrics": "1.18.1",
    "@opentelemetry/sdk-node": "0.45.1",
    "@opentelemetry/sdk-trace-base": "^1.21.0",
    "@opentelemetry/semantic-conventions": "^1.21.0",
    "grpc-health-check": "^2.0.0",
    "nestjs-grpc-reflection": "^0.2.2",
    "nestjs-pino": "4.0.0",
    {{#if database.database}}"opentelemetry-instrumentation-typeorm": "0.40.0",{{/if}}
    {{#if database.database}}"pg": "8.11.3",{{/if}}
    {{#if database.database}}"reflect-metadata": "0.1.14",{{/if}}
    "rxjs": "^7.8.1",
    "typeorm": "catalog:default"
  }
}