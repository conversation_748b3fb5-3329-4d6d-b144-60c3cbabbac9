# Service vars ---
PROJECT_ID=data-platform-sandbox-beam
ENVIRONMENT=development
PORT=11051
LOG_LEVEL=DEBUG

DATASET=data-platform-sandbox-beam.dev_core_public

# Elasticsearch connection ---
ES_CLOUD_ID=
ES_API_KEY=

# Database information ---
DB_HOST=127.0.0.1
DB_PORT=9012
DB_USER=main
DB_PASSWORD=<PASSWORD>
DB_NAME=core

# Document AI Database information ---
DOCUMENT_AI_CONNECTION=postgres://main:<PASSWORD>@127.0.0.1:9023/document_ai

# Cloud storage (these are in 1password)
BUCKET=beam-platform-docs-dev
GCS_ACCESS_KEY="ADD HERE"
GCS_SECRET_KEY="ADD HERE"

# doctopus url
DOCTOPUS_URL=http://127.0.0.1:8000

# Google Application Default Credentials - Service Account
# For local development, create a service account key:
#   mkdir -p ~/.gcp-keys
#   gcloud iam service-accounts keys create ~/.gcp-keys/entity-resolution-dev.json \
#     --iam-account=<EMAIL> \
#     --project=core-platform-dev-beam
# Then set the path to the key file:
GOOGLE_APPLICATION_CREDENTIALS=~/.gcp-keys/entity-resolution-dev.json
