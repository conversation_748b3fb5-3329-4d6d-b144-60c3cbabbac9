{"name": "@bybeam/config-server", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "rm -rf dist && tsc --project tsconfig.build.json", "nest": "nest", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "vitest"}, "devDependencies": {"@bybeam/platform-types": "workspace:^", "@bybeam/typescript-config": "workspace:^", "@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/node": "catalog:default", "@vitest/coverage-v8": "catalog:default", "source-map-support": "0.5.21", "ts-loader": "^9.5.1", "ts-node": "catalog:default", "tsconfig-paths": "catalog:default", "typescript": "catalog:default", "unplugin-swc": "^1.5.8", "vitest": "catalog:default"}, "dependencies": {"@bybeam/config-client": "workspace:*", "@bybeam/infrastructure-lib": "workspace:*", "@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "@nestjs/common": "^11.0.12", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.5", "@nestjs/microservices": "^10.4.6", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "grpc-health-check": "^2.0.0", "nestjs-grpc-reflection": "^0.2.2", "nestjs-pino": "4.0.0", "pg": "8.13.1", "reflect-metadata": "0.1.14", "rxjs": "^7.8.1", "typeorm": "catalog:default"}}