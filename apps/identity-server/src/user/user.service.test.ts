import { Role, UpdateUserRequest } from '@bybeam/identity-client/types';
import { FeatureName } from '@bybeam/platform-types';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { In } from 'typeorm';
import { describe, expect, it, vi } from 'vitest';
import { AdminService } from '../admin/admin.service.js';
import { AuthorizationService } from '../authorization/authorization.service.js';
import { NotificationService } from '../notification/notification.service.js';
import { VerificationService } from '../verification/verification.service.js';
import { PermissionsService } from './permissions.service.js';
import { ProvisionService } from './provision.service.js';
import { UserService } from './user.service.js';

vi.mock('node:crypto', async (importOriginal) => ({
  ...importOriginal(),
  randomBytes: vi.fn().mockReturnValue(Buffer.from('test')),
  randomUUID: vi.fn().mockReturnValue('mockRandomUUID'),
}));

describe('UserService', () => {
  describe('createUser', () => {
    it('should provision the user', async () => {
      const provisionGCIPUserFn = vi.fn();
      const provisionUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { debug: vi.fn(), info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return { get: vi.fn().mockReturnValueOnce('app-mock-123') };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === ProvisionService)
            return {
              provisionGCIPUser: provisionGCIPUserFn.mockResolvedValueOnce({ uid: 'mockGCIPUID' }),
              provisionUser: provisionUserFn.mockResolvedValueOnce({ userId: 'mockUserId' }),
            };
          if (token === 'CORE_PartnerEntityRepository')
            return { findOne: vi.fn().mockResolvedValueOnce({ config: undefined, features: [] }) };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                advocates: [],
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
              }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).createUser({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        roles: ['applicant'],
        applicantTypeId: 'mockApplicantTypeId',
        _email: 'email',
        _name: 'name',
        _password: 'password',
        _phone: 'phone',
        _applicantTypeId: 'applicantTypeId',
      });

      expect(result).toEqual({
        message: 'successfully created user',
        id: 'mockUserId',
        advocates: [],
        applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
      });
      expect(provisionGCIPUserFn).toHaveBeenCalledWith({
        tenantId: 'app-mock-123',
        user: {
          email: '<EMAIL>',
          name: '<EMAIL>',
          phone: undefined,
          verifiedEmail: false,
        },
      });
      expect(provisionUserFn).toHaveBeenCalledWith({
        gcipUid: 'mockGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'app-mock-123',
        user: {
          email: '<EMAIL>',
          name: '<EMAIL>',
          phone: undefined,
          verifiedEmail: false,
        },
        roles: ['applicant'],
        applicantTypeId: 'mockApplicantTypeId',
        sudoRequest: true,
      });
    });
    it('should generate an email if one is not provided', async () => {
      const provisionGCIPUserFn = vi.fn();
      const provisionUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService')
            return { debug: vi.fn(), info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return { get: vi.fn().mockReturnValueOnce('app-mock-123') };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return {
              provisionGCIPUser: provisionGCIPUserFn.mockResolvedValueOnce({ uid: 'mockGCIPUID' }),
              provisionUser: provisionUserFn.mockResolvedValueOnce({ userId: 'mockUserId' }),
            };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository')
            return { findOne: vi.fn().mockResolvedValueOnce({ config: undefined, features: [] }) };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                advocates: [],
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
              }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).createUser({
        partnerId: 'mockPartnerId',
        roles: ['applicant'],
        _email: 'email',
        _name: 'name',
        _password: 'password',
        _phone: 'phone',
        _applicantTypeId: 'applicantTypeId',
      });

      expect(result).toEqual({
        message: 'successfully created user',
        id: 'mockUserId',
        advocates: [],
        applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
      });
      expect(provisionGCIPUserFn).toHaveBeenCalledWith({
        tenantId: 'app-mock-123',
        user: {
          email: '<EMAIL>',
          name: '<EMAIL>',
          phone: undefined,
          verifiedEmail: false,
        },
      });
      expect(provisionUserFn).toHaveBeenCalledWith({
        gcipUid: 'mockGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'app-mock-123',
        user: {
          email: '<EMAIL>',
          name: '<EMAIL>',
          phone: undefined,
          verifiedEmail: false,
        },
        roles: ['applicant'],
        applicantTypeId: undefined,
        sudoRequest: true,
      });
    });
    it('should create the correct role relationship if the roles are not "applicant"', async () => {
      const provisionGCIPUserFn = vi.fn();
      const provisionUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { debug: vi.fn(), info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return { get: vi.fn().mockReturnValueOnce('app-mock-123') };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return {
              provisionGCIPUser: provisionGCIPUserFn.mockResolvedValueOnce({ uid: 'mockGCIPUID' }),
              provisionUser: provisionUserFn.mockResolvedValueOnce({ userId: 'mockUserId' }),
            };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository')
            return { findOne: vi.fn().mockResolvedValueOnce({ config: undefined, features: [] }) };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                advocates: [],
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
              }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).createUser({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        roles: ['standard'],
        _email: 'email',
        _name: 'name',
        _password: 'password',
        _phone: 'phone',
        _applicantTypeId: 'applicantTypeId',
      });

      expect(result).toEqual({
        message: 'successfully created user',
        id: 'mockUserId',
        advocates: [],
        applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
      });
      expect(provisionGCIPUserFn).toHaveBeenCalledWith({
        tenantId: 'app-mock-123',
        user: {
          email: '<EMAIL>',
          name: '<EMAIL>',
          phone: undefined,
          verifiedEmail: false,
        },
      });
      expect(provisionUserFn).toHaveBeenCalledWith({
        gcipUid: 'mockGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'app-mock-123',
        user: {
          email: '<EMAIL>',
          name: '<EMAIL>',
          phone: undefined,
          verifiedEmail: false,
        },
        roles: ['standard'],
        sudoRequest: true,
      });
    });
    it('should throw if any roles is invalid', async () => {
      const provisionGCIPUserFn = vi.fn();
      const provisionUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { debug: vi.fn(), info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return { get: vi.fn().mockReturnValueOnce('app-mock-123') };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return {
              provisionGCIPUser: provisionGCIPUserFn.mockResolvedValueOnce('mockGCIPUID'),
              provisionUser: provisionUserFn.mockResolvedValueOnce({ userId: 'mockUserId' }),
            };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository')
            return { findOne: vi.fn().mockResolvedValueOnce({ config: undefined, features: [] }) };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi
                .fn()
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                  applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                }),
            };
        })
        .compile();

      await expect(
        module.get<UserService>(UserService).createUser({
          email: '<EMAIL>',
          partnerId: 'mockPartnerId',
          roles: ['super_duper_admin'],
          _email: 'email',
          _name: 'name',
          _password: 'password',
          _phone: 'phone',
          _applicantTypeId: 'applicantTypeId',
        }),
      ).rejects.toThrow('invalid roles super_duper_admin');

      expect(provisionGCIPUserFn).not.toHaveBeenCalled();
      expect(provisionUserFn).not.toHaveBeenCalled();
    });
    it('should send user info for validation if feature `User: Validation` is enabled', async () => {
      const validateUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { debug: vi.fn(), info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return { get: vi.fn().mockReturnValueOnce('app-mock-123') };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === VerificationService) return { validateUser: validateUserFn };
          if (token === ProvisionService)
            return {
              provisionGCIPUser: vi.fn().mockResolvedValueOnce({ uid: 'mockGCIPUID' }),
              provisionUser: vi.fn().mockResolvedValueOnce({ userId: 'mockUserId' }),
            };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                config: undefined,
                features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
              }),
            };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                email: '<EMAIL>',
                advocates: [],
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
              }),
            };
        })
        .compile();

      await module.get<UserService>(UserService).createUser({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        roles: ['applicant'],
        applicantTypeId: 'mockApplicantTypeId',
        _email: 'email',
        _name: 'name',
        _password: 'password',
        _phone: 'phone',
        _applicantTypeId: 'applicantTypeId',
      });
      expect(validateUserFn).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'mockUserId',
          email: '<EMAIL>',
        }),
      );
    });
    it('should not validate user when feature `User: Validation` is not enabled', async () => {
      const validateUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { debug: vi.fn(), info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return { get: vi.fn().mockReturnValueOnce('app-mock-123') };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === VerificationService) return { validateUser: validateUserFn };
          if (token === ProvisionService)
            return {
              provisionGCIPUser: vi.fn().mockResolvedValueOnce({ uid: 'mockGCIPUID' }),
              provisionUser: vi.fn().mockResolvedValueOnce({ userId: 'mockUserId' }),
            };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                config: undefined,
                features: [{ enabled: false, feature: { name: FeatureName.UserValidation } }],
              }),
            };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                advocates: [],
                applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
              }),
            };
        })
        .compile();

      await module.get<UserService>(UserService).createUser({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        roles: ['applicant'],
        applicantTypeId: 'mockApplicantTypeId',
        _email: 'email',
        _name: 'name',
        _password: 'password',
        _phone: 'phone',
        _applicantTypeId: 'applicantTypeId',
      });

      expect(validateUserFn).not.toHaveBeenCalled();
    });
  });
  describe('verifyEmail', () => {
    it('should validate the token, find the user in the identity DB, and set the email as verified', async () => {
      const verifyEmailFn = vi.fn();
      const updateUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const auditLogFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              verifyEmail: verifyEmailFn,
              verifyToken: vi
                .fn()
                .mockReturnValueOnce({ uid: 'mockGCIPUID', email_verified: false }),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return { update: updateCoreUserFn };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return { save: auditLogFn };
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi
                .fn()
                .mockResolvedValueOnce({ id: 'mockUserId', advocates: [], applicants: [] }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.verifyEmail({
        token: 'mockToken',
        tenantId: 'mockTenantId',
      });

      expect(verifyEmailFn).toHaveBeenCalledWith('mockGCIPUID', 'mockTenantId');
      expect(updateUserFn).toHaveBeenCalledWith({ id: 'mockUserId' }, { verifiedEmail: true });
    });
    it('should update all linked users in the core db', async () => {
      const verifyEmailFn = vi.fn();
      const updateUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const auditLogFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              verifyEmail: verifyEmailFn,
              verifyToken: vi
                .fn()
                .mockReturnValueOnce({ uid: 'mockGCIPUID', email_verified: false }),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return { update: updateCoreUserFn };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return { save: auditLogFn };
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockUserId',
                advocates: [{ coreUserId: 'mockCoreUserId1' }],
                applicants: [{ id: 'mockCoreUserId2' }, { id: 'mockCoreUserId3' }],
              }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.verifyEmail({
        token: 'mockToken',
        tenantId: 'mockTenantId',
      });

      expect(verifyEmailFn).toHaveBeenCalledWith('mockGCIPUID', 'mockTenantId');
      expect(updateUserFn).toHaveBeenCalledWith({ id: 'mockUserId' }, { verifiedEmail: true });
      expect(updateCoreUserFn).toHaveBeenCalledWith(
        {
          id: In(['mockCoreUserId1', 'mockCoreUserId2', 'mockCoreUserId3']),
        },
        { validatedEmail: true },
      );
    });
    it('should throw if the token is invalid', async () => {
      const verifyEmailFn = vi.fn();
      const updateUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const auditLogFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              verifyEmail: verifyEmailFn,
              verifyToken: vi.fn().mockRejectedValueOnce(new Error('yeesh')),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return { update: updateCoreUserFn };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return { save: auditLogFn };
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce(null),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.verifyEmail({
          token: 'mockToken',
          tenantId: 'mockTenantId',
        }),
      ).rejects.toThrow('yeesh');

      expect(verifyEmailFn).not.toHaveBeenCalled();
      expect(updateUserFn).not.toHaveBeenCalled();
      expect(updateCoreUserFn).not.toHaveBeenCalled();
    });
    it('should throw NOT FOUND if user is not in the DB', async () => {
      const verifyEmailFn = vi.fn();
      const updateUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const auditLogFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              verifyEmail: verifyEmailFn,
              verifyToken: vi
                .fn()
                .mockReturnValueOnce({ uid: 'mockGCIPUID', email_verified: false }),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return { update: updateCoreUserFn };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return { save: auditLogFn };
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce(null),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.verifyEmail({
          token: 'mockToken',
          tenantId: 'mockTenantId',
        }),
      ).rejects.toThrow('user not found');

      expect(updateUserFn).not.toHaveBeenCalled();
      expect(updateCoreUserFn).not.toHaveBeenCalled();
    });
  });
  describe('sendVerificationEmail', () => {
    it('gets a token and sends the email if the user is found', async () => {
      const createTokenFn = vi.fn();
      const sendEmailFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return { createToken: createTokenFn.mockResolvedValueOnce('mockToken') };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return { sendEmail: sendEmailFn };
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi
                .fn()
                .mockResolvedValueOnce({ name: 'Unverified User', email: '<EMAIL>' }),
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.sendVerificationEmail({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        tenantId: 'mockTenantId',
      });

      expect(createTokenFn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        tenantId: 'mockTenantId',
      });
      expect(sendEmailFn).toHaveBeenCalledWith({
        notificationConfig: {
          context: {
            partnerId: 'mockPartnerId',
          },
          type: 'AuthenticationIdentityVerifyEmail',
          variables: {
            TOKEN: '%recipient.token%',
            TOKEN_LIFETIME: '%recipient.tokenLifetime%',
          },
        },
        recipients: [
          {
            email: '<EMAIL>',
            name: 'Unverified User',
            variables: {
              token: 'mockToken',
              tokenLifetime: '1',
            },
          },
        ],
      });
    });
    it('throws NOT_FOUND if the user is not found', async () => {
      const createTokenFn = vi.fn();
      const sendEmailFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return { createToken: createTokenFn.mockResolvedValueOnce('mockToken') };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return { sendEmail: sendEmailFn };
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce(null),
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.sendVerificationEmail({
          email: '<EMAIL>',
          partnerId: 'mockPartnerId',
          tenantId: 'mockTenantId',
        }),
      ).rejects.toThrow('user not found <NAME_EMAIL>');

      expect(createTokenFn).not.toHaveBeenCalled();
      expect(sendEmailFn).not.toHaveBeenCalled();
    });
  });
  describe('sendResetPasswordEmail', () => {
    it('gets a token and sends the email if the user is found', async () => {
      const createTokenFn = vi.fn();
      const sendEmailFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              createToken: createTokenFn.mockResolvedValueOnce('mockToken'),
              getUserByEmail: vi.fn().mockResolvedValueOnce({
                displayName: 'Mock User',
              }),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return { sendEmail: sendEmailFn };
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository') return {};
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.sendResetPasswordEmail({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        tenantId: 'mockTenantId',
      });

      expect(createTokenFn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        tenantId: 'mockTenantId',
      });
      expect(sendEmailFn).toHaveBeenCalledWith({
        notificationConfig: {
          context: {
            partnerId: 'mockPartnerId',
          },
          type: 'AuthenticationResetPassword',
          variables: {
            EMAIL: '%recipient.email%',
            TOKEN: '%recipient.token%',
          },
        },
        recipients: [
          {
            email: '<EMAIL>',
            name: 'Mock User',
            variables: {
              email: '<EMAIL>',
              token: 'mockToken',
            },
          },
        ],
      });
    });
    it('throws NOT_FOUND if the user is not found', async () => {
      const createTokenFn = vi.fn();
      const sendEmailFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              createToken: createTokenFn,
              getUserByEmail: vi.fn().mockResolvedValueOnce(null),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return { sendEmail: sendEmailFn };
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository') return {};
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.sendResetPasswordEmail({
          email: '<EMAIL>',
          partnerId: 'mockPartnerId',
          tenantId: 'mockTenantId',
        }),
      ).rejects.toThrow('user not found <NAME_EMAIL> in tenant mockTenantId');

      expect(createTokenFn).not.toHaveBeenCalled();
      expect(sendEmailFn).not.toHaveBeenCalled();
    });
  });
  describe('validateByEmail', () => {
    it('should throw if a user is not found and the role type is not "applicant"', async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return { provisionGCIPUser: vi.fn().mockResolvedValueOnce({ uid: 'mockGCIPUID' }) };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return { findOne: vi.fn().mockResolvedValueOnce(null) };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.validateByEmail({
          email: '<EMAIL>',
          partnerId: 'mockPartnerId',
          tenantId: 'adv-mock-id',
          timezone: 'America/Los_Angeles',
          _continueUrl: 'continueUrl',
        }),
      ).rejects.toThrow('cannot provision advocate user without token');
    });
    it('should provision the user if none is found', async () => {
      const provisionGCIPUserFn = vi.fn();
      const provisionUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return {
              provisionGCIPUser: provisionGCIPUserFn.mockResolvedValueOnce('mockGCIPUID'),
              provisionUser: provisionUserFn.mockResolvedValueOnce('success'),
            };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi
                .fn()
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                  applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByEmail({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        tenantId: 'app-mock-123',
        timezone: 'America/Los_Angle',
        _continueUrl: 'continueUrl',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
        applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
      });
    });
    it('should synchronize the existing user permissions if one is found', async () => {
      const provisionGCIPUserFn = vi.fn();
      const provisionUserFn = vi.fn();
      const syncPermissionsFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn() };
          if (token === AdminService)
            return {
              createUser: vi.fn().mockResolvedValueOnce('success'),
              getUserByEmail: vi.fn().mockResolvedValueOnce(undefined),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService)
            return { syncPermissions: syncPermissionsFn.mockResolvedValueOnce('sucess') };
          if (token === ProvisionService)
            return {
              provisionGCIPUser: provisionGCIPUserFn.mockResolvedValueOnce({
                uid: 'mockOtherGCIPUID',
              }),
              provisionUser: provisionUserFn.mockResolvedValueOnce('success'),
            };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: vi
                .fn()
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                  applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                })
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                  applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                })
                .mockResolvedValueOnce(null)
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [
                    { uid: 'mockGCIPUID', tenantId: 'app-mock-123' },
                    { uid: 'mockOtherGCIPUID', tenantId: 'app-other-123' },
                  ],
                  applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByEmail({
        email: '<EMAIL>',
        partnerId: 'mockPartnerId',
        tenantId: 'app-other-123',
        timezone: 'America/Los_Angeles',
        _continueUrl: 'continueUrl',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [
          { uid: 'mockGCIPUID', tenantId: 'app-mock-123' },
          { uid: 'mockOtherGCIPUID', tenantId: 'app-other-123' },
        ],
        applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
      });

      expect(syncPermissionsFn).toHaveBeenCalledWith({
        gcipUid: 'mockOtherGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'app-other-123',
        user: {
          id: 'mockUserId',
          applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
          gcipUsers: [{ tenantId: 'app-mock-123', uid: 'mockGCIPUID' }],
        },
      });
    });
  });
  describe('validateByToken', () => {
    it('should provision a new user if one is not found with the token uid', async () => {
      const provisionUserFn = vi.fn();
      const userFindFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({
                uid: 'mockGCIPUID',
                email: '<EMAIL>',
                email_verified: true,
              }),
            };

          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              update: vi.fn(),
              findOne: userFindFn
                // no user found
                .mockResolvedValueOnce(null)
                // lookup after provision and permission setting
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByToken({
        authenticationMechanism: 'UNKNOWN',
        partnerId: 'mockPartnerId',
        userId: 'mockGCIPUID',
        tenantId: 'adv-mock-123',
        token: 'mockToken',
        headers: '{"test": "something"}',
        sessionId: 'mockSessionId',
        _userId: 'userId',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [{ tenantId: 'adv-mock-123', uid: 'mockGCIPUID' }],
        advocates: [
          { coreUserId: 'mockCoreUserId', id: 'mockCoreAdminId', partnerId: 'mockPartnerId' },
        ],
      });
      expect(userFindFn).toHaveBeenCalledWith({
        where: { gcipUsers: [{ uid: 'mockGCIPUID' }] },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(provisionUserFn).toHaveBeenCalledWith({
        user: { email: '<EMAIL>', name: '<EMAIL>', verifiedEmail: true },
        gcipUid: 'mockGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'adv-mock-123',
        sessionId: 'mockSessionId',
        sudoRequest: false,
      });
    });
    it('should synchronize the existing user permissions if one is found with the token uid', async () => {
      const provisionUserFn = vi.fn();
      const syncPermissionsFn = vi.fn();
      const userFindFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              updateUser: vi.fn(),
              verifyToken: vi.fn().mockResolvedValueOnce({
                uid: 'mockGCIPUID',
                email: '<EMAIL>',
                email_verified: true,
              }),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService)
            return { syncPermissions: syncPermissionsFn.mockResolvedValue('success') };
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return { exist: vi.fn(), update: vi.fn() };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              update: vi.fn(),
              findOne: userFindFn

                // user with some permissions found
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                })

                // lookup after provision and permission setting
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  applicants: [{ id: 'mockOtherCoreUserId', partnerId: 'mockOtherCorePartnerId' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                })

                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  applicants: [{ id: 'mockOtherCoreUserId', partnerId: 'mockOtherCorePartnerId' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByToken({
        authenticationMechanism: 'UNKNOWN',
        partnerId: 'mockOtherCorePartnerId',
        userId: 'mockGCIPUID',
        tenantId: 'app-mock-123',
        token: 'mockToken',
        headers: '{"test": "something"}',
        sessionId: 'mockSessionId',
        _userId: 'userId',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [{ tenantId: 'adv-mock-123', uid: 'mockGCIPUID' }],
        advocates: [
          { coreUserId: 'mockCoreUserId', id: 'mockCoreAdminId', partnerId: 'mockPartnerId' },
        ],
        applicants: [{ id: 'mockOtherCoreUserId', partnerId: 'mockOtherCorePartnerId' }],
      });
      expect(userFindFn).toHaveBeenCalledWith({
        where: { gcipUsers: [{ uid: 'mockGCIPUID' }] },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(provisionUserFn).not.toHaveBeenCalled();
      expect(syncPermissionsFn).toHaveBeenCalledWith({
        user: {
          id: 'mockUserId',
          gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
          advocates: [
            { id: 'mockCoreAdminId', coreUserId: 'mockCoreUserId', partnerId: 'mockPartnerId' },
          ],
        },
        partnerId: 'mockOtherCorePartnerId',
        tenantId: 'app-mock-123',
        gcipUid: 'mockGCIPUID',
        sessionId: 'mockSessionId',
      });
    });
    it('should set the user email to verified if using an email token', async () => {
      const provisionUserFn = vi.fn();
      const userFindFn = vi.fn();
      const adminVerifyEmailFn = vi.fn();
      const identityUserUpdateFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              updateUser: vi.fn(),
              verifyToken: vi
                .fn()
                .mockResolvedValueOnce({
                  uid: 'mockGCIPUID',
                  email: '<EMAIL>',
                  email_verified: false,
                })
                .mockResolvedValueOnce({
                  uid: 'mockGCIPUID',
                  email: '<EMAIL>',
                  email_verified: false,
                }),
              verifyEmail: adminVerifyEmailFn.mockResolvedValueOnce('success'),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService)
            return {
              syncPermissions: vi.fn().mockResolvedValue('success'),
            };
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return { update: vi.fn(), exist: vi.fn() };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return { save: vi.fn() };
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              update: identityUserUpdateFn.mockResolvedValueOnce('success'),
              findOne: userFindFn
                // user found
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                  applicants: [{ id: 'mockCoreUserId', partnerId: 'mockPartnerId' }],
                })
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  verifiedEmail: false,
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                  applicants: [],
                })
                // lookup during email verification
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  verifiedEmail: false,
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                  applicants: [],
                })
                // lookup after all permissions and verification
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  verifiedEmail: true,
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                  applicants: [],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByToken({
        authenticationMechanism: 'EMAIL_TOKEN',
        partnerId: 'mockPartnerId',
        userId: 'mockGCIPUID',
        tenantId: 'adv-mock-123',
        token: 'mockToken',
        headers: '{"test": "something"}',
        sessionId: 'mockSessionId',
        _userId: 'userId',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [{ tenantId: 'adv-mock-123', uid: 'mockGCIPUID' }],
        advocates: [
          { coreUserId: 'mockCoreUserId', id: 'mockCoreAdminId', partnerId: 'mockPartnerId' },
        ],
        applicants: [],
        verifiedEmail: true,
      });
      expect(adminVerifyEmailFn).toHaveBeenCalledWith('mockGCIPUID', 'adv-mock-123');
      expect(identityUserUpdateFn).toHaveBeenCalledWith(
        { id: 'mockUserId' },
        { verifiedEmail: true },
      );
    });
    it('should provision a new advocate user if the token is valid SAML matching the partner configuration and there are available seats', async () => {
      const provisionUserFn = vi.fn();
      const userFindFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({
                uid: 'mockGCIPUID',
                email: '<EMAIL>',
                email_verified: true,
                firebase: { sign_in_provider: 'saml.mock' },
              }),
            };

          if (token === AuthorizationService) return {};
          if (token === ConfigService)
            return {
              get: vi
                .fn()
                .mockReturnValueOnce('beam-employees-mock')
                .mockReturnValueOnce('saml.beam'),
            };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockPartnerId',
                config: {
                  identity: { advocate: { saml: { providerId: 'saml.mock', seats: 10 } } },
                },
              }),
            };
          if (token === 'IDENTITY_AdvocateEntityRepository')
            return { findAndCount: vi.fn().mockResolvedValueOnce([{}, 5]) };
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              update: vi.fn(),
              findOne: userFindFn
                // no user found
                .mockResolvedValueOnce(null)
                // lookup after provision and permission setting
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByToken({
        authenticationMechanism: 'SAML',
        partnerId: 'mockPartnerId',
        userId: 'mockGCIPUID',
        tenantId: 'adv-mock-123',
        token: 'mockToken',
        headers: '{"test": "something"}',
        sessionId: 'mockSessionId',
        _userId: 'userId',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [{ tenantId: 'adv-mock-123', uid: 'mockGCIPUID' }],
        advocates: [
          { coreUserId: 'mockCoreUserId', id: 'mockCoreAdminId', partnerId: 'mockPartnerId' },
        ],
      });
      expect(userFindFn).toHaveBeenCalledWith({
        where: { gcipUsers: [{ uid: 'mockGCIPUID' }] },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(provisionUserFn).toHaveBeenCalledWith({
        user: { email: '<EMAIL>', name: '<EMAIL>', verifiedEmail: true },
        gcipUid: 'mockGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'adv-mock-123',
        sessionId: 'mockSessionId',
        sudoRequest: true,
      });
    });
    it('should throw a descriptive error if the SAML token does not match the partner configuration', async () => {
      const provisionUserFn = vi.fn();
      const userFindFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({
                uid: 'mockGCIPUID',
                email: '<EMAIL>',
                email_verified: true,
                firebase: { sign_in_provider: 'saml.mock' },
              }),
            };

          if (token === AuthorizationService) return {};
          if (token === ConfigService)
            return {
              get: vi
                .fn()
                .mockReturnValueOnce('beam-employees-mock')
                .mockReturnValueOnce('saml.beam'),
            };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockPartnerId',
                config: {
                  identity: { advocate: { saml: { providerId: 'saml.mockSomethingElse' } } },
                },
              }),
            };
          if (token === 'IDENTITY_AdvocateEntityRepository')
            return { findAndCount: vi.fn().mockResolvedValueOnce([{}, 0]) };
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              update: vi.fn(),
              findOne: userFindFn
                // no user found
                .mockResolvedValueOnce(null)
                // lookup after provision and permission setting
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                }),
            };
        })
        .compile();

      await expect(
        module.get<UserService>(UserService).validateByToken({
          authenticationMechanism: 'SAML',
          partnerId: 'mockPartnerId',
          userId: 'mockGCIPUID',
          tenantId: 'adv-mock-123',
          token: 'mockToken',
          headers: '{"test": "something"}',
          sessionId: 'mockSessionId',
          _userId: 'userId',
        }),
      ).rejects.toThrow(
        "the SAML assertion has been deemed invalid, configured provider: 'saml.mockSomethingElse' signin provider: 'saml.mock'",
      );

      expect(provisionUserFn).not.toHaveBeenCalled();
    });
    it('should throw a descriptive error if seat limit is reached', async () => {
      const provisionUserFn = vi.fn();
      const userFindFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({
                uid: 'mockGCIPUID',
                email: '<EMAIL>',
                email_verified: true,
                firebase: { sign_in_provider: 'saml.mock' },
              }),
            };

          if (token === AuthorizationService) return {};
          if (token === ConfigService)
            return {
              get: vi
                .fn()
                .mockReturnValueOnce('beam-employees-mock')
                .mockReturnValueOnce('saml.beam'),
            };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockPartnerId',
                config: {
                  identity: { advocate: { saml: { providerId: 'saml.mock', seats: 10 } } },
                },
              }),
            };
          if (token === 'IDENTITY_AdvocateEntityRepository')
            return { findAndCount: vi.fn().mockResolvedValueOnce([{}, 10]) };
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              update: vi.fn(),
              findOne: userFindFn
                // no user found
                .mockResolvedValueOnce(null)
                // lookup after provision and permission setting
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                }),
            };
        })
        .compile();

      await expect(
        module.get<UserService>(UserService).validateByToken({
          authenticationMechanism: 'SAML',
          partnerId: 'mockPartnerId',
          userId: 'mockGCIPUID',
          tenantId: 'adv-mock-123',
          token: 'mockToken',
          headers: '{"test": "something"}',
          sessionId: 'mockSessionId',
          _userId: 'userId',
        }),
      ).rejects.toThrow('exceeded available seats, 10 of 10 are filled');

      expect(provisionUserFn).not.toHaveBeenCalled();
    });
    it('should allow the user creation despite seat limits if a beam employee', async () => {
      const provisionUserFn = vi.fn();
      const userFindFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({
                uid: 'mockGCIPUID',
                email: '<EMAIL>',
                email_verified: true,
                firebase: { sign_in_provider: 'saml.beam' },
              }),
            };

          if (token === AuthorizationService) return {};
          if (token === ConfigService)
            return {
              get: vi
                .fn()
                .mockReturnValueOnce('beam-employees-mock')
                .mockReturnValueOnce('saml.beam'),
            };
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValueOnce({
                id: 'mockPartnerId',
                config: {
                  identity: { advocate: { saml: { providerId: 'saml.mock', seats: 10 } } },
                },
              }),
            };
          if (token === 'IDENTITY_AdvocateEntityRepository')
            return { findAndCount: vi.fn().mockResolvedValueOnce([{}, 10]) };
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              update: vi.fn(),
              findOne: userFindFn
                // no user found
                .mockResolvedValueOnce(null)
                // lookup after provision and permission setting
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                  advocates: [
                    {
                      id: 'mockCoreAdminId',
                      coreUserId: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByToken({
        authenticationMechanism: 'SAML',
        partnerId: 'mockPartnerId',
        userId: 'mockGCIPUID',
        tenantId: 'beam-employees-mock',
        token: 'mockToken',
        headers: '{"test": "something"}',
        sessionId: 'mockSessionId',
        _userId: 'userId',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [{ tenantId: 'adv-mock-123', uid: 'mockGCIPUID' }],
        advocates: [
          { coreUserId: 'mockCoreUserId', id: 'mockCoreAdminId', partnerId: 'mockPartnerId' },
        ],
      });
      expect(userFindFn).toHaveBeenCalledWith({
        where: { gcipUsers: [{ uid: 'mockGCIPUID' }] },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(provisionUserFn).toHaveBeenCalledWith({
        user: { email: '<EMAIL>', name: '<EMAIL>', verifiedEmail: true },
        gcipUid: 'mockGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'beam-employees-mock',
        sessionId: 'mockSessionId',
        sudoRequest: true,
      });
    });
    it('should provision an anonymous user if the authentication mechanism is anonymous', async () => {
      const provisionUserFn = vi.fn();
      const userFindFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              verifyToken: vi.fn().mockResolvedValueOnce({
                provider_id: 'anonymous',
                auth_time: **********,
                user_id: 'mockGCIPUID',
                firebase: {
                  identities: {},
                  sign_in_provider: 'anonymous',
                  tenant: 'app-local-c5615',
                },
                iat: **********,
                exp: **********,
                aud: 'core-platform-local-beam',
                iss: 'https://securetoken.google.com/core-platform-local-beam',
                sub: 'mockGCIPUID',
                uid: 'mockGCIPUID',
              }),
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService)
            return { provisionUser: provisionUserFn.mockResolvedValueOnce('success') };
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              findOne: userFindFn
                .mockResolvedValueOnce(null)
                // lookup after provision and permission setting
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'mockTenantId' }],
                  applicants: [
                    {
                      id: 'mockCoreUserId',
                      partnerId: 'mockPartnerId',
                    },
                  ],
                }),
            };
        })
        .compile();

      const result = await module.get<UserService>(UserService).validateByToken({
        authenticationMechanism: 'ANONYMOUS',
        partnerId: 'mockPartnerId',
        userId: 'mockGCIPUID',
        tenantId: 'mockTenantId',
        token: 'mockToken',
        headers: '{"test": "something"}',
        sessionId: 'mockSessionId',
        _userId: 'userId',
      });

      expect(result).toEqual({
        id: 'mockUserId',
        gcipUsers: [{ tenantId: 'mockTenantId', uid: 'mockGCIPUID' }],
        applicants: [
          {
            id: 'mockCoreUserId',
            partnerId: 'mockPartnerId',
          },
        ],
      });
      expect(provisionUserFn).toHaveBeenCalledWith({
        user: {
          email: '<EMAIL>',
          name: 'Anonymous 74657374',
        },
        gcipUid: 'mockGCIPUID',
        partnerId: 'mockPartnerId',
        tenantId: 'mockTenantId',
        sessionId: 'mockSessionId',
        sudoRequest: false,
      });
    });
  });
  describe('updateUser', () => {
    it('throws NOT FOUND if no user is found with the provided identityUserId', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService) return { updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return { findOne: findUserFn.mockResolvedValueOnce(null), update: updateUserFn };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.updateUser({
          email: '<EMAIL>',
          name: 'Jonjo Shelvey',
          phone: '+**********',
          userIdentifier: 'identityUserId',
          identityUserId: 'mockIdentityUserId',
          partnerId: 'mockPartnerId',
        } as UpdateUserRequest),
      ).rejects.toThrow('user not found with id {"identityUserId":"mockIdentityUserId"}');
    });
    it('throws NOT FOUND if no user is found with the provided coreUserId', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService) return { updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return { findOne: findUserFn.mockResolvedValueOnce(null), update: updateUserFn };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.updateUser({
          email: '<EMAIL>',
          name: 'Jonjo Shelvey',
          phone: '+**********',
          coreUserId: 'mockCoreUserId',
          userIdentifier: 'coreUserId',
          partnerId: 'mockPartnerId',
        } as UpdateUserRequest),
      ).rejects.toThrow('user not found with id {"coreUserId":"mockCoreUserId"}');
    });
    it('leaves the phone out of the update for identity db if it already exists in the user table', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return { createToken: vi.fn().mockResolvedValueOnce('mockToken'), updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              update: vi.fn(),
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(true),
              findOne: findUserFn.mockResolvedValueOnce({
                id: 'mockUserId',
                gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                phone: '+**********',
                applicants: [{ id: 'mockCoreUserId' }],
                advocates: [],
              }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.updateUser({
        email: '<EMAIL>',
        name: 'Jonjo Shelvey',
        phone: '+**********',
        identityUserId: 'mockIdentityUserId',
        userIdentifier: 'identityUserId',
        partnerId: 'mockPartnerId',
      } as UpdateUserRequest);

      expect(findUserFn).toHaveBeenCalledWith({
        where: { id: 'mockIdentityUserId' },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(updateUserFn).toHaveBeenCalledWith('mockUserId', {
        email: '<EMAIL>',
        verifiedEmail: false,
        name: 'Jonjo Shelvey',
        phone: '+**********',
      });
    });
    it('finds the user if only coreId is provided', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return { createToken: vi.fn().mockResolvedValueOnce('mockToken'), updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              update: vi.fn(),
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              findOne: findUserFn
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                })
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  email: '<EMAIL>',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                  applicants: [{ id: 'mockCoreUserId' }],
                  advocates: [],
                }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.updateUser({
        email: '<EMAIL>',
        name: 'Jonjo Shelvey',
        phone: '+**********',
        coreUserId: 'mockCoreUserId',
        userIdentifier: 'coreUserId',
        partnerId: 'mockPartnerId',
      } as UpdateUserRequest);

      expect(findUserFn).toHaveBeenCalledWith({
        where: [
          { applicants: { id: 'mockCoreUserId' } },
          { advocates: { coreUserId: 'mockCoreUserId' } },
        ],
      });
      expect(findUserFn).toHaveBeenCalledWith({
        where: { id: 'mockUserId' },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(updateUserFn).toHaveBeenCalledWith('mockUserId', {
        name: 'Jonjo Shelvey',
        phone: '+**********',
      });
    });
    it('updates applicants if they are found', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return { createToken: vi.fn().mockResolvedValueOnce('mockToken'), updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              update: updateCoreUserFn,
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              findOne: findUserFn.mockResolvedValueOnce({
                id: 'mockUserId',
                email: '<EMAIL>',
                gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                applicants: [{ id: 'mockCoreUserId', userId: 'mockUserId' }],
                advocates: [],
              }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.updateUser({
        email: '<EMAIL>',
        name: 'Jonjo Shelvey',
        phone: '+**********',
        identityUserId: 'mockIdentityUserId',
        userIdentifier: 'identityUserId',
        partnerId: 'mockPartnerId',
      } as UpdateUserRequest);

      expect(findUserFn).toHaveBeenCalledWith({
        where: { id: 'mockIdentityUserId' },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(updateUserFn).toHaveBeenCalledWith('mockUserId', {
        name: 'Jonjo Shelvey',
        phone: '+**********',
      });
      expect(updateCoreUserFn).toHaveBeenCalledWith(
        { id: In(['mockCoreUserId']) },
        {
          name: 'Jonjo Shelvey',
          phone: '+**********',
        },
      );
    });
    it('updates case workers if they are found', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return { createToken: vi.fn().mockResolvedValueOnce('mockToken'), updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              update: updateCoreUserFn,
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              findOne: findUserFn.mockResolvedValueOnce({
                id: 'mockUserId',
                email: '<EMAIL>',
                gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                applicants: [],
                advocates: [{ coreUserId: 'mockCoreUserId' }],
              }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.updateUser({
        email: '<EMAIL>',
        name: 'Jonjo Shelvey',
        phone: '+**********',
        identityUserId: 'mockIdentityUserId',
        userIdentifier: 'identityUserId',
        partnerId: 'mockPartnerId',
      } as UpdateUserRequest);

      expect(findUserFn).toHaveBeenCalledWith({
        where: { id: 'mockIdentityUserId' },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(updateUserFn).toHaveBeenCalledWith('mockUserId', {
        name: 'Jonjo Shelvey',
        phone: '+**********',
      });
      expect(updateCoreUserFn).toHaveBeenCalledWith(
        { id: In(['mockCoreUserId']) },
        {
          name: 'Jonjo Shelvey',
          phone: '+**********',
        },
      );
    });
    it('updates gcip users if the ID is provided', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const updateGcipUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              createToken: vi.fn().mockResolvedValueOnce('mockToken'),
              updateUser: updateGcipUserFn,
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              update: updateCoreUserFn,
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              findOne: findUserFn.mockResolvedValueOnce({
                id: 'mockUserId',
                email: '<EMAIL>',
                gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                applicants: [],
                advocates: [{ coreUserId: 'mockCoreUserId' }],
              }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.updateUser({
        email: '<EMAIL>',
        name: 'Jonjo Shelvey',
        phone: '+**********',
        identityUserId: 'mockIdentityUserId',
        userIdentifier: 'identityUserId',
        partnerId: 'mockPartnerId',
      } as UpdateUserRequest);

      expect(findUserFn).toHaveBeenCalledWith({
        where: { id: 'mockIdentityUserId' },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(updateUserFn).toHaveBeenCalledWith('mockUserId', {
        name: 'Jonjo Shelvey',
        phone: '+**********',
      });
      expect(updateCoreUserFn).toHaveBeenCalledWith(
        { id: In(['mockCoreUserId']) },
        {
          name: 'Jonjo Shelvey',
          phone: '+**********',
        },
      );
      expect(updateGcipUserFn).toHaveBeenCalledWith(
        { name: 'Jonjo Shelvey', phone: '+**********' },
        'adv-mock-123',
        'mockGCIPUID',
      );
    });
    it('throw an error if email already exists', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const updateCoreUserFn = vi.fn();
      const updateGcipUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              createToken: vi.fn().mockResolvedValueOnce('mockToken'),
              updateUser: updateGcipUserFn,
            };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(true),
              update: updateCoreUserFn,
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              findOne: findUserFn.mockResolvedValueOnce({
                id: 'mockUserId',
                gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                applicants: [],
                advocates: [{ coreUserId: 'mockCoreUserId' }],
              }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.updateUser({
          email: '<EMAIL>',
          name: 'Jonjo Shelvey',
          phone: '+**********',
          identityUserId: 'mockIdentityUserId',
          userIdentifier: 'identityUserId',
          partnerId: 'mockPartnerId',
        } as UpdateUserRequest),
      ).rejects.toThrow();

      expect(findUserFn).toHaveBeenCalledWith({
        where: { id: 'mockIdentityUserId' },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(updateUserFn).not.toHaveBeenCalled();
      expect(updateCoreUserFn).not.toHaveBeenCalled();
      expect(updateGcipUserFn).not.toHaveBeenCalled();
    });
    it('should revert everything if one of updates failed', async () => {
      const updateUserFn = vi.fn();
      const findUserFn = vi.fn();
      const updateCoreUserFn = vi.fn().mockRejectedValueOnce(new Error('Server ERROR'));
      const updateGcipUserFn = vi.fn();
      const createRelationshipsFn = vi.fn();
      const deleteRelationshipsFn = vi.fn();
      const getRolesFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return {
              createToken: vi.fn().mockResolvedValueOnce('mockToken'),
              updateUser: updateGcipUserFn,
            };
          if (token === AuthorizationService)
            return {
              createRelationships: createRelationshipsFn,
              deleteRelationships: deleteRelationshipsFn,
            };
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService)
            return {
              getRoles: getRolesFn,
            };
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return { findOne: vi.fn() };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              update: updateCoreUserFn,
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              findOne: findUserFn.mockResolvedValueOnce({
                id: 'mockUserId',
                email: '<EMAIL>',
                name: 'Jonjo Test',
                phone: null,
                gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'adv-mock-123' }],
                applicants: [],
                advocates: [{ coreUserId: 'mockCoreUserId' }],
              }),
              update: updateUserFn,
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.updateUser({
          email: '<EMAIL>',
          name: 'Jonjo Shelvey',
          phone: '+**********',
          identityUserId: 'mockIdentityUserId',
          userIdentifier: 'identityUserId',
          partnerId: 'mockPartnerId',
          roles: ['manager'],
        } as UpdateUserRequest),
      ).rejects.toThrow();

      expect(findUserFn).toHaveBeenCalledWith({
        where: { id: 'mockIdentityUserId' },
        relations: ['applicants', 'advocates', 'gcipUsers'],
      });
      expect(updateUserFn).toHaveBeenCalledTimes(2);
      expect(updateGcipUserFn).toHaveBeenCalledTimes(2);
      expect(updateCoreUserFn).toHaveBeenCalledTimes(1);
      expect(updateUserFn).toHaveBeenNthCalledWith(2, 'mockUserId', {
        email: '<EMAIL>',
        name: 'Jonjo Test',
        phone: null,
      });
      expect(updateGcipUserFn).toHaveBeenNthCalledWith(
        2,
        {
          email: '<EMAIL>',
          name: 'Jonjo Test',
          phone: null,
        },
        'adv-mock-123',
        'mockGCIPUID',
      );
      expect(getRolesFn).not.toHaveBeenCalled();
      expect(createRelationshipsFn).not.toHaveBeenCalled();
      expect(deleteRelationshipsFn).not.toHaveBeenCalled();
    });
    it('should send user info for validation if `User: Validation` feature is enabled', async () => {
      const validateUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService') return { info: vi.fn(), warn: vi.fn() };
          if (token === AdminService)
            return { createToken: vi.fn().mockResolvedValueOnce('mockToken'), updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: validateUserFn };
          if (token === 'CORE_PartnerEntityRepository')
            return {
              findOne: vi.fn().mockResolvedValue({
                id: 'mockPartnerId',
                externalId: 'mockExternalId',
                features: [{ enabled: true, feature: { name: FeatureName.UserValidation } }],
              }),
            };
          if (token === 'CORE_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              update: vi.fn(),
            };
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return {
              exist: vi.fn().mockResolvedValueOnce(false),
              findOne: vi
                .fn()
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                })
                .mockResolvedValueOnce({
                  id: 'mockUserId',
                  email: '<EMAIL>',
                  gcipUsers: [{ uid: 'mockGCIPUID', tenantId: 'app-mock-123' }],
                  applicants: [{ id: 'mockCoreUserId' }],
                  advocates: [],
                }),
              update: vi.fn(),
            };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await service.updateUser({
        email: '<EMAIL>',
        name: 'Jonjo Shelvey',
        phone: '+**********',
        coreUserId: 'mockCoreUserId',
        userIdentifier: 'coreUserId',
        partnerId: 'mockPartnerId',
      } as UpdateUserRequest);

      expect(validateUserFn).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'mockUserId',
          email: '<EMAIL>',
          phone: '+**********',
        }),
      );
    });
  });
  describe('readPortalRoles', () => {
    it('throws NOT FOUND if no user is found with the provided coreUserId', async () => {
      const getRolesFn = vi.fn();
      const findUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService')
            return { debug: vi.fn(), info: vi.fn(), warn: vi.fn() };
          if (token === AdminService) return { updateUser: vi.fn() };
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return { getRoles: getRolesFn };
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return { findOne: findUserFn.mockResolvedValueOnce(null) };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      await expect(
        service.readPortalRoles({
          partnerId: 'mockPartnerId',
          userIdentifier: 'coreUserId',
          coreUserId: 'mockCoreUserId',
        }),
      ).rejects.toThrow('user not found with core user id mockCoreUserId');
    });
    it('returns the roles if the user is found with the core userId', async () => {
      const getRolesFn = vi.fn().mockResolvedValueOnce([Role.Manager]);
      const findUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService')
            return { debug: vi.fn(), info: vi.fn(), warn: vi.fn() };
          if (token === AdminService) return { updateUser: vi.fn() };
          if (token === AuthorizationService) return { getPartnerPortalRoles: getRolesFn };
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return { findOne: findUserFn.mockResolvedValueOnce({ id: 'mockIdentityUserId' }) };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      const result = await service.readPortalRoles({
        partnerId: 'mockPartnerId',
        userIdentifier: 'coreUserId',
        coreUserId: 'mockCoreUserId',
      });
      expect(result).toEqual({
        message:
          'roles retrieved successfully for user:mockIdentityUserId and partner:mockPartnerId',
        roles: [Role.Manager],
      });
      expect(getRolesFn).toHaveBeenCalledWith('mockIdentityUserId', 'mockPartnerId');
    });
    it('returns the roles if the user is found with the identity userId', async () => {
      const getRolesFn = vi.fn().mockResolvedValueOnce([Role.Manager]);
      const findUserFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService')
            return { debug: vi.fn(), info: vi.fn(), warn: vi.fn() };
          if (token === AdminService) return { updateUser: vi.fn() };
          if (token === AuthorizationService) return { getPartnerPortalRoles: getRolesFn };
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository') return {};
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository')
            return { findOne: findUserFn.mockResolvedValueOnce({ id: 'mockIdentityUserId' }) };
        })
        .compile();

      const service = module.get<UserService>(UserService);
      const result = await service.readPortalRoles({
        partnerId: 'mockPartnerId',
        userIdentifier: 'identityUserId',
        identityUserId: 'mockIdentityUserId',
      });
      expect(result).toEqual({
        message:
          'roles retrieved successfully for user:mockIdentityUserId and partner:mockPartnerId',
        roles: [Role.Manager],
      });
      expect(getRolesFn).toHaveBeenCalledWith('mockIdentityUserId', 'mockPartnerId');
    });
  });
  describe('getAdmins', () => {
    it('should return a list of users with their core user id and the total count', async () => {
      const findAndCountFn = vi.fn();
      const module: TestingModule = await Test.createTestingModule({
        providers: [UserService],
      })
        .useMocker((token) => {
          if (token === 'PinoLogger:UserService')
            return { debug: vi.fn(), info: vi.fn(), warn: vi.fn() };
          if (token === AdminService) return {};
          if (token === AuthorizationService) return {};
          if (token === ConfigService) return {};
          if (token === NotificationService) return {};
          if (token === PermissionsService) return {};
          if (token === ProvisionService) return {};
          if (token === VerificationService) return { validateUser: vi.fn() };
          if (token === 'CORE_PartnerEntityRepository') return {};
          if (token === 'CORE_UserEntityRepository') return {};
          if (token === 'IDENTITY_AdvocateEntityRepository')
            return {
              findAndCount: findAndCountFn.mockResolvedValueOnce([
                [
                  { coreUserId: 'coreUserA', user: { id: 'identityIdA' } },
                  { coreUserId: 'coreUserB', user: { id: 'identityIdB' } },
                  { coreUserId: 'coreUserC', user: { id: 'identityIdC' } },
                ],
                3,
              ]),
            };
          if (token === 'IDENTITY_AuditLogEntityRepository') return {};
          if (token === 'IDENTITY_UserEntityRepository') return {};
        })
        .compile();

      const service = module.get<UserService>(UserService);
      const result = await service.getAdmins({
        ids: ['coreUserA', 'coreUserB', 'coreUserC'],
      });
      expect(result).toEqual({
        admins: [
          {
            id: 'identityIdA',
            coreUserId: 'coreUserA',
            _deactivatedAt: 'deactivatedAt',
            _phone: 'phone',
            _updatedAt: 'updatedAt',
            _zedToken: 'zedToken',
          },
          {
            id: 'identityIdB',
            coreUserId: 'coreUserB',
            _deactivatedAt: 'deactivatedAt',
            _phone: 'phone',
            _updatedAt: 'updatedAt',
            _zedToken: 'zedToken',
          },
          {
            id: 'identityIdC',
            coreUserId: 'coreUserC',
            _deactivatedAt: 'deactivatedAt',
            _phone: 'phone',
            _updatedAt: 'updatedAt',
            _zedToken: 'zedToken',
          },
        ],
        count: 3,
      });
      expect(findAndCountFn).toHaveBeenCalledWith({
        where: { coreUserId: In(['coreUserA', 'coreUserB', 'coreUserC']) },
        relations: ['user'],
      });
    });
  });
});
