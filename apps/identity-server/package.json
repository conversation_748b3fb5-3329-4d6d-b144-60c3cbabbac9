{"name": "@bybeam/identity-server", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "nest build", "emulator:seed": "nest build && node --loader ts-node/esm scripts/seedEmulator.ts", "nest": "nest", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "vitest"}, "devDependencies": {"@bybeam/typescript-config": "workspace:^", "@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/node": "catalog:default", "@types/uuid": "catalog:default", "@vitest/coverage-v8": "catalog:default", "mockdate": "catalog:default", "source-map-support": "0.5.21", "ts-loader": "^9.5.1", "ts-node": "catalog:default", "tsconfig-paths": "catalog:default", "typescript": "catalog:default", "unplugin-swc": "^1.5.8", "vitest": "catalog:default"}, "dependencies": {"@authzed/authzed-node": "^0.15.0", "@bybeam/formatting": "workspace:^", "@bybeam/identity-client": "workspace:^", "@bybeam/identity-entities": "workspace:^", "@bybeam/infrastructure-lib": "workspace:^", "@bybeam/notification-client": "workspace:^", "@bybeam/platform-entities": "workspace:^", "@bybeam/platform-lib": "workspace:^", "@bybeam/platform-types": "workspace:^", "@bybeam/verification-client": "workspace:^", "@google-cloud/recaptcha-enterprise": "5.1.0", "@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "@nestjs/common": "^11.0.12", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.6", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^11.0.0", "argon2": "^0.44.0", "dayjs": "1.11.19", "firebase-admin": "catalog:default", "grpc-health-check": "^2.0.0", "nestjs-grpc-reflection": "^0.2.2", "nestjs-pino": "^3.5.0", "pg": "8.13.1", "reflect-metadata": "0.1.14", "rxjs": "^7.8.1", "typeorm": "catalog:default"}}