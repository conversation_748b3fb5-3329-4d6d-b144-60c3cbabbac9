import { AuthenticationMechanism } from '@bybeam/identity-client/types';
import { User as CoreUser } from '@bybeam/platform-types';
import { FirebaseError } from 'firebase/app';
import { Auth, User as GCIPUser } from 'firebase/auth';

interface User {
  gcip: GCIPUser;
  core: CoreUser;
}

interface AuthState {
  user?: User;
  loading: boolean;
  resetAuthState: () => Promise<void>;
  identity: IdentityState;
  hasFullProfile: boolean;
}

type Status = 'success' | 'error';

interface IdentityState {
  auth: Auth;
  signinMethods: {
    anonymousAuth: () => Promise<void>;
    basicAuth: (input: {
      email: string;
      password: string;
    }) => Promise<void>;
    beamEmployeeSAML(): Promise<never>;
    emailLinkAuth({ email, link }: { email: string; link: string }): Promise<void>;
    redirectListener: () => Promise<void>;
    resetPassword: (input: { currentPassword: string; password: string }) => Promise<{
      status: Status;
      authMechanism: AuthenticationMechanism;
      code?: string;
    }>;
    tokenAuth(token: string): Promise<void>;
  };
  loading: boolean;
  coreUser: CoreUser;
  refreshUser: () => Promise<void>;
  tenantId: string;
}

// biome-ignore lint/suspicious/noExplicitAny: could be any error
function isFirebaseAuthError(error: any): error is FirebaseError {
  if (!error) return false;
  return (error as FirebaseError)?.code?.startsWith('auth/');
}

export { isFirebaseAuthError };
export type { AuthState, IdentityState, User };
