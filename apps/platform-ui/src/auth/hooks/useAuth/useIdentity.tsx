import { useLazyQuery, useMutation } from '@apollo/client';
import { AuthenticationMechanism } from '@bybeam/identity-client/types';
import { BeginSessionInput, MutationResponse } from '@bybeam/platform-types';
import { User as PlatformUser } from '@bybeam/platform-types';
import usePartner from '@platform-ui-common/hooks/contexts/usePartner.js';
import { StorageKeys } from '@platform-ui/@types/storage.js';
import env from '@platform-ui/utilities/env.js';
import {
  SAMLAuthProvider,
  UserCredential,
  getRedirectResult,
  signInAnonymously,
  signInWithCustomToken,
  signInWithEmailAndPassword,
  signInWithEmailLink,
  signInWithRedirect,
  updatePassword,
} from 'firebase/auth';
import { usePostHog } from 'posthog-js/react';
import { useEffect, useMemo } from 'react';
import BeginSession from '../../graphql/BeginSessionMutation.graphql';
import GetPlatformUserQuery from '../../graphql/GetPlatformUser.graphql';
import useTenant from '../useTenant.js';
import { auth } from './firebase.js';
import { IdentityState, isFirebaseAuthError } from './types.js';

function useIdentity(): IdentityState {
  const { tenantId } = useTenant();
  const partner = usePartner();
  const posthog = usePostHog();

  if (auth) auth.tenantId = tenantId;

  const [doBeginSession, _] = useMutation<
    { identity: { beginSession: MutationResponse } },
    { input: BeginSessionInput }
  >(BeginSession);

  const [fetchCoreUser, coreUserStatus] = useLazyQuery<{ currentUser: PlatformUser }>(
    GetPlatformUserQuery,
    { fetchPolicy: 'cache-first' },
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: intentionally running only on first render
  useEffect(() => {
    if (auth?.currentUser) fetchCoreUser();
  }, []);

  useEffect(() => {
    const { loading, data, error } = coreUserStatus;
    if (auth?.currentUser && !loading && !error && !data?.currentUser) fetchCoreUser();
  }, [coreUserStatus, fetchCoreUser]);

  useEffect(() => {
    if (partner) {
      posthog.group('org', partner.id, { externalId: partner.externalId, name: partner.name });
    }
  });

  async function beginSession(
    result: UserCredential,
    authenticationMechanism: AuthenticationMechanism,
  ) {
    if (!result.user || !auth.currentUser) throw new Error('No user found');
    const token = await result.user.getIdToken();
    await doBeginSession({
      variables: {
        input: {
          partnerId: partner.id,
          tenantId,
          token,
          authenticationMechanism,
          userId: '', // deprecated, will remove
        },
      },
    });
    await auth.currentUser.getIdToken(true);
    await fetchCoreUser();
    posthog.identify(auth.currentUser.uid, {
      email: auth.currentUser.email,
      name: auth.currentUser.displayName,
      tenantId: auth.currentUser.tenantId,
      organization: partner.externalId,
    });
  }

  async function basicAuth({ email, password }: { email: string; password: string }) {
    auth.tenantId = tenantId;
    return signInWithEmailAndPassword(auth, email, password).then(
      async (result) => await beginSession(result, 'BASIC'),
    );
  }

  async function anonymousAuth() {
    auth.tenantId = tenantId;
    return signInAnonymously(auth).then(async (result) => await beginSession(result, 'ANONYMOUS'));
  }

  async function tokenAuth(token: string) {
    auth.tenantId = tenantId;
    return signInWithCustomToken(auth, token).then(
      async (result) => await beginSession(result, 'EMAIL_TOKEN'),
    );
  }

  async function emailLinkAuth({ email, link }: { email: string; link: string }) {
    auth.tenantId = tenantId;
    return signInWithEmailLink(auth, email, link).then(
      async (result) => await beginSession(result, 'EMAIL_TOKEN'),
    );
  }

  async function beamEmployeeSAML() {
    const { REACT_APP_IDENTITY_BEAM_TENANT: employeeTenantId } = env();
    if (!employeeTenantId) throw new Error('REACT_APP_IDENTITY_BEAM_TENANT not set');
    auth.tenantId = employeeTenantId;
    const provider = new SAMLAuthProvider('saml.okta');
    localStorage.setItem(StorageKeys.SAMLRedirect, 'true');
    localStorage.setItem(StorageKeys.AuthMechanism, 'SAML');
    return signInWithRedirect(auth, provider);
  }

  async function redirectListener() {
    return getRedirectResult(auth).then((result) => beginSession(result, 'SAML'));
  }

  async function refreshUser() {
    await auth.currentUser?.getIdToken(true);
    return;
  }

  async function resetPassword({
    currentPassword,
    password,
  }: { currentPassword: string; password: string }) {
    return updatePassword(auth?.currentUser, password)
      .then(() => ({ status: 'success' }))
      .catch((error) => {
        const canAutoAuthenticate =
          isFirebaseAuthError(error) &&
          error.code === 'auth/requires-recent-login' &&
          !!currentPassword;
        if (canAutoAuthenticate) {
          return signInWithEmailAndPassword(auth, auth.currentUser.email, currentPassword).then(
            () => resetPassword({ currentPassword, password }),
          );
        }
        return { status: 'error', code: error.code };
      });
  }

  const signinMethods = {
    anonymousAuth,
    basicAuth,
    beamEmployeeSAML,
    emailLinkAuth,
    redirectListener,
    resetPassword,
    tokenAuth,
  };

  const loading = coreUserStatus.loading;
  const coreUser = coreUserStatus?.data?.currentUser;

  // biome-ignore lint/correctness/useExhaustiveDependencies: this is causing all kinds of issues
  return useMemo(
    () => ({
      auth,
      loading,
      signinMethods,
      refreshUser,
      coreUser,
      tenantId,
    }),
    [coreUser, loading, tenantId],
  );
}

export { useIdentity };
