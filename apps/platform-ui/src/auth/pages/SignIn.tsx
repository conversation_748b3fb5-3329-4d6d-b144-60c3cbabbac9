import { useMutation } from '@apollo/client';
import { FeatureName, MutationResponse, SendMagicLinkInput } from '@bybeam/platform-types';
import ErrorDisplay from '@platform-ui-common/components/ErrorDisplay/index.js';
import FormContainer from '@platform-ui-common/components/FormContainer/index.js';
import Loading from '@platform-ui-common/components/Loading/index.js';
import TextInput from '@platform-ui-common/components/TextInput/index.js';
import usePartner from '@platform-ui-common/hooks/contexts/usePartner.js';
import useForm, {
  ValidationMode,
  emailField,
  fieldUpdate,
} from '@platform-ui-common/hooks/useForm/index.js';
import useAuth from '@platform-ui/auth/hooks/useAuth/index.js';
import Button from '@platform-ui/common/components/Button/index.js';
import { useUserHomeRoute } from '@platform-ui/utilities/Routes.js';
import { checkPartnerHasFeature } from '@platform-ui/utilities/checkFeature.js';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom-v5-compat';
import { IdentityPaths } from '../Routes.js';
import SendMagicLinkV2 from '../graphql/SendMagicLinkV2Mutation.graphql';
import { getTenantConfig } from '../utils/getTenantConfig.js';

function SignIn(): JSX.Element {
  const partner = usePartner();
  const navigate = useNavigate();
  const userHome = useUserHomeRoute();

  const [error, setError] = useState(null);
  const [authLoading, setAuthLoading] = useState(false);

  const { dispatch, formData, errors, trySubmit } = useForm(
    { email: sessionStorage.getItem('signinEmail') ?? '' },
    {
      email: emailField(),
    },
    ValidationMode.OnSubmit,
  );

  const {
    identity: { tenantId, auth, signinMethods },
    resetAuthState,
    user,
  } = useAuth();

  const { basic, emailSent, roleRedirect, title } = getTenantConfig(partner);
  const hidePasswordSignin = checkPartnerHasFeature(
    partner,
    FeatureName.AuthenticationHidePassword,
  );
  const hidePartnerPortalRedirect = checkPartnerHasFeature(
    partner,
    FeatureName.AuthenticationHidePartnerRedirect,
  );
  const signInAnonymouslyEnabled = checkPartnerHasFeature(
    partner,
    FeatureName.AuthenticationAnonymous,
  );

  const [sendLink, mutationResults] = useMutation<
    { identity: { sendMagicLinkV2: MutationResponse } },
    { input: SendMagicLinkInput }
  >(SendMagicLinkV2);

  // biome-ignore lint/correctness/useExhaustiveDependencies: intentionally running only on first render
  useEffect(() => {
    if (!auth?.currentUser) resetAuthState();
  }, []);

  useEffect(() => {
    const { called, loading, data } = mutationResults;
    if (called && !loading) {
      setAuthLoading(false);
      if (data?.identity?.sendMagicLinkV2?.metadata?.status === 200) {
        navigate(emailSent, { state: { email } });
      } else {
        setError('Error sending magic link');
      }
    }
  });

  useEffect(() => {
    if (user?.gcip && !user?.core) setAuthLoading(true);
    if (user?.core && user?.gcip) {
      navigate(userHome);
      setAuthLoading(false);
    }
  });

  useEffect(() => {
    if (formData?.email) localStorage.setItem('signinEmail', formData.email);
  }, [formData]);

  const { email } = formData;
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const submit = () =>
    trySubmit(async () => {
      try {
        setAuthLoading(true);
        await sendLink({
          variables: {
            input: {
              email,
              partnerId: partner.id,
              tenantId,
              timezone,
              continueUrl: `${window.location.origin}/${partner.externalId}${IdentityPaths.MAGIC_LINK}`,
            },
          },
        });
      } catch (e) {
        setError(e);
        setAuthLoading(false);
      }
    });

  const handleAnonymousSignIn = async () => {
    try {
      setAuthLoading(true);
      setError(null);
      await signinMethods.anonymousAuth();
    } catch (e) {
      setError(e);
      setAuthLoading(false);
    }
  };

  const loading = authLoading || mutationResults?.loading;

  if (loading) {
    return <Loading size="XXL" fullPage />;
  }

  return (
    <FormContainer
      title={title}
      onSubmit={submit}
      applyMinHeight={false}
      buttons={
        <div className="flex flex-col gap-8">
          <div className="w-full flex flex-col gap-y-8 items-center text-center">
            <ErrorDisplay errors={error} visible={!!error} message="Error sending magic link" />
            <Button loading={loading || authLoading} type="submit">
              Send Me a Link
            </Button>
            {!hidePasswordSignin ? (
              <Button loading={loading || authLoading} variant="outline" link={basic}>
                Use a Password
              </Button>
            ) : null}
            {signInAnonymouslyEnabled ? (
              <Button
                loading={loading || authLoading}
                variant="outline"
                onClick={handleAnonymousSignIn}
                type="button"
              >
                Sign In Anonymously
              </Button>
            ) : null}
          </div>
        </div>
      }
    >
      <TextInput
        id="email"
        label="Email Address"
        onChange={(value): void => dispatch(fieldUpdate('email', value))}
        value={email}
        error={errors.email as string}
        type="email"
        autoComplete="email"
        disabled={loading}
        required
      />
      {hidePartnerPortalRedirect ? null : roleRedirect}
    </FormContainer>
  );
}

export default SignIn;
