import {
  CreateDocumentationTemplateInput,
  DocumentationTemplate,
  Partner,
  Program,
} from '@bybeam/platform-types';
import { composeResolvers } from '@graphql-tools/resolvers-composition';
import { IResolvers } from '@graphql-tools/utils';
import { GraphQLFieldResolver } from 'graphql';
import { AdminContext, AuthenticatedContext } from '../../../@types/graphql.js';
import IsAdmin from '../../../resolvers/composition/IsAdmin.js';

type DocumentationTemplateFieldResolver<TContext, TReturn> = GraphQLFieldResolver<
  DocumentationTemplate,
  TContext,
  Record<string, never>,
  TReturn
>;

type DocumentationTemplateMutations = {
  create: GraphQLFieldResolver<void, AdminContext, { input: CreateDocumentationTemplateInput }>;
};

type DocumentationTemplateResolvers = IResolvers<DocumentationTemplate, AuthenticatedContext> & {
  Query: {
    documentationTemplates: GraphQLFieldResolver<
      void,
      AuthenticatedContext,
      Record<string, never>,
      Promise<DocumentationTemplate[]>
    >;
  };
  Mutation: {
    documentationTemplate: () => { _type: 'DocumentationTemplateMutations' };
  };
  DocumentationTemplate: {
    partner: DocumentationTemplateFieldResolver<AuthenticatedContext, Promise<Partner>>;
    program: DocumentationTemplateFieldResolver<AuthenticatedContext, Promise<Program | null>>;
  };
  DocumentationTemplateMutations: DocumentationTemplateMutations;
};

const documentationTemplateResolvers: DocumentationTemplateResolvers = {
  Query: {
    documentationTemplates: async (_, __, context) => {
      return context.services.documentationTemplates.findByPartnerId(context.token.partnerId);
    },
  },
  Mutation: {
    documentationTemplate: () => ({ _type: 'DocumentationTemplateMutations' }),
  },
  DocumentationTemplate: {
    partner: async (template, _, context) =>
      template.partner ?? context.services.partners.findById(template.partnerId),
    program: async (template, _, context) =>
      template.program ??
      (template.programId ? context.services.programs.findById(template.programId) : null),
  },
  DocumentationTemplateMutations: {
    create: async (_, { input }, context) =>
      context.services.documentationTemplates.create(context.token, input),
  },
};

const resolverComposition = {
  'Query.documentationTemplates': [IsAdmin()],
  'DocumentationTemplateMutations.create': [IsAdmin()],
};

export default composeResolvers(documentationTemplateResolvers, resolverComposition);
