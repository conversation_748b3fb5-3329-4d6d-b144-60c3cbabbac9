import { CreateDocumentationTemplateInput, DocumentationTemplate } from '@bybeam/platform-types';
import { composeResolvers } from '@graphql-tools/resolvers-composition';
import { IResolvers } from '@graphql-tools/utils';
import IsAdmin from '../../../resolvers/composition/IsAdmin.js';
import { GraphQLFieldResolver } from 'graphql';
import { AuthenticatedContext } from '../../../@types/graphql.js';
import type { GqlResolvers as Resolvers, GqlQueryCaseArgs as QueryCaseArgs, GqlResolvedEntities } from '../../../graphql/__generated__/resolvers-types.js';


const documentationResolvers: Resolvers = {
  Query: {
    documentationTemplates: async (_, __, context) => {
      return context.services.documentationTemplates.findByPartnerId(context.token.partnerId);
    },
  },
}

documentationResolvers.Mutation = {
  re: () => ({ _type: 'DocumentationTemplateMutations' }),
};

const resolverComposition = {
  'Query.documentationTemplates': [IsAdmin()],
  'DocumentationTemplateMutations.create': [IsAdmin()],
};

export default composeResolvers(documentationTemplateResolvers, resolverComposition);
