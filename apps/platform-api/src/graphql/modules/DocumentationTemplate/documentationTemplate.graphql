type DocumentationTemplate {
  id: UUID!
  name: NonEmptyString!
  description: String
  documentKey: NonEmptyString!
  partnerId: UUID!
  programId: UUID
  fieldMappings: JSON!
  createdAt: DateTime!
  updatedAt: DateTime
  deactivatedAt: DateTime
  partner: Partner!
  program: Program
}

input CreateDocumentationTemplateInput {
  name: NonEmptyString!
  description: String
  file: Upload!
  programId: UUID
  fieldMappings: JSON!
}

type DocumentationTemplateMutationResponse {
  metadata: ResponseMetadata!
  record: DocumentationTemplate
}

type DocumentationTemplateMutations {
  create(input: CreateDocumentationTemplateInput!): DocumentationTemplateMutationResponse!
}

extend type Query {
  documentationTemplates: [DocumentationTemplate!]!
}

extend type Mutation {
  documentationTemplate: DocumentationTemplateMutations
}
