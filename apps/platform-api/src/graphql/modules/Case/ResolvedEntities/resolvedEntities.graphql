type ResolvedEntities {
    caseId: UUID!
    applicantNameConsistency: ApplicantNameConsistency!
}

type ApplicantNameConsistency {
    status: ResolverStatus!
    result: ApplicantNameConsistencyResult
}

type ApplicantNameConsistencyResult {
    canonicalName: String!
    overallSimilarity: Similarity!
    documents: [DocumentConsistency!]!
}

enum ResolverStatus {
    PENDING
    COMPLETED
    FAILED
}

enum Similarity {
    EXACT
    SIMILAR
    NOT_SIMILAR
    UNKNOWN
}

type DocumentConsistency {
    documentId: UUID!
    similarity: Similarity!
    bestMatchedName: String!
}