export type ResolvedEntities = {
  caseId: string;
  applicantNameConsistency?: ApplicantNameConsistency | null;
};

export type ApplicantNameConsistencyResult = {
  canonicalName: string;
  overallSimilarity: Similarity;
  documents: DocumentConsistency[];
};

export type ApplicantNameConsistency = {
  status: ResolverStatus;
  result?: ApplicantNameConsistencyResult | null;
};

export type DocumentConsistency = {
  documentId: string;
  similarity: Similarity;
  bestMatchedName: string;
};

export enum Similarity {
  EXACT = 'EXACT',
  SIMILAR = 'SIMILAR',
  NOT_SIMILAR = 'NOT_SIMILAR',
  UNKNOWN = 'UNKNOWN',
}

export enum ResolverStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}
