import type { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
import type { Case, TagAutomation } from '@bybeam/platform-types';
import type { AuthenticatedContext } from '../../@types/graphql.js';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  AccountNumber: { input: string; output: string; }
  AssigneeId: { input: string; output: string; }
  BigInt: { input: bigint; output: bigint; }
  Byte: { input: unknown; output: unknown; }
  CountryCode: { input: unknown; output: unknown; }
  Cuid: { input: unknown; output: unknown; }
  Currency: { input: unknown; output: unknown; }
  DID: { input: unknown; output: unknown; }
  Date: { input: string; output: string; }
  DateTime: { input: string; output: string; }
  DateTimeISO: { input: unknown; output: unknown; }
  DeweyDecimal: { input: unknown; output: unknown; }
  Duration: { input: unknown; output: unknown; }
  EmailAddress: { input: string; output: string; }
  GUID: { input: unknown; output: unknown; }
  HSL: { input: unknown; output: unknown; }
  HSLA: { input: unknown; output: unknown; }
  HexColorCode: { input: unknown; output: unknown; }
  Hexadecimal: { input: unknown; output: unknown; }
  IBAN: { input: unknown; output: unknown; }
  IP: { input: unknown; output: unknown; }
  IPCPatent: { input: unknown; output: unknown; }
  IPv4: { input: unknown; output: unknown; }
  IPv6: { input: unknown; output: unknown; }
  ISBN: { input: unknown; output: unknown; }
  ISO8601Duration: { input: unknown; output: unknown; }
  JSON: { input: unknown; output: unknown; }
  JSONObject: { input: unknown; output: unknown; }
  JWT: { input: string; output: string; }
  LCCSubclass: { input: unknown; output: unknown; }
  Latitude: { input: number; output: number; }
  LocalDate: { input: unknown; output: unknown; }
  LocalDateTime: { input: unknown; output: unknown; }
  LocalEndTime: { input: unknown; output: unknown; }
  LocalTime: { input: unknown; output: unknown; }
  Locale: { input: unknown; output: unknown; }
  Long: { input: unknown; output: unknown; }
  Longitude: { input: number; output: number; }
  MAC: { input: unknown; output: unknown; }
  NegativeFloat: { input: unknown; output: unknown; }
  NegativeInt: { input: unknown; output: unknown; }
  NonEmptyString: { input: string; output: string; }
  NonNegativeFloat: { input: unknown; output: unknown; }
  NonNegativeInt: { input: number; output: number; }
  NonPositiveFloat: { input: unknown; output: unknown; }
  NonPositiveInt: { input: unknown; output: unknown; }
  ObjectID: { input: unknown; output: unknown; }
  Password: { input: unknown; output: unknown; }
  PhoneNumber: { input: string; output: string; }
  Port: { input: unknown; output: unknown; }
  PositiveFloat: { input: unknown; output: unknown; }
  PositiveInt: { input: number; output: number; }
  PostalCode: { input: unknown; output: unknown; }
  Primitive: { input: unknown; output: unknown; }
  RGB: { input: unknown; output: unknown; }
  RGBA: { input: unknown; output: unknown; }
  RoutingNumber: { input: string; output: string; }
  SESSN: { input: unknown; output: unknown; }
  SafeInt: { input: unknown; output: unknown; }
  SemVer: { input: unknown; output: unknown; }
  Time: { input: unknown; output: unknown; }
  TimeZone: { input: unknown; output: unknown; }
  Timestamp: { input: unknown; output: unknown; }
  URL: { input: string; output: string; }
  USCurrency: { input: unknown; output: unknown; }
  UUID: { input: string; output: string; }
  UnsignedFloat: { input: unknown; output: unknown; }
  UnsignedInt: { input: unknown; output: unknown; }
  Upload: { input: unknown; output: unknown; }
  UtcOffset: { input: unknown; output: unknown; }
  Void: { input: unknown; output: unknown; }
};

export type GqlAccessRequest = {
  __typename?: 'AccessRequest';
  approved: Scalars['Boolean']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  partnerId: Scalars['UUID']['output'];
  relation: Scalars['NonEmptyString']['output'];
  requestDetail?: Maybe<Scalars['String']['output']>;
  requester: GqlIdentityUser;
  resourceId: Scalars['UUID']['output'];
  resourceType: Scalars['NonEmptyString']['output'];
  reviewDetail?: Maybe<Scalars['String']['output']>;
  reviewer?: Maybe<GqlIdentityUser>;
  subjectId: Scalars['UUID']['output'];
  subjectType: Scalars['NonEmptyString']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GqlAccessRequestFilter = {
  partnerId: Scalars['UUID']['input'];
  relation?: InputMaybe<Scalars['NonEmptyString']['input']>;
  resource?: InputMaybe<GqlObjectReference>;
  subject?: InputMaybe<GqlObjectReference>;
};

export type GqlAccessRequestMutationResponse = {
  __typename?: 'AccessRequestMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlAccessRequest>;
};

export type GqlAccessRequestMutations = {
  __typename?: 'AccessRequestMutations';
  createAccessRequest: GqlAccessRequestMutationResponse;
  reviewAccessRequest: GqlAccessRequestMutationResponse;
};


export type GqlAccessRequestMutationsCreateAccessRequestArgs = {
  input: GqlCreateAccessRequestInput;
};


export type GqlAccessRequestMutationsReviewAccessRequestArgs = {
  input: GqlReviewAccessRequestInput;
};

export type GqlAccessRequestPage = {
  __typename?: 'AccessRequestPage';
  accessRequests: Array<GqlAccessRequest>;
  pageInfo: GqlPageInfo;
};

export enum GqlAccountType {
  Checking = 'checking',
  Savings = 'savings'
}

export type GqlActiveLabel = {
  __typename?: 'ActiveLabel';
  createdAt: Scalars['DateTime']['output'];
  displayName?: Maybe<Scalars['NonEmptyString']['output']>;
  id: Scalars['UUID']['output'];
  /** @deprecated This field is no longer in use. There is no direct replacement. */
  modelVersion?: Maybe<GqlModelVersion>;
  name: Scalars['NonEmptyString']['output'];
};

export type GqlAddApplicationVersionInput = {
  content: GqlApplicationContentInput;
  description?: InputMaybe<Scalars['NonEmptyString']['input']>;
  id: Scalars['UUID']['input'];
  versionId?: InputMaybe<Scalars['UUID']['input']>;
};

export type GqlAddBulkCommentInput = {
  content: Scalars['NonEmptyString']['input'];
  ids: Array<Scalars['UUID']['input']>;
};

export type GqlAddCommentInput = {
  caseId: Scalars['UUID']['input'];
  content: Scalars['NonEmptyString']['input'];
  parentCommentId?: InputMaybe<Scalars['UUID']['input']>;
};

export type GqlAddNoteInput = {
  content: Scalars['NonEmptyString']['input'];
  relationId: Scalars['UUID']['input'];
  relationType: GqlNoteRelationType;
};

export type GqlAddNoteToApplicationAnswerInput = {
  applicationAnswerId: Scalars['ID']['input'];
  content: Scalars['String']['input'];
};

export type GqlAddNoteToApplicationAnswerPayload = {
  __typename?: 'AddNoteToApplicationAnswerPayload';
  applicationAnswer?: Maybe<GqlApplicationAnswer>;
  applicationAnswerNote?: Maybe<GqlApplicationAnswerNote>;
  message?: Maybe<Scalars['String']['output']>;
  note?: Maybe<GqlNote>;
  success: Scalars['Boolean']['output'];
};

export type GqlAddProgramFundInput = {
  fundId: Scalars['UUID']['input'];
  programId: Scalars['UUID']['input'];
};

export type GqlAddReviewToApplicationAnswerInput = {
  applicationAnswerId: Scalars['ID']['input'];
  content: Scalars['String']['input'];
  reviewStatus: GqlApplicationAnswerReviewStatus;
};

export type GqlAddReviewToApplicationAnswerPayload = {
  __typename?: 'AddReviewToApplicationAnswerPayload';
  applicationAnswer?: Maybe<GqlApplicationAnswer>;
  applicationAnswerReview?: Maybe<GqlApplicationAnswerReview>;
  message?: Maybe<Scalars['String']['output']>;
  note?: Maybe<GqlNote>;
  success: Scalars['Boolean']['output'];
};

export type GqlAddress = {
  __typename?: 'Address';
  addressLine1?: Maybe<Scalars['String']['output']>;
  addressLine2?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  incomeLimitArea?: Maybe<GqlIncomeLimitArea>;
  latitude?: Maybe<Scalars['Latitude']['output']>;
  longitude?: Maybe<Scalars['Longitude']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export type GqlAddressField = {
  __typename?: 'AddressField';
  allowUnhoused?: Maybe<Scalars['Boolean']['output']>;
  copy?: Maybe<Scalars['JSON']['output']>;
  copyAddressKey?: Maybe<Scalars['NonEmptyString']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  filter?: Maybe<GqlAddressFieldFilter>;
  key: Scalars['NonEmptyString']['output'];
  type: GqlApplicationFieldType;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlAddressFieldFilter = {
  __typename?: 'AddressFieldFilter';
  city?: Maybe<Scalars['NonEmptyString']['output']>;
  state: Scalars['NonEmptyString']['output'];
};

export type GqlAddressFilter = {
  id: Scalars['UUID']['input'];
};

export type GqlAddressPage = {
  __typename?: 'AddressPage';
  addresses: Array<GqlAddress>;
  pageInfo: GqlPageInfo;
};

export type GqlAdmin = {
  __typename?: 'Admin';
  archivedAt?: Maybe<Scalars['DateTime']['output']>;
  assignments: Array<GqlAssignment>;
  id: Scalars['UUID']['output'];
  identityUser: GqlIdentityUser;
  roles: Array<GqlPortalRole>;
  /** @deprecated use identityUser instead */
  user: GqlUser;
};

export type GqlAdminMutations = {
  __typename?: 'AdminMutations';
  create: GqlAdminResponse;
  delete: GqlAdminResponse;
  update: GqlAdminResponse;
};


export type GqlAdminMutationsCreateArgs = {
  input: GqlCreateAdminInput;
};


export type GqlAdminMutationsDeleteArgs = {
  input: GqlDeleteAdminInput;
};


export type GqlAdminMutationsUpdateArgs = {
  input: GqlUpdateAdminInput;
};

export type GqlAdminPage = {
  __typename?: 'AdminPage';
  nodes: Array<GqlAdmin>;
  pageInfo: GqlPageInfo;
};

export type GqlAdminResponse = {
  __typename?: 'AdminResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
};

export type GqlAdvocateIdentityConfig = {
  __typename?: 'AdvocateIdentityConfig';
  saml?: Maybe<GqlSamlConfig>;
  tenantId: Scalars['NonEmptyString']['output'];
};

export type GqlAggregateApplicantsReferred = {
  __typename?: 'AggregateApplicantsReferred';
  count: Scalars['NonNegativeInt']['output'];
};

export type GqlAggregateApplication = {
  __typename?: 'AggregateApplication';
  count: Scalars['Int']['output'];
};

export type GqlAggregateApplicationFilter = {
  createdAt?: InputMaybe<GqlDateRange>;
  programId?: InputMaybe<Scalars['UUID']['input']>;
  submittedAt?: InputMaybe<GqlDateRange>;
};

export type GqlAggregatePayment = {
  __typename?: 'AggregatePayment';
  count: Scalars['NonNegativeInt']['output'];
  sum: Scalars['NonNegativeInt']['output'];
};

export type GqlAggregatePaymentFilter = {
  completedAt?: InputMaybe<GqlDateRange>;
  initiatedAt?: InputMaybe<GqlDateRange>;
  programId?: InputMaybe<Scalars['UUID']['input']>;
  status?: InputMaybe<Array<GqlPaymentStatus>>;
};

export type GqlAnalyticsResource = {
  __typename?: 'AnalyticsResource';
  dashboardId?: Maybe<Scalars['NonEmptyString']['output']>;
  displayInPortal: Scalars['Boolean']['output'];
  id: Scalars['UUID']['output'];
  name: Scalars['NonEmptyString']['output'];
  url: Scalars['URL']['output'];
  workspaceId?: Maybe<Scalars['NonEmptyString']['output']>;
};

export type GqlApplicantIdentityConfig = {
  __typename?: 'ApplicantIdentityConfig';
  tenantId: Scalars['NonEmptyString']['output'];
};

export type GqlApplicantNameConsistency = {
  __typename?: 'ApplicantNameConsistency';
  result?: Maybe<GqlApplicantNameConsistencyResult>;
  status: GqlResolverStatus;
};

export type GqlApplicantNameConsistencyResult = {
  __typename?: 'ApplicantNameConsistencyResult';
  canonicalName: Scalars['String']['output'];
  documents: Array<GqlDocumentConsistency>;
  overallSimilarity: GqlSimilarity;
};

export type GqlApplicantProfile = {
  __typename?: 'ApplicantProfile';
  addresses: Array<GqlAddress>;
  answers: Scalars['JSON']['output'];
  applicantType?: Maybe<GqlApplicantType>;
  createdAt: Scalars['DateTime']['output'];
  documents: Array<GqlDocument>;
  id: Scalars['UUID']['output'];
  mailingAddress?: Maybe<GqlAddress>;
  notes: Array<GqlNote>;
  secondaryEmail?: Maybe<Scalars['String']['output']>;
  secondaryPhone?: Maybe<Scalars['String']['output']>;
  user: GqlUser;
};

export type GqlApplicantProfileConfig = {
  __typename?: 'ApplicantProfileConfig';
  profileKeys?: Maybe<Array<GqlProfileKey>>;
};

export type GqlApplicantProfileConfiguration = {
  __typename?: 'ApplicantProfileConfiguration';
  applicantTypeId: Scalars['String']['output'];
  config?: Maybe<GqlApplicantProfileConfig>;
  partnerId: Scalars['String']['output'];
};

export type GqlApplicantRolesConfig = {
  __typename?: 'ApplicantRolesConfig';
  FIRST_PARTY?: Maybe<GqlRoleConfig>;
  THIRD_PARTY?: Maybe<GqlRoleConfig>;
};

export type GqlApplicantType = {
  __typename?: 'ApplicantType';
  id: Scalars['UUID']['output'];
  name: Scalars['String']['output'];
  role: GqlApplicantTypeRole;
};

export enum GqlApplicantTypeRole {
  FirstParty = 'FIRST_PARTY',
  ThirdParty = 'THIRD_PARTY'
}

export type GqlApplication = {
  __typename?: 'Application';
  addresses: Array<GqlAddress>;
  answers: Scalars['JSON']['output'];
  case: GqlCase;
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  displayId: Scalars['String']['output'];
  documents: Array<GqlDocument>;
  id: Scalars['UUID']['output'];
  linkAttempts: Array<GqlLinkAttempt>;
  referral?: Maybe<GqlProgramReferral>;
  score?: Maybe<GqlApplicationScore>;
  submittedAt?: Maybe<Scalars['DateTime']['output']>;
  submitter: GqlUser;
  updatedAt: Scalars['DateTime']['output'];
  verification?: Maybe<GqlApplicationVerification>;
  versions: Array<GqlApplicationVersion>;
};

export type GqlApplicationAddressUpdate = {
  address?: InputMaybe<GqlCreateAddressInput>;
  copyKey?: InputMaybe<Scalars['NonEmptyString']['input']>;
  key: Scalars['NonEmptyString']['input'];
};

export type GqlApplicationAnswer = {
  __typename?: 'ApplicationAnswer';
  applicationAnswerNotes: Array<GqlApplicationAnswerNote>;
  fieldReviews: Array<GqlApplicationAnswerReview>;
  fieldReviewsRequiringResubmission: Array<GqlApplicationAnswerReview>;
  id: Scalars['String']['output'];
  key?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['JSON']['output']>;
};

export type GqlApplicationAnswerNote = {
  __typename?: 'ApplicationAnswerNote';
  applicationAnswer: GqlApplicationAnswer;
  applicationAnswerId: Scalars['String']['output'];
  caseManagerId: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  note: GqlNote;
  noteId: Scalars['String']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GqlApplicationAnswerReview = {
  __typename?: 'ApplicationAnswerReview';
  applicationAnswerId: Scalars['String']['output'];
  caseManagerId: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['String']['output'];
  note?: Maybe<GqlNote>;
  noteId?: Maybe<Scalars['String']['output']>;
  reviewStatus: GqlApplicationAnswerReviewStatus;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export enum GqlApplicationAnswerReviewStatus {
  DoesNotMeetRequirements = 'DOES_NOT_MEET_REQUIREMENTS',
  MeetsRequirements = 'MEETS_REQUIREMENTS',
  NeedsFurtherReview = 'NEEDS_FURTHER_REVIEW',
  NeedsResubmission = 'NEEDS_RESUBMISSION',
  NoteOnly = 'NOTE_ONLY'
}

export type GqlApplicationConfigOverrides = {
  __typename?: 'ApplicationConfigOverrides';
  statuses?: Maybe<Array<GqlStatusOverride>>;
  submission?: Maybe<GqlSubmissionOverrides>;
};

export type GqlApplicationConfigReviewFields = {
  __typename?: 'ApplicationConfigReviewFields';
  appConfigId: Scalars['UUID']['output'];
  fields: Array<Scalars['String']['output']>;
};

export type GqlApplicationConfigReviewFieldsInput = {
  appConfigId: Scalars['UUID']['input'];
  fields?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type GqlApplicationConfiguration = {
  __typename?: 'ApplicationConfiguration';
  introPages: Array<GqlIntroPage>;
  overrides?: Maybe<GqlApplicationConfigOverrides>;
  sections: Array<GqlApplicationSection>;
};

export type GqlApplicationContentInput = {
  addresses: Array<GqlApplicationAddressUpdate>;
  answers: Scalars['JSON']['input'];
  documents: Array<GqlApplicationDocumentUpdate>;
};

export type GqlApplicationDocumentUpdate = {
  files: Array<Scalars['Upload']['input']>;
  key: Scalars['NonEmptyString']['input'];
};

export type GqlApplicationField = GqlAddressField | GqlCalculatedField | GqlCheckboxField | GqlCheckboxGroupField | GqlComplexField | GqlDateField | GqlDocumentField | GqlDropdownField | GqlRadioListField | GqlTextField | GqlTypographyField;

export enum GqlApplicationFieldType {
  Address = 'address',
  Calculated = 'calculated',
  Checkbox = 'checkbox',
  CheckboxGroup = 'checkboxGroup',
  Complex = 'complex',
  Date = 'date',
  Document = 'document',
  Dropdown = 'dropdown',
  RadioList = 'radioList',
  Text = 'text',
  Typography = 'typography'
}

export type GqlApplicationFieldValidation = {
  __typename?: 'ApplicationFieldValidation';
  condition?: Maybe<Scalars['JSON']['output']>;
  required: Scalars['Boolean']['output'];
  rules?: Maybe<Array<GqlApplicationFieldValidationRule>>;
};

export type GqlApplicationFieldValidationRule = {
  __typename?: 'ApplicationFieldValidationRule';
  message: Scalars['NonEmptyString']['output'];
  rule: Scalars['JSON']['output'];
};

export type GqlApplicationFilter = {
  id?: InputMaybe<Scalars['UUID']['input']>;
  programId?: InputMaybe<Scalars['UUID']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  submitterId?: InputMaybe<Scalars['UUID']['input']>;
};

export type GqlApplicationMutationResponse = {
  __typename?: 'ApplicationMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlApplication>;
};

export type GqlApplicationMutations = {
  __typename?: 'ApplicationMutations';
  addVersion: GqlApplicationMutationResponse;
  create: GqlApplicationMutationResponse;
  evaluateEligibility: GqlApplicationMutationResponse;
  submit: GqlApplicationMutationResponse;
  update: GqlApplicationMutationResponse;
};


export type GqlApplicationMutationsAddVersionArgs = {
  input: GqlAddApplicationVersionInput;
};


export type GqlApplicationMutationsCreateArgs = {
  input: GqlCreateApplicationInput;
};


export type GqlApplicationMutationsEvaluateEligibilityArgs = {
  input: GqlSubmitApplicationInput;
};


export type GqlApplicationMutationsSubmitArgs = {
  input: GqlSubmitApplicationInput;
};


export type GqlApplicationMutationsUpdateArgs = {
  input: GqlUpdateApplicationInput;
};

export type GqlApplicationPage = {
  __typename?: 'ApplicationPage';
  applications: Array<GqlApplication>;
  pageInfo: GqlPageInfo;
};

export type GqlApplicationQuestion = {
  __typename?: 'ApplicationQuestion';
  copy: GqlApplicationQuestionCopy;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  fields: Array<GqlApplicationField>;
  key: Scalars['NonEmptyString']['output'];
  layout?: Maybe<GqlApplicationQuestionLayout>;
  skippable?: Maybe<Scalars['Boolean']['output']>;
};

export type GqlApplicationQuestionCopy = {
  __typename?: 'ApplicationQuestionCopy';
  intro?: Maybe<Scalars['NonEmptyString']['output']>;
  title: Scalars['NonEmptyString']['output'];
};

export type GqlApplicationQuestionGroup = {
  __typename?: 'ApplicationQuestionGroup';
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  name: Scalars['NonEmptyString']['output'];
  overview?: Maybe<GqlApplicationQuestionGroupOverview>;
  questions: Array<GqlApplicationQuestion>;
};

export type GqlApplicationQuestionGroupOverview = {
  __typename?: 'ApplicationQuestionGroupOverview';
  description: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export enum GqlApplicationQuestionLayout {
  Default = 'default',
  Panel = 'panel'
}

export type GqlApplicationScore = {
  __typename?: 'ApplicationScore';
  algorithmVersion: Scalars['NonNegativeInt']['output'];
  applicationVersionId: Scalars['UUID']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  score: Scalars['NonNegativeInt']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlApplicationSection = {
  __typename?: 'ApplicationSection';
  key: Scalars['NonEmptyString']['output'];
  name: Scalars['NonEmptyString']['output'];
  overview: GqlApplicationSectionOverview;
  questionGroups: Array<GqlApplicationQuestionGroup>;
};

export type GqlApplicationSectionOverview = {
  __typename?: 'ApplicationSectionOverview';
  description: Scalars['String']['output'];
  title: Scalars['NonEmptyString']['output'];
};

export enum GqlApplicationStatus {
  Application = 'Application',
  Approved = 'Approved',
  AwaitingReview = 'AwaitingReview',
  Denied = 'Denied',
  InProgress = 'InProgress',
  InReview = 'InReview',
  Incomplete = 'Incomplete',
  Unknown = 'Unknown',
  WithdrawnClosed = 'WithdrawnClosed',
  WithdrawnOpen = 'WithdrawnOpen'
}

export type GqlApplicationSubmission = {
  __typename?: 'ApplicationSubmission';
  addresses?: Maybe<Array<GqlAddress>>;
  createdAt: Scalars['DateTime']['output'];
  currentVersion: GqlApplicationVersion;
  displayId: Scalars['String']['output'];
  documents: Array<GqlDocument>;
  id: Scalars['UUID']['output'];
  latestSubmission: GqlApplicationVersion;
  submitter: GqlUser;
  updatedAt: Scalars['DateTime']['output'];
  versions: Array<GqlApplicationVersion>;
};

export type GqlApplicationVerification = {
  __typename?: 'ApplicationVerification';
  confidence: Scalars['Float']['output'];
  createdAt: Scalars['DateTime']['output'];
  details?: Maybe<Array<GqlVerificationDetail>>;
  id: Scalars['UUID']['output'];
  metadata?: Maybe<Array<GqlVerificationMetadata>>;
  /** @deprecated Replaced with confidence */
  score?: Maybe<Scalars['Float']['output']>;
  service?: Maybe<Scalars['String']['output']>;
};

export type GqlApplicationVersion = {
  __typename?: 'ApplicationVersion';
  answers: Scalars['JSON']['output'];
  applicationId: Scalars['UUID']['output'];
  createdAt: Scalars['DateTime']['output'];
  creator: GqlUser;
  creatorId: Scalars['UUID']['output'];
  description?: Maybe<Scalars['String']['output']>;
  eligibility?: Maybe<Scalars['String']['output']>;
  eligibilityReason?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  lastEvaluatedAt?: Maybe<Scalars['DateTime']['output']>;
  mappedAnswers: Array<GqlSectionResponse>;
  updatedAt: Scalars['DateTime']['output'];
  verification?: Maybe<GqlApplicationVerification>;
  versionScore?: Maybe<Scalars['Int']['output']>;
};

export type GqlApproveAmountInput = {
  id: Scalars['UUID']['input'];
};

export type GqlApproveCasePaymentsInput = {
  fulfillmentIds?: InputMaybe<Array<Scalars['UUID']['input']>>;
  ids: Array<Scalars['UUID']['input']>;
};

export type GqlAssignToCaseInput = {
  assigneeId?: InputMaybe<Scalars['UUID']['input']>;
  ids: Array<Scalars['UUID']['input']>;
};

export type GqlAssignment = {
  __typename?: 'Assignment';
  assigneeId: Scalars['UUID']['output'];
  case?: Maybe<GqlCase>;
  caseId: Scalars['UUID']['output'];
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['UUID']['output'];
};

export type GqlBankAccount = {
  __typename?: 'BankAccount';
  accountNumber: Scalars['NonEmptyString']['output'];
  accountType: GqlAccountType;
  routingNumber: Scalars['RoutingNumber']['output'];
};

export type GqlBankAccountInput = {
  accountNumber: Scalars['AccountNumber']['input'];
  accountType: GqlAccountType;
  routingNumber: Scalars['RoutingNumber']['input'];
};

export type GqlBeginSessionInput = {
  authenticationMechanism: Scalars['NonEmptyString']['input'];
  partnerId: Scalars['UUID']['input'];
  tenantId: Scalars['NonEmptyString']['input'];
  token: Scalars['JWT']['input'];
  /** @deprecated User extracted from token */
  userId?: InputMaybe<Scalars['String']['input']>;
};

export enum GqlBenefitType {
  Other = 'Other',
  Rental = 'Rental',
  Utility = 'Utility'
}

export type GqlBulkCaseMutationResponse = {
  __typename?: 'BulkCaseMutationResponse';
  metadata: GqlBulkResponseMetadata;
  query: GqlQuery;
  records?: Maybe<Array<GqlCase>>;
};

export type GqlBulkFulfillmentResponse = {
  __typename?: 'BulkFulfillmentResponse';
  metadata: GqlBulkResponseMetadata;
  records?: Maybe<Array<GqlFulfillment>>;
};

export type GqlBulkIssueFundsInput = {
  ids: Array<Scalars['UUID']['input']>;
};

export type GqlBulkResponseError = {
  __typename?: 'BulkResponseError';
  id?: Maybe<Scalars['String']['output']>;
  message: Scalars['NonEmptyString']['output'];
};

export type GqlBulkResponseMetadata = {
  __typename?: 'BulkResponseMetadata';
  errors?: Maybe<Array<GqlBulkResponseError>>;
  ids?: Maybe<Array<Scalars['UUID']['output']>>;
  message?: Maybe<Scalars['NonEmptyString']['output']>;
  status: Scalars['NonNegativeInt']['output'];
};

export type GqlBulkUserMutationResponse = {
  __typename?: 'BulkUserMutationResponse';
  metadata: GqlBulkResponseMetadata;
  query: GqlQuery;
  records?: Maybe<Array<GqlUser>>;
};

export enum GqlButtonVariant {
  Outline = 'outline',
  Solid = 'solid'
}

export type GqlCalculatedField = {
  __typename?: 'CalculatedField';
  display?: Maybe<GqlCalculatedFieldDisplay>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  formula: Scalars['JSON']['output'];
  key: Scalars['NonEmptyString']['output'];
  type: GqlApplicationFieldType;
};

export type GqlCalculatedFieldDisplay = {
  __typename?: 'CalculatedFieldDisplay';
  copy?: Maybe<Scalars['JSON']['output']>;
  inputType?: Maybe<Scalars['NonEmptyString']['output']>;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlCase = {
  __typename?: 'Case';
  applications: Array<GqlApplication>;
  assignee?: Maybe<GqlAdmin>;
  caseTags: Array<GqlCaseTag>;
  comments?: Maybe<Array<GqlComment>>;
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  decisionReachedAt?: Maybe<Scalars['DateTime']['output']>;
  displayId: Scalars['String']['output'];
  documents: Array<GqlDocument>;
  fulfillments: Array<GqlFulfillment>;
  id: Scalars['UUID']['output'];
  metadata: GqlCaseMetadata;
  name: Scalars['NonEmptyString']['output'];
  notes: Array<GqlNote>;
  participants: Array<GqlCaseParticipant>;
  priority: Scalars['NonNegativeInt']['output'];
  program: GqlProgram;
  resolvedEntities: GqlResolvedEntities;
  status: GqlCaseStatus;
  statusUpdatedAt?: Maybe<Scalars['DateTime']['output']>;
  workflowEvents: Array<GqlWorkflowEvent>;
};

export type GqlCaseApplicationAnswer = {
  __typename?: 'CaseApplicationAnswer';
  key: Scalars['NonEmptyString']['output'];
  value: Scalars['JSON']['output'];
};

export type GqlCaseApplicationsDocument = {
  __typename?: 'CaseApplicationsDocument';
  data: GqlCasesApplicationsSource;
  id: Scalars['ID']['output'];
  index: Scalars['NonEmptyString']['output'];
};

export type GqlCaseCounts = {
  __typename?: 'CaseCounts';
  All?: Maybe<Scalars['Int']['output']>;
  Approved?: Maybe<Scalars['Int']['output']>;
  Archived?: Maybe<Scalars['Int']['output']>;
  Denied?: Maybe<Scalars['Int']['output']>;
  FiscalReview?: Maybe<Scalars['Int']['output']>;
  InProgress?: Maybe<Scalars['Int']['output']>;
  InReview?: Maybe<Scalars['Int']['output']>;
  Incomplete?: Maybe<Scalars['Int']['output']>;
  PaymentSent?: Maybe<Scalars['Int']['output']>;
  PendingCertification?: Maybe<Scalars['Int']['output']>;
  ReadyForReview?: Maybe<Scalars['Int']['output']>;
  Withdrawn?: Maybe<Scalars['Int']['output']>;
};

export type GqlCaseFilter = {
  assigneeId?: InputMaybe<Array<Scalars['AssigneeId']['input']>>;
  ids?: InputMaybe<Array<Scalars['UUID']['input']>>;
  programId?: InputMaybe<Scalars['UUID']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  searchAnswersKey?: InputMaybe<Scalars['String']['input']>;
  searchAnswersValue?: InputMaybe<Scalars['String']['input']>;
  searchCategories?: InputMaybe<Array<Scalars['String']['input']>>;
  status?: InputMaybe<Array<GqlCaseStatus>>;
  statusFilters?: InputMaybe<GqlStatusFilters>;
};

export type GqlCaseMetadata = {
  __typename?: 'CaseMetadata';
  denialReason?: Maybe<Scalars['String']['output']>;
  incompleteReason?: Maybe<Scalars['String']['output']>;
  intakeStaff?: Maybe<Scalars['String']['output']>;
  withdrawalReason?: Maybe<Scalars['String']['output']>;
};

export type GqlCaseMutationResponse = {
  __typename?: 'CaseMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlCase>;
};

export type GqlCaseMutations = {
  __typename?: 'CaseMutations';
  addBulkComment: GqlBulkCaseMutationResponse;
  addComment: GqlCaseMutationResponse;
  addParticipantComment: GqlCaseMutationResponse;
  addTagToCase: GqlCaseTagMutationResponse;
  approvePayments: GqlBulkCaseMutationResponse;
  expedite: GqlBulkCaseMutationResponse;
  inviteParticipant: GqlCaseMutationResponse;
  overridePayments: GqlBulkCaseMutationResponse;
  removeDocuments: GqlCaseMutationResponse;
  removeTagFromCase: GqlCaseTagMutationResponse;
  transitionStatuses: GqlBulkCaseMutationResponse;
  undoExpedite: GqlBulkCaseMutationResponse;
  unlinkParticipant: GqlCaseMutationResponse;
  uploadDocuments: GqlCaseMutationResponse;
};


export type GqlCaseMutationsAddBulkCommentArgs = {
  input: GqlAddBulkCommentInput;
};


export type GqlCaseMutationsAddCommentArgs = {
  input: GqlAddCommentInput;
};


export type GqlCaseMutationsAddParticipantCommentArgs = {
  input: GqlAddCommentInput;
};


export type GqlCaseMutationsAddTagToCaseArgs = {
  input: GqlCreateCaseTagInput;
};


export type GqlCaseMutationsApprovePaymentsArgs = {
  input: GqlApproveCasePaymentsInput;
};


export type GqlCaseMutationsExpediteArgs = {
  input: GqlExpediteCasesInput;
};


export type GqlCaseMutationsInviteParticipantArgs = {
  input: GqlInviteCaseParticipantInput;
};


export type GqlCaseMutationsOverridePaymentsArgs = {
  input: GqlOverrideCasePaymentsInput;
};


export type GqlCaseMutationsRemoveDocumentsArgs = {
  input: GqlRemoveDocumentsInput;
};


export type GqlCaseMutationsRemoveTagFromCaseArgs = {
  input: GqlDeleteCaseTagInput;
};


export type GqlCaseMutationsTransitionStatusesArgs = {
  input: GqlTransitionCaseStatusesInput;
};


export type GqlCaseMutationsUndoExpediteArgs = {
  input: GqlExpediteCasesInput;
};


export type GqlCaseMutationsUnlinkParticipantArgs = {
  input: GqlUnlinkCaseParticipantInput;
};


export type GqlCaseMutationsUploadDocumentsArgs = {
  input: GqlUploadDocumentsInput;
};

export type GqlCasePage = {
  __typename?: 'CasePage';
  cases: Array<GqlCase>;
  pageInfo: GqlPageInfo;
};

export type GqlCaseParticipant = {
  __typename?: 'CaseParticipant';
  applicantType: GqlApplicantType;
  case: GqlCase;
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  email: Scalars['EmailAddress']['output'];
  id: Scalars['UUID']['output'];
  invitationCodes: Array<GqlInvitationCode>;
  linkAttempts: Array<GqlLinkAttempt>;
  name: Scalars['NonEmptyString']['output'];
};

export enum GqlCaseParticipantStatus {
  FailedLink = 'FailedLink',
  Linked = 'Linked',
  PendingLink = 'PendingLink',
  Unlinked = 'Unlinked'
}

export type GqlCaseSort = {
  column: GqlCaseSortColumn;
  direction: GqlSortDirection;
};

export enum GqlCaseSortColumn {
  AmiBracket = 'AMIBracket',
  ApplicantName = 'ApplicantName',
  Assignee = 'Assignee',
  AwardedAmount = 'AwardedAmount',
  CreatedAt = 'CreatedAt',
  Eligibility = 'Eligibility',
  ProgramName = 'ProgramName',
  PropertyAddress = 'PropertyAddress',
  Status = 'Status',
  SubmittedAt = 'SubmittedAt',
  UpdatedAt = 'UpdatedAt'
}

export enum GqlCaseStatus {
  Approved = 'Approved',
  Archived = 'Archived',
  Denied = 'Denied',
  FiscalReview = 'FiscalReview',
  InProgress = 'InProgress',
  InReview = 'InReview',
  Incomplete = 'Incomplete',
  PaymentSent = 'PaymentSent',
  PendingCertification = 'PendingCertification',
  ReadyForReview = 'ReadyForReview',
  Withdrawn = 'Withdrawn'
}

export type GqlCaseTag = {
  __typename?: 'CaseTag';
  case: GqlCase;
  caseId: Scalars['UUID']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  tag: GqlTag;
  tagId: Scalars['UUID']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GqlCaseTagMutationResponse = {
  __typename?: 'CaseTagMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlCaseTag>;
};

export type GqlCasesApplicationsSource = {
  __typename?: 'CasesApplicationsSource';
  applicationAnswers?: Maybe<Array<GqlApplicationAnswer>>;
  applicationCreatedAt: Scalars['DateTime']['output'];
  applicationDeactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  applicationDisplayId: Scalars['NonEmptyString']['output'];
  applicationEligibility?: Maybe<Scalars['String']['output']>;
  applicationEligibilityReason?: Maybe<Scalars['String']['output']>;
  applicationId: Scalars['UUID']['output'];
  applicationReferralId?: Maybe<Scalars['UUID']['output']>;
  applicationRequestedAmount?: Maybe<Scalars['Float']['output']>;
  applicationSubmittedAt?: Maybe<Scalars['DateTime']['output']>;
  applicationSubmitterId: Scalars['UUID']['output'];
  applicationUpdatedAt: Scalars['DateTime']['output'];
  applicationVerificationScore?: Maybe<Scalars['Float']['output']>;
  assigneeId?: Maybe<Scalars['UUID']['output']>;
  assigneeName?: Maybe<Scalars['String']['output']>;
  caseApplicantTypeIds?: Maybe<Scalars['String']['output']>;
  caseApplicantTypes?: Maybe<Scalars['String']['output']>;
  caseCreatedAt: Scalars['DateTime']['output'];
  caseDeactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  caseDecisionReachedAt?: Maybe<Scalars['DateTime']['output']>;
  caseDisplayId: Scalars['NonEmptyString']['output'];
  caseId: Scalars['UUID']['output'];
  caseLegacyId?: Maybe<Scalars['String']['output']>;
  caseName: Scalars['String']['output'];
  caseParticipantCount: Scalars['Int']['output'];
  casePartnerId: Scalars['UUID']['output'];
  casePriority: Scalars['Int']['output'];
  caseProgramId: Scalars['UUID']['output'];
  caseProgramName: Scalars['String']['output'];
  caseProgramStatus: Scalars['String']['output'];
  caseStatus: GqlCaseStatus;
  caseStatusUpdatedAt?: Maybe<Scalars['DateTime']['output']>;
  caseTagIds?: Maybe<Scalars['String']['output']>;
  caseTags?: Maybe<Scalars['String']['output']>;
  documentIds?: Maybe<Scalars['String']['output']>;
  documentTagIds?: Maybe<Scalars['String']['output']>;
  documentTags?: Maybe<Scalars['String']['output']>;
  hasFailedLink: Scalars['Boolean']['output'];
  hasMissingParticipant: Scalars['Boolean']['output'];
  hasPendingLink: Scalars['Boolean']['output'];
  location?: Maybe<GqlGeoPoint>;
  partnerName: Scalars['String']['output'];
  paymentStatus?: Maybe<Array<GqlPaymentStatus>>;
  reviewStatus?: Maybe<Array<Scalars['String']['output']>>;
  submitterDisplayId: Scalars['NonEmptyString']['output'];
  submitterEmail: Scalars['String']['output'];
  submitterLegacyId?: Maybe<Scalars['String']['output']>;
  submitterName: Scalars['String']['output'];
  submitterPhone?: Maybe<Scalars['String']['output']>;
};

export type GqlCasesMutationResponse = {
  __typename?: 'CasesMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<Array<GqlCase>>;
};

export type GqlChangelog = {
  __typename?: 'Changelog';
  content: Scalars['NonEmptyString']['output'];
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['UUID']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GqlChannelConfiguration = {
  __typename?: 'ChannelConfiguration';
  channel: GqlNotificationChannel;
  enabled: Scalars['Boolean']['output'];
  template: GqlNotificationTemplate;
};

export type GqlCheckboxField = {
  __typename?: 'CheckboxField';
  copy?: Maybe<Scalars['JSON']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  type: GqlApplicationFieldType;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlCheckboxGroupField = {
  __typename?: 'CheckboxGroupField';
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  options: Array<GqlOptionLabelValue>;
  type: GqlApplicationFieldType;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlClaimCheckInput = {
  address: GqlCreateAddressInput;
  id: Scalars['UUID']['input'];
};

export type GqlClaimDirectDepositInput = {
  bankAccount: GqlBankAccountInput;
  id: Scalars['UUID']['input'];
};

export type GqlClaimFulfillmentResponse = {
  __typename?: 'ClaimFulfillmentResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlFulfillment>;
};

export type GqlClaimPhysicalCardInput = {
  address: GqlCreateAddressInput;
  id: Scalars['UUID']['input'];
};

export type GqlClaimVirtualCardInput = {
  address: GqlCreateAddressInput;
  id: Scalars['UUID']['input'];
};

export type GqlClaimZelleInput = {
  id: Scalars['UUID']['input'];
};

export type GqlComment = {
  __typename?: 'Comment';
  author?: Maybe<GqlUser>;
  authorId: Scalars['UUID']['output'];
  case?: Maybe<GqlCase>;
  caseId: Scalars['UUID']['output'];
  children?: Maybe<Array<GqlComment>>;
  content: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  parent?: Maybe<GqlComment>;
  parentId?: Maybe<Scalars['UUID']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GqlCommunicationChannelsConfig = {
  __typename?: 'CommunicationChannelsConfig';
  channels?: Maybe<Scalars['JSON']['output']>;
};

export type GqlCommunicationChannelsConfiguration = {
  __typename?: 'CommunicationChannelsConfiguration';
  config?: Maybe<GqlCommunicationChannelsConfig>;
};

export type GqlCommunicationPreferences = {
  __typename?: 'CommunicationPreferences';
  email?: Maybe<Scalars['Boolean']['output']>;
  sms?: Maybe<Scalars['Boolean']['output']>;
};

export type GqlCommunicationPreferencesInput = {
  email?: InputMaybe<Scalars['Boolean']['input']>;
  sms?: InputMaybe<Scalars['Boolean']['input']>;
};

export type GqlComplexField = {
  __typename?: 'ComplexField';
  copy?: Maybe<Scalars['JSON']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  subFields: Array<GqlApplicationField>;
  type: GqlApplicationFieldType;
  useLabelPrefix?: Maybe<Scalars['Boolean']['output']>;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export enum GqlContentBlockType {
  Custom = 'Custom',
  IconList = 'IconList',
  Logo = 'Logo',
  Paragraph = 'Paragraph'
}

export type GqlCreateAccessRequestInput = {
  detail?: InputMaybe<Scalars['String']['input']>;
  partnerId: Scalars['UUID']['input'];
  relation: Scalars['NonEmptyString']['input'];
  resource: GqlObjectReference;
};

export type GqlCreateAddressInput = {
  addressLine1?: InputMaybe<Scalars['String']['input']>;
  addressLine2?: InputMaybe<Scalars['String']['input']>;
  careOf?: InputMaybe<Scalars['String']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  county?: InputMaybe<Scalars['String']['input']>;
  latitude?: InputMaybe<Scalars['Latitude']['input']>;
  longitude?: InputMaybe<Scalars['Longitude']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  zip?: InputMaybe<Scalars['String']['input']>;
};

export type GqlCreateAdminInput = {
  email: Scalars['EmailAddress']['input'];
  name?: InputMaybe<Scalars['NonEmptyString']['input']>;
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
  roles?: InputMaybe<Array<GqlPortalRole>>;
};

export type GqlCreateApplicationInput = {
  programId: Scalars['UUID']['input'];
  userId?: InputMaybe<Scalars['UUID']['input']>;
};

export type GqlCreateBulkProgramReferralInput = {
  programId: Scalars['UUID']['input'];
  userIds: Array<Scalars['UUID']['input']>;
};

export type GqlCreateCaseTagInput = {
  caseId: Scalars['UUID']['input'];
  tagId: Scalars['UUID']['input'];
};

export type GqlCreateFundInput = {
  awardAmountMax?: InputMaybe<Scalars['BigInt']['input']>;
  name: Scalars['NonEmptyString']['input'];
  startingBalance: Scalars['BigInt']['input'];
};

export type GqlCreateIncidentInput = {
  message: Scalars['NonEmptyString']['input'];
  severity?: InputMaybe<Scalars['Int']['input']>;
};

export type GqlCreatePaymentInput = {
  caseId: Scalars['UUID']['input'];
  payment: GqlPaymentInput;
};

export type GqlCreateProgramInput = {
  fundIds: Array<Scalars['UUID']['input']>;
  heroImage?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['NonEmptyString']['input'];
  status?: InputMaybe<GqlProgramStatus>;
};

export type GqlCreateReferralInput = {
  programId: Scalars['UUID']['input'];
  userId: Scalars['UUID']['input'];
};

export type GqlCreateSavedViewInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  filters: Scalars['JSON']['input'];
  isPublic?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['NonEmptyString']['input'];
};

export type GqlCreateTag = {
  name: Scalars['NonEmptyString']['input'];
};

export type GqlCreateTagAutomationInput = {
  actionType: GqlTagAutomationActionType;
  criteria?: InputMaybe<Scalars['JSON']['input']>;
  programId?: InputMaybe<Scalars['UUID']['input']>;
  tagIds: Array<Scalars['UUID']['input']>;
  triggerType: GqlTagAutomationTriggerType;
};

export type GqlCreateTagsInput = {
  tags: Array<GqlCreateTag>;
};

export type GqlCreateUserInput = {
  applicantTypeId: Scalars['UUID']['input'];
  email?: InputMaybe<Scalars['EmailAddress']['input']>;
  name: Scalars['NonEmptyString']['input'];
  partnerId: Scalars['UUID']['input'];
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
};

export type GqlCreateVendorInput = {
  bankAccount?: InputMaybe<GqlBankAccountInput>;
  email?: InputMaybe<Scalars['EmailAddress']['input']>;
  externalId?: InputMaybe<Scalars['NonEmptyString']['input']>;
  files: Array<Scalars['Upload']['input']>;
  mailingAddress: GqlCreateAddressInput;
  name: Scalars['NonEmptyString']['input'];
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
  taxId?: InputMaybe<Scalars['NonEmptyString']['input']>;
  typeIds: Array<Scalars['UUID']['input']>;
};

export type GqlCreateW9TaxFormInput = {
  data: GqlW9FormData;
  type: GqlTaxFormType;
};

export type GqlCursorPageInfo = {
  __typename?: 'CursorPageInfo';
  count: Scalars['NonNegativeInt']['output'];
  endCursor?: Maybe<Scalars['NonEmptyString']['output']>;
  hasNextPage: Scalars['Boolean']['output'];
  hasPreviousPage: Scalars['Boolean']['output'];
  startCursor?: Maybe<Scalars['NonEmptyString']['output']>;
};

export type GqlCursorPagination = {
  after?: InputMaybe<Scalars['NonEmptyString']['input']>;
  before?: InputMaybe<Scalars['NonEmptyString']['input']>;
  take: Scalars['Int']['input'];
};

export type GqlDataLookupConfiguration = {
  __typename?: 'DataLookupConfiguration';
  fields: Array<GqlDataLookupField>;
};

export type GqlDataLookupField = {
  __typename?: 'DataLookupField';
  details: Scalars['NonEmptyString']['output'];
  key: Scalars['NonEmptyString']['output'];
  metadata?: Maybe<Scalars['Boolean']['output']>;
  sample: Scalars['NonEmptyString']['output'];
  weight?: Maybe<Scalars['Float']['output']>;
};

export type GqlDataLookupFieldInput = {
  details: Scalars['NonEmptyString']['input'];
  key: Scalars['NonEmptyString']['input'];
  metadata?: InputMaybe<Scalars['Boolean']['input']>;
  sample: Scalars['NonEmptyString']['input'];
  weight?: InputMaybe<Scalars['Float']['input']>;
};

export type GqlDateField = {
  __typename?: 'DateField';
  copy?: Maybe<Scalars['JSON']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  props?: Maybe<Scalars['JSON']['output']>;
  type: GqlApplicationFieldType;
  validation?: Maybe<GqlDateFieldValidation>;
};

export enum GqlDateFieldRange {
  Future = 'future',
  Past = 'past',
  Today = 'today'
}

export type GqlDateFieldValidation = {
  __typename?: 'DateFieldValidation';
  allowedRange?: Maybe<GqlDateFieldRange>;
  condition?: Maybe<Scalars['JSON']['output']>;
  maximumDate?: Maybe<Scalars['NonEmptyString']['output']>;
  minimumDate?: Maybe<Scalars['NonEmptyString']['output']>;
  required: Scalars['Boolean']['output'];
  rules?: Maybe<Array<GqlApplicationFieldValidationRule>>;
};

export enum GqlDateInterval {
  Week = 'Week'
}

export type GqlDateRange = {
  end?: InputMaybe<Scalars['DateTime']['input']>;
  interval?: InputMaybe<GqlDateInterval>;
  start?: InputMaybe<Scalars['DateTime']['input']>;
};

export type GqlDeleteAdminInput = {
  id: Scalars['UUID']['input'];
};

export type GqlDeleteCaseTagInput = {
  caseTagId: Scalars['UUID']['input'];
};

export type GqlDeleteSavedViewInput = {
  id: Scalars['UUID']['input'];
};

export type GqlDeleteTagAutomationInput = {
  id: Scalars['UUID']['input'];
};

export type GqlDeleteTagsInput = {
  ids: Array<Scalars['UUID']['input']>;
};

export type GqlDeleteVendorInput = {
  id: Scalars['UUID']['input'];
};

export type GqlDoctopusTag = {
  __typename?: 'DoctopusTag';
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  name: Scalars['String']['output'];
  type: Scalars['String']['output'];
  visibility: Scalars['String']['output'];
};

export type GqlDocument = {
  __typename?: 'Document';
  createdAt: Scalars['DateTime']['output'];
  documentFields: Array<GqlDocumentField>;
  documentKey: Scalars['NonEmptyString']['output'];
  documentTags: Array<GqlDocumentTag>;
  filename: Scalars['NonEmptyString']['output'];
  id: Scalars['UUID']['output'];
  mimetype: Scalars['NonEmptyString']['output'];
  pinned?: Maybe<Scalars['Boolean']['output']>;
  previewUrl?: Maybe<Scalars['URL']['output']>;
  summary?: Maybe<GqlDocumentSummary>;
  uploader?: Maybe<GqlUser>;
  uploaderId: Scalars['UUID']['output'];
};

export type GqlDocumentConsistency = {
  __typename?: 'DocumentConsistency';
  bestMatchedName: Scalars['String']['output'];
  documentId: Scalars['UUID']['output'];
  similarity: GqlSimilarity;
};

export type GqlDocumentField = {
  __typename?: 'DocumentField';
  confidence?: Maybe<Scalars['Float']['output']>;
  copy?: Maybe<Scalars['JSON']['output']>;
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  docId: Scalars['UUID']['output'];
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  fieldKey: Scalars['String']['output'];
  fieldValue: Scalars['String']['output'];
  id: Scalars['UUID']['output'];
  key: Scalars['NonEmptyString']['output'];
  normalizedValue?: Maybe<Scalars['String']['output']>;
  startIndex?: Maybe<Scalars['Int']['output']>;
  stopIndex?: Maybe<Scalars['Int']['output']>;
  type: GqlApplicationFieldType;
  valid?: Maybe<Scalars['Boolean']['output']>;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlDocumentFieldCopy = {
  __typename?: 'DocumentFieldCopy';
  description?: Maybe<Scalars['NonEmptyString']['output']>;
  title?: Maybe<Scalars['NonEmptyString']['output']>;
};

export type GqlDocumentMutations = {
  __typename?: 'DocumentMutations';
  pinDocument: GqlDocumentsMutationResponse;
  submitFeedback: GqlDocumentsMutationResponse;
};


export type GqlDocumentMutationsPinDocumentArgs = {
  input: GqlPinDocumentInput;
};


export type GqlDocumentMutationsSubmitFeedbackArgs = {
  input: GqlSubmitPredictionFeedbackInput;
};

export type GqlDocumentSummary = {
  __typename?: 'DocumentSummary';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  /** @deprecated This field is no longer in use. There is no direct replacement. */
  modelVersion: GqlModelVersion;
  ocr?: Maybe<GqlOcrResult>;
  prediction?: Maybe<GqlPrediction>;
};

export type GqlDocumentTag = {
  __typename?: 'DocumentTag';
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  documentId: Scalars['UUID']['output'];
  id: Scalars['UUID']['output'];
  tag: GqlDoctopusTag;
  tagId: Scalars['UUID']['output'];
};

export type GqlDocumentsMutationResponse = {
  __typename?: 'DocumentsMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlDocument>;
};

export enum GqlDomain {
  Applicant = 'Applicant',
  Partner = 'Partner',
  ThirdParty = 'ThirdParty'
}

export enum GqlDropdownChildMode {
  AlwaysAvailable = 'AlwaysAvailable',
  OnParentSelect = 'OnParentSelect'
}

export type GqlDropdownField = {
  __typename?: 'DropdownField';
  copy?: Maybe<Scalars['JSON']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  options: Array<GqlOptionLabelValue>;
  props?: Maybe<Scalars['JSON']['output']>;
  type: GqlApplicationFieldType;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlEligibilityConfig = {
  __typename?: 'EligibilityConfig';
  copy?: Maybe<Scalars['NonEmptyString']['output']>;
  createdAt: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  id: Scalars['UUID']['output'];
  message?: Maybe<Scalars['JSON']['output']>;
  questions: Array<GqlEligibilityQuestion>;
  title?: Maybe<Scalars['NonEmptyString']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlEligibilityQuestion = {
  __typename?: 'EligibilityQuestion';
  copy?: Maybe<Scalars['NonEmptyString']['output']>;
  description: Scalars['NonEmptyString']['output'];
  id: Scalars['UUID']['output'];
  key: Scalars['NonEmptyString']['output'];
  props: Scalars['JSON']['output'];
  type: GqlEligibilityQuestionType;
};

export enum GqlEligibilityQuestionType {
  AmiComponent = 'AMIComponent',
  Address = 'Address',
  Content = 'Content',
  Dropdown = 'Dropdown',
  TextInput = 'TextInput',
  YesOrNo = 'YesOrNo'
}

export enum GqlEligibilityStatus {
  Eligible = 'Eligible',
  FailedToEvaluate = 'FailedToEvaluate',
  Ineligible = 'Ineligible',
  NotEvaluated = 'NotEvaluated',
  Undetermined = 'Undetermined'
}

export type GqlEmailTemplateContent = {
  __typename?: 'EmailTemplateContent';
  button?: Maybe<GqlNotificationAction>;
  buttons?: Maybe<Array<GqlNotificationAction>>;
  postText?: Maybe<Scalars['String']['output']>;
  preText: Scalars['String']['output'];
  subject: Scalars['String']['output'];
};

export type GqlEndSessionInput = {
  tenantId: Scalars['NonEmptyString']['input'];
  token: Scalars['NonEmptyString']['input'];
};

export type GqlEnrollment = {
  __typename?: 'Enrollment';
  createdAt: Scalars['DateTime']['output'];
  endDate?: Maybe<Scalars['Date']['output']>;
  enrollmentOutcomes: Array<GqlEnrollmentOutcome>;
  enrollmentServices: Array<GqlEnrollmentService>;
  id: Scalars['UUID']['output'];
  program: GqlProgram;
  startDate?: Maybe<Scalars['Date']['output']>;
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlEnrollmentIdentifier = {
  applicantId: Scalars['UUID']['input'];
  programId: Scalars['UUID']['input'];
};

export type GqlEnrollmentMutationResponse = {
  __typename?: 'EnrollmentMutationResponse';
  metadata: GqlResponseMetadata;
  record?: Maybe<GqlEnrollment>;
};

export type GqlEnrollmentMutations = {
  __typename?: 'EnrollmentMutations';
  removeOutcome: GqlEnrollmentMutationResponse;
  removeService: GqlEnrollmentMutationResponse;
  upsert: GqlEnrollmentMutationResponse;
  upsertOutcome: GqlEnrollmentMutationResponse;
  upsertService: GqlEnrollmentMutationResponse;
};


export type GqlEnrollmentMutationsRemoveOutcomeArgs = {
  input: GqlRemoveEnrollmentOutcomeInput;
};


export type GqlEnrollmentMutationsRemoveServiceArgs = {
  input: GqlRemoveEnrollmentServiceInput;
};


export type GqlEnrollmentMutationsUpsertArgs = {
  input: GqlUpsertEnrollmentInput;
};


export type GqlEnrollmentMutationsUpsertOutcomeArgs = {
  input: GqlUpsertEnrollmentOutcomeInput;
};


export type GqlEnrollmentMutationsUpsertServiceArgs = {
  input: GqlUpsertEnrollmentServiceInput;
};

export type GqlEnrollmentOutcome = {
  __typename?: 'EnrollmentOutcome';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  outcomeDate: Scalars['Date']['output'];
  outcomes: Array<GqlOutcome>;
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlEnrollmentService = {
  __typename?: 'EnrollmentService';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  serviceDate: Scalars['Date']['output'];
  services: Array<GqlService>;
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlExpediteCasesInput = {
  ids: Array<Scalars['UUID']['input']>;
  reason: Scalars['String']['input'];
};

export enum GqlExpenseType {
  Hotel = 'Hotel',
  Internet = 'Internet',
  Other = 'Other',
  Rent = 'Rent',
  SecurityDeposit = 'SecurityDeposit'
}

export type GqlExportTableToCsvInput = {
  dashboardId: Scalars['String']['input'];
};

export type GqlFeature = {
  __typename?: 'Feature';
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlFeatureSetting = {
  __typename?: 'FeatureSetting';
  createdAt: Scalars['DateTime']['output'];
  enabled: Scalars['Boolean']['output'];
  feature: GqlFeature;
  id: Scalars['UUID']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlFeedback = {
  __typename?: 'Feedback';
  accurate: GqlYesNoUnsure;
  admin: GqlAdmin;
  id: Scalars['UUID']['output'];
  preferredLabel?: Maybe<GqlActiveLabel>;
};

export type GqlFieldResponse = {
  __typename?: 'FieldResponse';
  answer?: Maybe<GqlApplicationAnswer>;
  field: GqlApplicationField;
};

export type GqlFormDefinition = {
  __typename?: 'FormDefinition';
  applicantTypeId: Scalars['UUID']['output'];
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  formSchema: Scalars['JSON']['output'];
  id: Scalars['UUID']['output'];
  previousVersion?: Maybe<GqlFormDefinition>;
  previousVersionId?: Maybe<Scalars['UUID']['output']>;
  programId: Scalars['UUID']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlFulfillment = {
  __typename?: 'Fulfillment';
  approvedAmount: Scalars['NonNegativeInt']['output'];
  case: GqlCase;
  displayId: Scalars['String']['output'];
  fulfillmentMeta?: Maybe<GqlFulfillmentMeta>;
  fund: GqlFund;
  id: Scalars['UUID']['output'];
  paymentPattern?: Maybe<GqlPaymentPattern>;
  payments?: Maybe<Array<GqlPayment>>;
  schedule?: Maybe<Array<Scalars['DateTime']['output']>>;
  scheduleType: GqlScheduleType;
};

export type GqlFulfillmentFilter = {
  ids?: InputMaybe<Array<Scalars['UUID']['input']>>;
};

export type GqlFulfillmentMeta = {
  __typename?: 'FulfillmentMeta';
  accountNumber?: Maybe<Scalars['String']['output']>;
  benefit?: Maybe<GqlBenefitType>;
  billingCode?: Maybe<Scalars['String']['output']>;
  checkNumber?: Maybe<Scalars['String']['output']>;
  endDate?: Maybe<Scalars['Date']['output']>;
  expenseType?: Maybe<GqlExpenseType>;
  fulfillment: GqlFulfillment;
  id: Scalars['UUID']['output'];
  months: Array<GqlMonth>;
  serviceDate?: Maybe<Scalars['Date']['output']>;
  startDate?: Maybe<Scalars['Date']['output']>;
  type?: Maybe<GqlSubBenefitType>;
  utilityType?: Maybe<GqlUtilityType>;
};

export type GqlFulfillmentMutations = {
  __typename?: 'FulfillmentMutations';
  /** @deprecated Replaced with case.approvePayments */
  approveAmount: GqlFulfillmentResponse;
  bulkIssueFunds: GqlBulkFulfillmentResponse;
  claimCheck: GqlClaimFulfillmentResponse;
  claimDirectDeposit: GqlClaimFulfillmentResponse;
  claimPhysicalCard: GqlClaimFulfillmentResponse;
  claimVirtualCard: GqlClaimFulfillmentResponse;
  claimZelle: GqlClaimFulfillmentResponse;
  issueFunds: GqlClaimFulfillmentResponse;
};


export type GqlFulfillmentMutationsApproveAmountArgs = {
  input?: InputMaybe<GqlApproveAmountInput>;
};


export type GqlFulfillmentMutationsBulkIssueFundsArgs = {
  input?: InputMaybe<GqlBulkIssueFundsInput>;
};


export type GqlFulfillmentMutationsClaimCheckArgs = {
  input: GqlClaimCheckInput;
};


export type GqlFulfillmentMutationsClaimDirectDepositArgs = {
  input: GqlClaimDirectDepositInput;
};


export type GqlFulfillmentMutationsClaimPhysicalCardArgs = {
  input: GqlClaimPhysicalCardInput;
};


export type GqlFulfillmentMutationsClaimVirtualCardArgs = {
  input: GqlClaimVirtualCardInput;
};


export type GqlFulfillmentMutationsClaimZelleArgs = {
  input: GqlClaimZelleInput;
};


export type GqlFulfillmentMutationsIssueFundsArgs = {
  input: GqlIssueFundsInput;
};

export type GqlFulfillmentPage = {
  __typename?: 'FulfillmentPage';
  fulfillments: Array<GqlFulfillment>;
  pageInfo: GqlVerifiedPageInfo;
};

export type GqlFulfillmentResponse = {
  __typename?: 'FulfillmentResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlFulfillment>;
};

export type GqlFund = {
  __typename?: 'Fund';
  awardAmountMax?: Maybe<Scalars['BigInt']['output']>;
  config?: Maybe<GqlFundConfig>;
  defaultPaymentFieldKey?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  name: Scalars['String']['output'];
  programs: Array<GqlProgram>;
  startingBalance: Scalars['BigInt']['output'];
  stats?: Maybe<GqlFundStats>;
};

export type GqlFundConfig = {
  __typename?: 'FundConfig';
  accountNumber?: Maybe<Scalars['String']['output']>;
  checkCourierCode?: Maybe<Scalars['String']['output']>;
  checkFormatCode?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  includeCheckMemo?: Maybe<Scalars['Boolean']['output']>;
  physicalDistributorId?: Maybe<Scalars['String']['output']>;
  programId?: Maybe<Scalars['String']['output']>;
  provider?: Maybe<Scalars['String']['output']>;
  skipACHTransfer?: Maybe<Scalars['Boolean']['output']>;
  usioDesignId?: Maybe<Scalars['String']['output']>;
  usioKey?: Maybe<Scalars['String']['output']>;
  virtualDistributorId?: Maybe<Scalars['String']['output']>;
};

export type GqlFundFilter = {
  id?: InputMaybe<Scalars['UUID']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type GqlFundMutationResponse = {
  __typename?: 'FundMutationResponse';
  metadata: GqlResponseMetadata;
  record?: Maybe<GqlFund>;
};

export type GqlFundMutations = {
  __typename?: 'FundMutations';
  create: GqlFundMutationResponse;
  remove: GqlFundMutationResponse;
  update: GqlFundMutationResponse;
};


export type GqlFundMutationsCreateArgs = {
  input: GqlCreateFundInput;
};


export type GqlFundMutationsRemoveArgs = {
  input: GqlRemoveFundInput;
};


export type GqlFundMutationsUpdateArgs = {
  input: GqlUpdateFundInput;
};

export type GqlFundPage = {
  __typename?: 'FundPage';
  nodes: Array<GqlFund>;
  pageInfo: GqlPageInfo;
};

export type GqlFundSort = {
  column: GqlFundSortColumn;
  direction: GqlSortDirection;
};

export enum GqlFundSortColumn {
  Name = 'Name',
  StartingBalance = 'StartingBalance'
}

export type GqlFundStats = {
  __typename?: 'FundStats';
  awardedBalance: Scalars['BigInt']['output'];
  id: Scalars['UUID']['output'];
  obligatedBalance: Scalars['BigInt']['output'];
  remainingBalance: Scalars['BigInt']['output'];
};

export type GqlGeoPoint = {
  __typename?: 'GeoPoint';
  lat: Scalars['Float']['output'];
  lon: Scalars['Float']['output'];
};

export type GqlHasAccessInput = {
  action: Scalars['NonEmptyString']['input'];
  resource: GqlObjectReference;
};

export type GqlHasAccessResponse = {
  __typename?: 'HasAccessResponse';
  canAccess: Scalars['Boolean']['output'];
  /** @deprecated Use canAccess instead */
  hasAccess: Scalars['Boolean']['output'];
  id: Scalars['NonEmptyString']['output'];
  message?: Maybe<Scalars['String']['output']>;
  token?: Maybe<Scalars['NonEmptyString']['output']>;
};

export type GqlHouseholdLimit = {
  __typename?: 'HouseholdLimit';
  householdSize: Scalars['NonNegativeInt']['output'];
  incomeLimit: Scalars['NonNegativeInt']['output'];
};

export type GqlIdentityConfig = {
  __typename?: 'IdentityConfig';
  advocate?: Maybe<GqlAdvocateIdentityConfig>;
  applicant?: Maybe<GqlApplicantIdentityConfig>;
};

export type GqlIdentityMutations = {
  __typename?: 'IdentityMutations';
  beginSession: GqlIdentityResponse;
  endSession: GqlIdentityResponse;
  /** @deprecated use sendMagicLinkV2 instead */
  sendMagicLink: GqlIdentityResponse;
  sendMagicLinkV2: GqlIdentityResponse;
  sendVerificationEmail: GqlIdentityResponse;
  verifyEmail: GqlIdentityResponse;
};


export type GqlIdentityMutationsBeginSessionArgs = {
  input: GqlBeginSessionInput;
};


export type GqlIdentityMutationsSendMagicLinkArgs = {
  input: GqlSendMagicLinkInput;
};


export type GqlIdentityMutationsSendMagicLinkV2Args = {
  input: GqlSendMagicLinkInput;
};


export type GqlIdentityMutationsSendVerificationEmailArgs = {
  input: GqlSendIdentityVerificationEmailInput;
};


export type GqlIdentityMutationsVerifyEmailArgs = {
  input: GqlVerifyIdentityEmailInput;
};

export type GqlIdentityResponse = {
  __typename?: 'IdentityResponse';
  metadata: GqlResponseMetadata;
};

export type GqlIdentityUser = {
  __typename?: 'IdentityUser';
  email: Scalars['NonEmptyString']['output'];
  id: Scalars['UUID']['output'];
  name: Scalars['NonEmptyString']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  verifiedEmail: Scalars['Boolean']['output'];
};

export type GqlIncidentMessage = {
  __typename?: 'IncidentMessage';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  isGlobal?: Maybe<Scalars['Boolean']['output']>;
  message: Scalars['NonEmptyString']['output'];
  partners?: Maybe<Array<GqlPartner>>;
  severity: Scalars['Int']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlIncidentMessageFilter = {
  partnerId?: InputMaybe<Scalars['UUID']['input']>;
};

export type GqlIncidentMessagePage = {
  __typename?: 'IncidentMessagePage';
  /** @deprecated use 'nodes' instead */
  incidentMessages: Array<GqlIncidentMessage>;
  nodes: Array<GqlIncidentMessage>;
  pageInfo: GqlPageInfo;
};

export type GqlIncidentMutationResponse = {
  __typename?: 'IncidentMutationResponse';
  metadata: GqlResponseMetadata;
  record?: Maybe<GqlPartnerIncident>;
};

export type GqlIncomeLimit = {
  __typename?: 'IncomeLimit';
  fipsCode: Scalars['String']['output'];
  id: Scalars['UUID']['output'];
  limits: GqlLimits;
  year: Scalars['NonNegativeInt']['output'];
};

export type GqlIncomeLimitArea = {
  __typename?: 'IncomeLimitArea';
  county: Scalars['NonEmptyString']['output'];
  fipsCode: Scalars['NonEmptyString']['output'];
  id: Scalars['UUID']['output'];
  incomeLimit: GqlIncomeLimit;
  state: Scalars['NonEmptyString']['output'];
  town?: Maybe<Scalars['String']['output']>;
};

export type GqlIncomeLimitAreaFilter = {
  county?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  search?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GqlIncomeLimitAreaPage = {
  __typename?: 'IncomeLimitAreaPage';
  incomeLimitAreas: Array<GqlIncomeLimitArea>;
  pageInfo: GqlPageInfo;
};

export enum GqlIndex {
  CasesApplications = 'cases_applications'
}

export type GqlInputMonth = {
  month: Scalars['Int']['input'];
  year: Scalars['Int']['input'];
};

export type GqlIntroPage = {
  __typename?: 'IntroPage';
  body?: Maybe<Scalars['NonEmptyString']['output']>;
  copy?: Maybe<Scalars['String']['output']>;
  key: Scalars['NonEmptyString']['output'];
  layout: GqlIntroPageLayoutType;
  steps?: Maybe<Array<GqlIntroPageContentBlock>>;
  title: Scalars['NonEmptyString']['output'];
};

export type GqlIntroPageContentBlock = {
  __typename?: 'IntroPageContentBlock';
  description?: Maybe<Scalars['NonEmptyString']['output']>;
  icon: Scalars['NonEmptyString']['output'];
  title: Scalars['NonEmptyString']['output'];
};

export enum GqlIntroPageLayoutType {
  Default = 'default',
  Steps = 'steps',
  Welcome = 'welcome'
}

export type GqlInvitationCode = {
  __typename?: 'InvitationCode';
  caseParticipant: GqlCaseParticipant;
  code: Scalars['NonEmptyString']['output'];
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['UUID']['output'];
  usedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GqlInviteCaseParticipantInput = {
  id: Scalars['UUID']['input'];
  participant?: InputMaybe<GqlParticipantInput>;
};

export type GqlIssueFundsInput = {
  id: Scalars['UUID']['input'];
};

export enum GqlLegacyUserType {
  LinkedLegacyUser = 'LinkedLegacyUser',
  NonLegacyUser = 'NonLegacyUser',
  UnlinkedLegacyUser = 'UnlinkedLegacyUser'
}

export type GqlLimits = {
  __typename?: 'Limits';
  extremelyLow: Array<GqlHouseholdLimit>;
  low: Array<GqlHouseholdLimit>;
  veryLow: Array<GqlHouseholdLimit>;
};

export type GqlLinkAttempt = {
  __typename?: 'LinkAttempt';
  application: GqlApplication;
  caseParticipant: GqlCaseParticipant;
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  details?: Maybe<Scalars['JSON']['output']>;
  id: Scalars['UUID']['output'];
  originalCase: GqlCase;
  result: GqlLinkAttemptResult;
};

export enum GqlLinkAttemptResult {
  Fail = 'fail',
  Success = 'success'
}

export type GqlLinkLegacyUserInput = {
  id: Scalars['UUID']['input'];
  legacyUserId: Scalars['UUID']['input'];
};

export type GqlLoginDetails = {
  __typename?: 'LoginDetails';
  isVpn?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  timestamp?: Maybe<Scalars['DateTime']['output']>;
};

export enum GqlMailingAddressType {
  Applicant = 'applicant',
  Payee = 'payee'
}

export type GqlModelVersion = {
  __typename?: 'ModelVersion';
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['NonEmptyString']['output']>;
  id: Scalars['UUID']['output'];
  name: Scalars['NonEmptyString']['output'];
  type: Scalars['NonEmptyString']['output'];
};

export type GqlMonth = {
  __typename?: 'Month';
  month: Scalars['NonNegativeInt']['output'];
  year: Scalars['NonNegativeInt']['output'];
};

export type GqlMutation = {
  __typename?: 'Mutation';
  accessRequest?: Maybe<GqlAccessRequestMutations>;
  addNoteToApplicationAnswer: GqlAddNoteToApplicationAnswerPayload;
  addReviewToApplicationAnswer: GqlAddNoteToApplicationAnswerPayload;
  admin?: Maybe<GqlAdminMutations>;
  application?: Maybe<GqlApplicationMutations>;
  case?: Maybe<GqlCaseMutations>;
  createFormDefinition: GqlFormDefinition;
  document?: Maybe<GqlDocumentMutations>;
  enrollment?: Maybe<GqlEnrollmentMutations>;
  fulfillment?: Maybe<GqlFulfillmentMutations>;
  fund?: Maybe<GqlFundMutations>;
  identity?: Maybe<GqlIdentityMutations>;
  note?: Maybe<GqlNoteMutations>;
  partnerIncident: GqlPartnerIncidentMutations;
  payment?: Maybe<GqlPaymentMutations>;
  preset?: Maybe<GqlPresetMutations>;
  program?: Maybe<GqlProgramMutations>;
  programReferral?: Maybe<GqlProgramReferralMutations>;
  saveRuleset: GqlRuleset;
  savedView?: Maybe<GqlSavedViewMutations>;
  tag?: Maybe<GqlTagMutations>;
  tagAutomations?: Maybe<GqlTagAutomationsMutations>;
  updateFormDefinition: GqlFormDefinition;
  user?: Maybe<GqlUserMutations>;
  vendor?: Maybe<GqlVendorMutations>;
};


export type GqlMutationAddNoteToApplicationAnswerArgs = {
  input: GqlAddNoteToApplicationAnswerInput;
};


export type GqlMutationAddReviewToApplicationAnswerArgs = {
  input: GqlAddReviewToApplicationAnswerInput;
};


export type GqlMutationCreateFormDefinitionArgs = {
  applicantTypeId: Scalars['UUID']['input'];
  formSchema: Scalars['JSON']['input'];
  programId: Scalars['UUID']['input'];
};


export type GqlMutationSaveRulesetArgs = {
  model: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  programId: Scalars['UUID']['input'];
};


export type GqlMutationUpdateFormDefinitionArgs = {
  formSchema: Scalars['JSON']['input'];
  id: Scalars['UUID']['input'];
};

export type GqlNote = {
  __typename?: 'Note';
  author: GqlAdmin;
  case: GqlCase;
  content: Scalars['String']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
};

export type GqlNoteMutationResponse = {
  __typename?: 'NoteMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlNote>;
};

export type GqlNoteMutations = {
  __typename?: 'NoteMutations';
  add: GqlNoteMutationResponse;
  update: GqlNoteMutationResponse;
};


export type GqlNoteMutationsAddArgs = {
  input: GqlAddNoteInput;
};


export type GqlNoteMutationsUpdateArgs = {
  input: GqlUpdateNoteInput;
};

export enum GqlNoteRelationType {
  Case = 'case',
  Profile = 'profile',
  Vendor = 'vendor'
}

export type GqlNotificationAction = {
  __typename?: 'NotificationAction';
  domain?: Maybe<GqlDomain>;
  showFallbackText?: Maybe<Scalars['Boolean']['output']>;
  text: Scalars['String']['output'];
  url: Scalars['String']['output'];
  variant?: Maybe<GqlButtonVariant>;
};

export enum GqlNotificationChannel {
  Email = 'email',
  Sms = 'sms'
}

export type GqlNotificationConfig = {
  __typename?: 'NotificationConfig';
  channelConfigurations: Array<GqlChannelConfiguration>;
  description?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  type: Scalars['NonEmptyString']['output'];
};

export type GqlNotificationTemplate = {
  __typename?: 'NotificationTemplate';
  channel: GqlNotificationChannel;
  content: GqlTemplateContent;
  id: Scalars['UUID']['output'];
  partnerId?: Maybe<Scalars['UUID']['output']>;
  programId?: Maybe<Scalars['UUID']['output']>;
  type: Scalars['NonEmptyString']['output'];
};

export type GqlOcrResult = {
  __typename?: 'OCRResult';
  id: Scalars['UUID']['output'];
  /** @deprecated This field is no longer in use. There is no direct replacement. */
  modelVersion: GqlModelVersion;
  raw?: Maybe<Scalars['JSON']['output']>;
};

export type GqlObjectReference = {
  objectId: Scalars['UUID']['input'];
  objectType: Scalars['NonEmptyString']['input'];
};

export type GqlOffsetPagination = {
  page: Scalars['NonNegativeInt']['input'];
  take: Scalars['Int']['input'];
};

export type GqlOptionLabelValue = {
  __typename?: 'OptionLabelValue';
  children?: Maybe<Array<GqlOptionLabelValue>>;
  description?: Maybe<Scalars['String']['output']>;
  disabled?: Maybe<Scalars['Boolean']['output']>;
  label: Scalars['NonEmptyString']['output'];
  value: Scalars['JSON']['output'];
};

export type GqlOutcome = {
  __typename?: 'Outcome';
  id: Scalars['UUID']['output'];
  name: Scalars['NonEmptyString']['output'];
};

export type GqlOverrideCasePaymentsInput = {
  ids: Array<Scalars['UUID']['input']>;
  payment: GqlOverridePaymentInput;
};

export type GqlOverridePaymentInput = {
  amount: Scalars['NonNegativeInt']['input'];
  applicantTypeId?: InputMaybe<Scalars['UUID']['input']>;
  fundId: Scalars['UUID']['input'];
  payeeType: GqlPayeeType;
};

export type GqlPageInfo = {
  __typename?: 'PageInfo';
  count: Scalars['NonNegativeInt']['output'];
  nextCursor?: Maybe<Scalars['NonEmptyString']['output']>;
};

export type GqlParticipantInput = {
  applicantTypeId: Scalars['UUID']['input'];
  autoLink?: InputMaybe<Scalars['Boolean']['input']>;
  email: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type GqlPartner = {
  __typename?: 'Partner';
  analyticsResources: Array<GqlAnalyticsResource>;
  applicantProfileConfigs: Array<GqlApplicantProfileConfiguration>;
  applicantTypes: Array<GqlApplicantType>;
  communicationChannels?: Maybe<GqlCommunicationChannelsConfiguration>;
  config?: Maybe<GqlPartnerConfig>;
  eligibility?: Maybe<GqlEligibilityConfig>;
  email: Scalars['EmailAddress']['output'];
  externalId: Scalars['NonEmptyString']['output'];
  features: Array<GqlFeatureSetting>;
  funds: Array<GqlFund>;
  gleanInviteToken?: Maybe<Scalars['NonEmptyString']['output']>;
  id: Scalars['UUID']['output'];
  mailingAddress?: Maybe<GqlAddress>;
  name: Scalars['NonEmptyString']['output'];
  parent?: Maybe<GqlPartner>;
  phone?: Maybe<Scalars['PhoneNumber']['output']>;
  programs: Array<GqlProgram>;
  roles: Array<GqlPortalRole>;
  savedViews?: Maybe<Array<GqlSavedView>>;
  tagAutomations: Array<GqlTagAutomation>;
  tags: Array<GqlTag>;
  whitelabeling?: Maybe<GqlPartnerWhitelabeling>;
};

export type GqlPartnerConfig = {
  __typename?: 'PartnerConfig';
  applicantTypeRoles?: Maybe<GqlApplicantRolesConfig>;
  identity?: Maybe<GqlIdentityConfig>;
};

export type GqlPartnerFilter = {
  externalId: Scalars['NonEmptyString']['input'];
};

export type GqlPartnerIncident = {
  __typename?: 'PartnerIncident';
  id: Scalars['UUID']['output'];
  incidentId: Scalars['UUID']['output'];
  partnerId: Scalars['UUID']['output'];
};

export type GqlPartnerIncidentMutations = {
  __typename?: 'PartnerIncidentMutations';
  create: GqlIncidentMutationResponse;
  remove: GqlIncidentMutationResponse;
};


export type GqlPartnerIncidentMutationsCreateArgs = {
  input: GqlCreateIncidentInput;
};


export type GqlPartnerIncidentMutationsRemoveArgs = {
  input: GqlRemoveIncidentInput;
};

export type GqlPartnerPage = {
  __typename?: 'PartnerPage';
  pageInfo: GqlPageInfo;
  partners: Array<GqlPartner>;
};

export type GqlPartnerWhitelabeling = {
  __typename?: 'PartnerWhitelabeling';
  brandColor: Scalars['String']['output'];
  favicon: Scalars['String']['output'];
  id: Scalars['UUID']['output'];
  logo: Scalars['String']['output'];
  platformName: Scalars['String']['output'];
};

export type GqlPayee = GqlUser | GqlVendor;

export enum GqlPayeeType {
  User = 'User',
  Vendor = 'Vendor'
}

export type GqlPayment = {
  __typename?: 'Payment';
  amount: Scalars['NonNegativeInt']['output'];
  completedAt?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  displayId: Scalars['String']['output'];
  fulfillment: GqlFulfillment;
  id: Scalars['UUID']['output'];
  initiatedAt?: Maybe<Scalars['DateTime']['output']>;
  mailingAddress?: Maybe<GqlPaymentMailingAddress>;
  mailingAddressType?: Maybe<GqlMailingAddressType>;
  method?: Maybe<GqlPaymentMethod>;
  note?: Maybe<Scalars['NonEmptyString']['output']>;
  payee?: Maybe<GqlPayee>;
  payeeType?: Maybe<GqlPayeeType>;
  scheduledFor?: Maybe<Scalars['DateTime']['output']>;
  status: GqlPaymentStatus;
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlPaymentInput = {
  accountNumber?: InputMaybe<Scalars['String']['input']>;
  amount: Scalars['NonNegativeInt']['input'];
  benefit?: InputMaybe<GqlBenefitType>;
  billingCode?: InputMaybe<Scalars['String']['input']>;
  checkNumber?: InputMaybe<Scalars['String']['input']>;
  completedAt?: InputMaybe<Scalars['DateTime']['input']>;
  count?: InputMaybe<Scalars['PositiveInt']['input']>;
  endDate?: InputMaybe<Scalars['Date']['input']>;
  expenseType?: InputMaybe<GqlExpenseType>;
  fundId: Scalars['UUID']['input'];
  mailingAddress?: InputMaybe<GqlCreateAddressInput>;
  mailingAddressType?: InputMaybe<GqlMailingAddressType>;
  method?: InputMaybe<GqlPaymentMethod>;
  months?: InputMaybe<Array<GqlInputMonth>>;
  note?: InputMaybe<Scalars['NonEmptyString']['input']>;
  pattern?: InputMaybe<GqlPaymentSchedule>;
  payeeId: Scalars['UUID']['input'];
  payeeType: GqlPayeeType;
  serviceDate?: InputMaybe<Scalars['Date']['input']>;
  start?: InputMaybe<Scalars['Date']['input']>;
  startDate?: InputMaybe<Scalars['Date']['input']>;
  status?: InputMaybe<GqlPaymentStatus>;
  type?: InputMaybe<GqlSubBenefitType>;
  utilityType?: InputMaybe<GqlUtilityType>;
};

export type GqlPaymentMailingAddress = {
  __typename?: 'PaymentMailingAddress';
  addressLine1?: Maybe<Scalars['String']['output']>;
  addressLine2?: Maybe<Scalars['String']['output']>;
  careOf?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export enum GqlPaymentMethod {
  Ach = 'ach',
  Check = 'check',
  External = 'external',
  PhysicalCard = 'physicalCard',
  VirtualCard = 'virtualCard',
  Zelle = 'zelle'
}

export type GqlPaymentMutationResponse = {
  __typename?: 'PaymentMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlPayment>;
};

export type GqlPaymentMutations = {
  __typename?: 'PaymentMutations';
  create: GqlPaymentMutationResponse;
  remove: GqlPaymentMutationResponse;
  update: GqlPaymentMutationResponse;
};


export type GqlPaymentMutationsCreateArgs = {
  input: GqlCreatePaymentInput;
};


export type GqlPaymentMutationsRemoveArgs = {
  input: GqlRemovePaymentInput;
};


export type GqlPaymentMutationsUpdateArgs = {
  input: GqlUpdatePaymentInput;
};

export type GqlPaymentPage = {
  __typename?: 'PaymentPage';
  pageInfo: GqlPageInfo;
  payments: Array<GqlPayment>;
};

export type GqlPaymentPattern = {
  __typename?: 'PaymentPattern';
  amount: Scalars['PositiveInt']['output'];
  count: Scalars['PositiveInt']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  pattern: GqlPaymentSchedule;
  start?: Maybe<Scalars['DateTime']['output']>;
  totalAmount: Scalars['PositiveInt']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export enum GqlPaymentSchedule {
  BiWeekly = 'biWeekly',
  Monthly = 'monthly',
  OneTime = 'oneTime',
  SemiMonthly = 'semiMonthly',
  Weekly = 'weekly'
}

export type GqlPaymentSort = {
  column: GqlPaymentSortColumn;
  direction: GqlSortDirection;
};

export enum GqlPaymentSortColumn {
  CheckDate = 'CheckDate'
}

export enum GqlPaymentStatus {
  Authorized = 'authorized',
  Canceled = 'canceled',
  Failed = 'failed',
  Initiated = 'initiated',
  Pending = 'pending',
  Success = 'success'
}

export type GqlPaymentsConfig = {
  __typename?: 'PaymentsConfig';
  recurring?: Maybe<GqlRecurringPaymentConfig>;
};

export type GqlPinDocumentInput = {
  id: Scalars['UUID']['input'];
  pinned: Scalars['Boolean']['input'];
};

export enum GqlPortalRole {
  Applicant = 'applicant',
  Manager = 'manager',
  Standard = 'standard',
  StandardPayment = 'standard_payment',
  ViewOnly = 'view_only'
}

export type GqlPrediction = {
  __typename?: 'Prediction';
  createdAt: Scalars['DateTime']['output'];
  feedback: Array<GqlFeedback>;
  id: Scalars['UUID']['output'];
  label: GqlActiveLabel;
  probability: Scalars['Float']['output'];
};

export type GqlPresetGuestTokenInput = {
  dashboardId: Scalars['String']['input'];
  partnerId: Scalars['String']['input'];
  workspaceId?: InputMaybe<Scalars['String']['input']>;
};

export type GqlPresetMutations = {
  __typename?: 'PresetMutations';
  exportTableToCSV: Scalars['String']['output'];
  generatePresetGuestToken: Scalars['String']['output'];
};


export type GqlPresetMutationsExportTableToCsvArgs = {
  input: GqlExportTableToCsvInput;
};


export type GqlPresetMutationsGeneratePresetGuestTokenArgs = {
  input: GqlPresetGuestTokenInput;
};

export type GqlProfileAnswer = {
  __typename?: 'ProfileAnswer';
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  key: Scalars['NonEmptyString']['output'];
  profile: GqlApplicantProfile;
  value?: Maybe<Scalars['String']['output']>;
};

export type GqlProfileKey = {
  __typename?: 'ProfileKey';
  key: Scalars['NonEmptyString']['output'];
  label?: Maybe<Scalars['String']['output']>;
  type: Scalars['NonEmptyString']['output'];
};

export type GqlProgram = {
  __typename?: 'Program';
  applicantTypes: Array<GqlProgramApplicantType>;
  /** @deprecated Use program.applicationConfigurations instead */
  applicationConfiguration?: Maybe<GqlApplicationConfiguration>;
  applicationConfigurations: Array<GqlProgramApplicationConfiguration>;
  config: GqlProgramConfig;
  documentLabels: Array<GqlActiveLabel>;
  documents?: Maybe<Array<GqlProgramDocument>>;
  features: Array<Maybe<GqlFeatureSetting>>;
  funds: Array<GqlFund>;
  heroImage?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  name: Scalars['String']['output'];
  notifications?: Maybe<Array<GqlNotificationConfig>>;
  outcomes: Array<GqlOutcome>;
  services: Array<GqlService>;
  stats: GqlProgramStats;
  status: GqlProgramStatus;
  verificationConfigurations: Array<GqlVerificationConfiguration>;
  workflow: GqlWorkflow;
  workflowSummary: GqlWorkflowSummary;
};


export type GqlProgramWorkflowSummaryArgs = {
  filter?: InputMaybe<GqlWorkflowSummaryFilter>;
};

export type GqlProgramApplicantType = {
  __typename?: 'ProgramApplicantType';
  applicantType: GqlApplicantType;
  id: Scalars['UUID']['output'];
  nameOverride?: Maybe<Scalars['NonEmptyString']['output']>;
};

export type GqlProgramApplicationConfiguration = {
  __typename?: 'ProgramApplicationConfiguration';
  applicantTypeId?: Maybe<Scalars['UUID']['output']>;
  configuration: GqlApplicationConfiguration;
  configurationId: Scalars['UUID']['output'];
  programId: Scalars['UUID']['output'];
};

export type GqlProgramConfig = {
  __typename?: 'ProgramConfig';
  applicationConfiguration?: Maybe<Scalars['String']['output']>;
  applicationReviewFields?: Maybe<Array<GqlApplicationConfigReviewFields>>;
  mailingAddressOverride?: Maybe<GqlAddress>;
  maxFundingAmount?: Maybe<Scalars['NonNegativeInt']['output']>;
  nameMatchingEnabled?: Maybe<Scalars['Boolean']['output']>;
  payments?: Maybe<GqlPaymentsConfig>;
  programContext?: Maybe<GqlProgramContext>;
  reapplicationRules?: Maybe<Array<GqlReapplicationRules>>;
  rulesEvaluationEnabled?: Maybe<Scalars['Boolean']['output']>;
  sortOrder?: Maybe<Scalars['Int']['output']>;
  /** @deprecated Use program.verificationConfigurations instead */
  verification?: Maybe<GqlVerificationConfig>;
};

export type GqlProgramContext = {
  __typename?: 'ProgramContext';
  description: Scalars['String']['output'];
  link?: Maybe<Scalars['String']['output']>;
};

export type GqlProgramContextInput = {
  description: Scalars['String']['input'];
  link?: InputMaybe<Scalars['String']['input']>;
};

export type GqlProgramDocument = {
  __typename?: 'ProgramDocument';
  document?: Maybe<GqlDocument>;
  id: Scalars['UUID']['output'];
  status?: Maybe<GqlProgramDocumentStatus>;
};

export enum GqlProgramDocumentStatus {
  Completed = 'Completed',
  Failed = 'Failed',
  InProgress = 'InProgress'
}

export type GqlProgramFilter = {
  id: Scalars['UUID']['input'];
};

export type GqlProgramFundStats = {
  __typename?: 'ProgramFundStats';
  awardedBalance: Scalars['BigInt']['output'];
  fundId: Scalars['UUID']['output'];
  obligatedBalance: Scalars['BigInt']['output'];
  paymentCount?: Maybe<Scalars['Int']['output']>;
  programId: Scalars['UUID']['output'];
};

export type GqlProgramMutationResponse = {
  __typename?: 'ProgramMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlProgram>;
};

export type GqlProgramMutations = {
  __typename?: 'ProgramMutations';
  addProgramFund: GqlProgramMutationResponse;
  create: GqlProgramMutationResponse;
  removeProgramFund: GqlProgramMutationResponse;
  update: GqlProgramsMutationResponse;
  uploadVerificationFile: GqlProgramMutationResponse;
  upsertLookupConfig: GqlProgramMutationResponse;
};


export type GqlProgramMutationsAddProgramFundArgs = {
  input?: InputMaybe<GqlAddProgramFundInput>;
};


export type GqlProgramMutationsCreateArgs = {
  input?: InputMaybe<GqlCreateProgramInput>;
};


export type GqlProgramMutationsRemoveProgramFundArgs = {
  input?: InputMaybe<GqlRemoveProgramFundInput>;
};


export type GqlProgramMutationsUpdateArgs = {
  input: Array<GqlUpdateProgramInput>;
};


export type GqlProgramMutationsUploadVerificationFileArgs = {
  input: GqlUploadVerificationFileInput;
};


export type GqlProgramMutationsUpsertLookupConfigArgs = {
  input: GqlUpsertLookupConfigInput;
};

export type GqlProgramPage = {
  __typename?: 'ProgramPage';
  pageInfo: GqlPageInfo;
  programs: Array<GqlProgram>;
};

export type GqlProgramReferral = {
  __typename?: 'ProgramReferral';
  admin: GqlAdmin;
  createdAt: Scalars['DateTime']['output'];
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['UUID']['output'];
  program: GqlProgram;
  updatedAt: Scalars['DateTime']['output'];
  user: GqlUser;
};

export type GqlProgramReferralMutationResponse = {
  __typename?: 'ProgramReferralMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlProgramReferral>;
};

export type GqlProgramReferralMutations = {
  __typename?: 'ProgramReferralMutations';
  create: GqlProgramReferralMutationResponse;
  createBulkProgramReferral: GqlBulkUserMutationResponse;
};


export type GqlProgramReferralMutationsCreateArgs = {
  input: GqlCreateReferralInput;
};


export type GqlProgramReferralMutationsCreateBulkProgramReferralArgs = {
  input: GqlCreateBulkProgramReferralInput;
};

export type GqlProgramStats = {
  __typename?: 'ProgramStats';
  awardedBalance: Scalars['BigInt']['output'];
  caseCounts: GqlCaseCounts;
  id: Scalars['UUID']['output'];
  programFundStats: Array<GqlProgramFundStats>;
};

export enum GqlProgramStatus {
  Closed = 'Closed',
  Open = 'Open',
  ReferralOnly = 'ReferralOnly'
}

export type GqlProgramsMutationResponse = {
  __typename?: 'ProgramsMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<Array<GqlProgram>>;
};

export type GqlQuery = {
  __typename?: 'Query';
  accessRequests: GqlAccessRequestPage;
  admins: GqlAdminPage;
  aggregateApplication: GqlAggregateApplication;
  aggregatePayment: GqlAggregatePayment;
  applicantsReferred: GqlAggregateApplicantsReferred;
  applicationSubmission?: Maybe<GqlApplicationSubmission>;
  applications: GqlApplicationPage;
  /**
   * Fetch a single case by its ID. Requires the caller to belong to the same
   * partner as the case's program. When used with an API key, the key must be
   * issued for the partner that owns the case.
   *
   * This field is **read-only** and returns only the Case object. Clients may
   * request whichever sub-fields they need (e.g. `status`).
   */
  case: GqlCase;
  cases: GqlCasePage;
  changelogs: Array<GqlChangelog>;
  currentUser?: Maybe<GqlUser>;
  formDefinition?: Maybe<GqlFormDefinition>;
  formDefinitionByProgramAndApplicantType?: Maybe<GqlFormDefinition>;
  formDefinitionsByProgram: Array<GqlFormDefinition>;
  fulfillments: GqlFulfillmentPage;
  funds: GqlFundPage;
  hasAccess: GqlHasAccessResponse;
  incidentMessages: GqlIncidentMessagePage;
  incomeLimitAreas: GqlIncomeLimitAreaPage;
  partners: GqlPartnerPage;
  payments: GqlPaymentPage;
  programs: GqlProgramPage;
  ruleset?: Maybe<GqlRuleset>;
  rulesets: GqlRulesetPage;
  search: GqlSearchPage;
  tagAutomations: Array<GqlTagAutomation>;
  users: GqlUserPage;
  vendorTypes: Array<GqlVendorType>;
  vendors: GqlVendorPage;
};


export type GqlQueryAccessRequestsArgs = {
  filter?: InputMaybe<GqlAccessRequestFilter>;
  pagination?: InputMaybe<GqlOffsetPagination>;
};


export type GqlQueryAdminsArgs = {
  pagination?: InputMaybe<GqlOffsetPagination>;
};


export type GqlQueryAggregateApplicationArgs = {
  filter?: InputMaybe<GqlAggregateApplicationFilter>;
};


export type GqlQueryAggregatePaymentArgs = {
  filter?: InputMaybe<GqlAggregatePaymentFilter>;
};


export type GqlQueryApplicationSubmissionArgs = {
  id: Scalars['UUID']['input'];
};


export type GqlQueryApplicationsArgs = {
  filter?: InputMaybe<GqlApplicationFilter>;
  pagination?: InputMaybe<GqlCursorPagination>;
};


export type GqlQueryCaseArgs = {
  id: Scalars['UUID']['input'];
};


export type GqlQueryCasesArgs = {
  filter?: InputMaybe<GqlCaseFilter>;
  pagination?: InputMaybe<GqlOffsetPagination>;
  sort?: InputMaybe<GqlCaseSort>;
};


export type GqlQueryFormDefinitionArgs = {
  id: Scalars['UUID']['input'];
  includeInactive?: InputMaybe<Scalars['Boolean']['input']>;
};


export type GqlQueryFormDefinitionByProgramAndApplicantTypeArgs = {
  applicantTypeId: Scalars['UUID']['input'];
  includeInactive?: InputMaybe<Scalars['Boolean']['input']>;
  programId: Scalars['UUID']['input'];
};


export type GqlQueryFormDefinitionsByProgramArgs = {
  includeInactive?: InputMaybe<Scalars['Boolean']['input']>;
  programId: Scalars['UUID']['input'];
};


export type GqlQueryFulfillmentsArgs = {
  filter?: InputMaybe<GqlFulfillmentFilter>;
  pagination?: InputMaybe<GqlOffsetPagination>;
};


export type GqlQueryFundsArgs = {
  filter?: InputMaybe<GqlFundFilter>;
  pagination?: InputMaybe<GqlOffsetPagination>;
  sort?: InputMaybe<GqlFundSort>;
};


export type GqlQueryHasAccessArgs = {
  input: GqlHasAccessInput;
};


export type GqlQueryIncidentMessagesArgs = {
  filter?: InputMaybe<GqlIncidentMessageFilter>;
};


export type GqlQueryIncomeLimitAreasArgs = {
  filter?: InputMaybe<GqlIncomeLimitAreaFilter>;
  pagination?: InputMaybe<GqlCursorPagination>;
};


export type GqlQueryPartnersArgs = {
  filter?: InputMaybe<GqlPartnerFilter>;
  pagination?: InputMaybe<GqlCursorPagination>;
};


export type GqlQueryPaymentsArgs = {
  pagination?: InputMaybe<GqlOffsetPagination>;
  sort?: InputMaybe<GqlPaymentSort>;
  vendorId: Scalars['UUID']['input'];
};


export type GqlQueryProgramsArgs = {
  filter?: InputMaybe<GqlProgramFilter>;
  pagination?: InputMaybe<GqlCursorPagination>;
};


export type GqlQueryRulesetArgs = {
  name?: InputMaybe<Scalars['String']['input']>;
  programId: Scalars['ID']['input'];
};


export type GqlQueryRulesetsArgs = {
  name?: InputMaybe<Scalars['String']['input']>;
  pagination?: InputMaybe<GqlOffsetPagination>;
  programId: Scalars['UUID']['input'];
  sort?: InputMaybe<GqlRulesetSort>;
};


export type GqlQuerySearchArgs = {
  input: GqlSearchInput;
  pagination?: InputMaybe<GqlCursorPagination>;
  sort?: InputMaybe<GqlSearchSort>;
};


export type GqlQueryUsersArgs = {
  filter?: InputMaybe<GqlUserFilter>;
  pagination?: InputMaybe<GqlOffsetPagination>;
  sort?: InputMaybe<GqlUserSort>;
};


export type GqlQueryVendorsArgs = {
  filter?: InputMaybe<GqlVendorFilter>;
  pagination?: InputMaybe<GqlOffsetPagination>;
  sort?: InputMaybe<GqlVendorSort>;
};

export type GqlQuestionGroupResponse = {
  __typename?: 'QuestionGroupResponse';
  key: Scalars['String']['output'];
  name: Scalars['String']['output'];
  questions: Array<GqlQuestionResponse>;
};

export type GqlQuestionResponse = {
  __typename?: 'QuestionResponse';
  copy: Scalars['String']['output'];
  displayName?: Maybe<Scalars['String']['output']>;
  fields: Array<GqlFieldResponse>;
  key: Scalars['String']['output'];
};

export type GqlRadioListField = {
  __typename?: 'RadioListField';
  copy?: Maybe<Scalars['JSON']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  options: Array<GqlOptionLabelValue>;
  type: GqlApplicationFieldType;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlReapplicationRules = {
  __typename?: 'ReapplicationRules';
  type: GqlReapplicationValidationType;
  unit?: Maybe<Scalars['String']['output']>;
  value: Scalars['NonNegativeInt']['output'];
};

export enum GqlReapplicationValidationType {
  MaxNumberOfApplications = 'MaxNumberOfApplications',
  TimeSinceDecision = 'TimeSinceDecision'
}

export type GqlRecurringPaymentConfig = {
  __typename?: 'RecurringPaymentConfig';
  amount?: Maybe<Scalars['PositiveInt']['output']>;
  count?: Maybe<Scalars['PositiveInt']['output']>;
  /** @deprecated Replaced with pattern */
  frequency: GqlPaymentSchedule;
  pattern?: Maybe<GqlPaymentSchedule>;
  start?: Maybe<Scalars['DateTime']['output']>;
  /** @deprecated Replaced with start */
  startDate?: Maybe<Scalars['DateTime']['output']>;
  totalAmount?: Maybe<Scalars['PositiveInt']['output']>;
};

export type GqlRemoveDocumentsInput = {
  documentIds: Array<Scalars['UUID']['input']>;
  id: Scalars['UUID']['input'];
};

export type GqlRemoveEnrollmentOutcomeInput = {
  identifier: GqlEnrollmentIdentifier;
  outcomeId: Scalars['UUID']['input'];
};

export type GqlRemoveEnrollmentServiceInput = {
  identifier: GqlEnrollmentIdentifier;
  serviceId: Scalars['UUID']['input'];
};

export type GqlRemoveFundInput = {
  id: Scalars['UUID']['input'];
};

export type GqlRemoveIncidentInput = {
  incidentId: Scalars['UUID']['input'];
};

export type GqlRemovePaymentInput = {
  id: Scalars['UUID']['input'];
};

export type GqlRemoveProgramFundInput = {
  fundId: Scalars['UUID']['input'];
  programId: Scalars['UUID']['input'];
};

export type GqlRemoveVendorDocumentsInput = {
  documentIds: Array<Scalars['UUID']['input']>;
  id: Scalars['UUID']['input'];
};

export type GqlResolvedEntities = {
  __typename?: 'ResolvedEntities';
  applicantNameConsistency: GqlApplicantNameConsistency;
  caseId: Scalars['UUID']['output'];
};

export enum GqlResolverStatus {
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  Pending = 'PENDING'
}

export type GqlResponseMetadata = {
  __typename?: 'ResponseMetadata';
  errors?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  id?: Maybe<Scalars['UUID']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status: Scalars['NonNegativeInt']['output'];
};

export type GqlReviewAccessRequestInput = {
  approved: Scalars['Boolean']['input'];
  detail?: InputMaybe<Scalars['String']['input']>;
  requestId: Scalars['UUID']['input'];
};

export type GqlRoleConfig = {
  __typename?: 'RoleConfig';
  description?: Maybe<Scalars['String']['output']>;
};

export type GqlRuleset = {
  __typename?: 'Ruleset';
  author?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['UUID']['output'];
  model: Scalars['String']['output'];
  name: Scalars['String']['output'];
  partnerId: Scalars['UUID']['output'];
  programId: Scalars['UUID']['output'];
  status: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
  version: Scalars['Int']['output'];
};

export type GqlRulesetPage = {
  __typename?: 'RulesetPage';
  nodes: Array<GqlRuleset>;
  pageInfo: GqlPageInfo;
};

export type GqlRulesetSort = {
  column: GqlRulesetSortColumn;
  direction: GqlSortDirection;
};

export enum GqlRulesetSortColumn {
  Author = 'Author',
  CreatedAt = 'CreatedAt',
  UpdatedAt = 'UpdatedAt',
  Version = 'Version'
}

export type GqlSamlConfig = {
  __typename?: 'SAMLConfig';
  /** @deprecated unnecessary */
  callbackURL: Scalars['NonEmptyString']['output'];
  copy?: Maybe<Scalars['NonEmptyString']['output']>;
  method: Scalars['NonEmptyString']['output'];
  providerId: Scalars['NonEmptyString']['output'];
  /** @deprecated unnecessary */
  ssoURL: Scalars['NonEmptyString']['output'];
};

export type GqlSaveUserBankAccountInput = {
  bankAccount: GqlBankAccountInput;
  id: Scalars['UUID']['input'];
};

export type GqlSavedView = {
  __typename?: 'SavedView';
  author: GqlUser;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  filters: Scalars['JSON']['output'];
  id: Scalars['UUID']['output'];
  isPublic?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['NonEmptyString']['output'];
  partner: GqlPartner;
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlSavedViewMutationResponse = {
  __typename?: 'SavedViewMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlSavedView>;
};

export type GqlSavedViewMutations = {
  __typename?: 'SavedViewMutations';
  create: GqlSavedViewMutationResponse;
  delete: GqlSavedViewMutationResponse;
  update: GqlSavedViewMutationResponse;
};


export type GqlSavedViewMutationsCreateArgs = {
  input: GqlCreateSavedViewInput;
};


export type GqlSavedViewMutationsDeleteArgs = {
  input: GqlDeleteSavedViewInput;
};


export type GqlSavedViewMutationsUpdateArgs = {
  input: GqlUpdateSavedViewInput;
};

export enum GqlScheduleType {
  Onetime = 'onetime',
  Recurring = 'recurring'
}

export type GqlSearchFilter = {
  answerKey?: InputMaybe<Scalars['NonEmptyString']['input']>;
  answerReviews?: InputMaybe<Array<GqlApplicationAnswerReviewStatus>>;
  answerValue?: InputMaybe<Scalars['NonEmptyString']['input']>;
  applicantTypeId?: InputMaybe<Array<Scalars['UUID']['input']>>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  assigneeId?: InputMaybe<Array<Scalars['AssigneeId']['input']>>;
  caseParticipantStatus?: InputMaybe<Array<GqlCaseParticipantStatus>>;
  caseStatus?: InputMaybe<Array<GqlCaseStatus>>;
  documentTagTypes?: InputMaybe<Array<Scalars['NonEmptyString']['input']>>;
  eligibility?: InputMaybe<Array<GqlEligibilityStatus>>;
  expedited?: InputMaybe<Scalars['Boolean']['input']>;
  newApplicant?: InputMaybe<Scalars['Boolean']['input']>;
  paymentStatus?: InputMaybe<Array<GqlPaymentStatus>>;
  programId?: InputMaybe<Scalars['UUID']['input']>;
  referred?: InputMaybe<Scalars['Boolean']['input']>;
  tags?: InputMaybe<Array<Scalars['UUID']['input']>>;
  verified?: InputMaybe<Array<GqlVerificationStatus>>;
};

export type GqlSearchInput = {
  deepSearch?: InputMaybe<Scalars['Boolean']['input']>;
  filters?: InputMaybe<GqlSearchFilter>;
  index: GqlIndex;
  legacyFilters?: InputMaybe<GqlCaseFilter>;
  search?: InputMaybe<Scalars['String']['input']>;
  searchCategories?: InputMaybe<Array<Scalars['NonEmptyString']['input']>>;
  sort?: InputMaybe<GqlSearchSort>;
};

export type GqlSearchPage = {
  __typename?: 'SearchPage';
  nodes: Array<GqlCaseApplicationsDocument>;
  pageInfo: GqlCursorPageInfo;
};

export type GqlSearchSort = {
  column: GqlSearchSortColumn;
  direction: GqlSortDirection;
};

export enum GqlSearchSortColumn {
  ApplicantName = 'ApplicantName',
  Assignee = 'Assignee',
  AwardedAmount = 'AwardedAmount',
  CreatedAt = 'CreatedAt',
  Eligibility = 'Eligibility',
  ProgramName = 'ProgramName',
  PropertyAddress = 'PropertyAddress',
  Status = 'Status',
  SubmittedAt = 'SubmittedAt',
  UpdatedAt = 'UpdatedAt'
}

export type GqlSectionResponse = {
  __typename?: 'SectionResponse';
  key: Scalars['String']['output'];
  name: Scalars['String']['output'];
  questionGroups: Array<GqlQuestionGroupResponse>;
};

export type GqlSendIdentityVerificationEmailInput = {
  email: Scalars['NonEmptyString']['input'];
  partnerId: Scalars['UUID']['input'];
  tenantId: Scalars['NonEmptyString']['input'];
};

export type GqlSendMagicLinkInput = {
  continueUrl?: InputMaybe<Scalars['NonEmptyString']['input']>;
  email: Scalars['NonEmptyString']['input'];
  partnerId: Scalars['UUID']['input'];
  tenantId: Scalars['NonEmptyString']['input'];
  timezone?: InputMaybe<Scalars['NonEmptyString']['input']>;
};

export type GqlService = {
  __typename?: 'Service';
  children: Array<GqlService>;
  id: Scalars['UUID']['output'];
  name: Scalars['NonEmptyString']['output'];
  parent?: Maybe<GqlService>;
};

export enum GqlSimilarity {
  Exact = 'EXACT',
  NotSimilar = 'NOT_SIMILAR',
  Similar = 'SIMILAR',
  Unknown = 'UNKNOWN'
}

export type GqlSmsTemplateContent = {
  __typename?: 'SmsTemplateContent';
  link?: Maybe<GqlNotificationAction>;
  postText?: Maybe<Scalars['String']['output']>;
  preText: Scalars['String']['output'];
};

export enum GqlSortDirection {
  Ascending = 'Ascending',
  Descending = 'Descending'
}

export type GqlStatusFilters = {
  answerReviews?: InputMaybe<Array<Scalars['String']['input']>>;
  applicantTypeId?: InputMaybe<Array<Scalars['UUID']['input']>>;
  archived?: InputMaybe<Scalars['Boolean']['input']>;
  caseParticipantStatus?: InputMaybe<Array<GqlCaseParticipantStatus>>;
  documentTagTypes?: InputMaybe<Array<Scalars['String']['input']>>;
  eligibility?: InputMaybe<Array<GqlEligibilityStatus>>;
  expedited?: InputMaybe<Scalars['Boolean']['input']>;
  newApplicant?: InputMaybe<Scalars['Boolean']['input']>;
  paymentStatus?: InputMaybe<Array<GqlPaymentStatus>>;
  referred?: InputMaybe<Scalars['Boolean']['input']>;
  tags?: InputMaybe<Array<Scalars['UUID']['input']>>;
  verified?: InputMaybe<Array<GqlVerificationStatus>>;
  versionCount?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type GqlStatusOverride = {
  __typename?: 'StatusOverride';
  action?: Maybe<Scalars['NonEmptyString']['output']>;
  label?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['NonEmptyString']['output']>;
  referralMessage?: Maybe<Scalars['NonEmptyString']['output']>;
  status: GqlApplicationStatus;
};

export enum GqlSubBenefitType {
  Arrearage = 'Arrearage',
  Prospective = 'Prospective'
}

export type GqlSubmissionBullet = {
  __typename?: 'SubmissionBullet';
  description?: Maybe<Scalars['NonEmptyString']['output']>;
  icon: Scalars['NonEmptyString']['output'];
  key: Scalars['NonEmptyString']['output'];
  title: Scalars['NonEmptyString']['output'];
};

export type GqlSubmissionOverrides = {
  __typename?: 'SubmissionOverrides';
  bullets: Array<GqlSubmissionBullet>;
  title: Scalars['NonEmptyString']['output'];
};

export type GqlSubmitApplicationInput = {
  id: Scalars['UUID']['input'];
};

export type GqlSubmitPredictionFeedbackInput = {
  accurate: GqlYesNoUnsure;
  id: Scalars['UUID']['input'];
  preferredLabelId?: InputMaybe<Scalars['UUID']['input']>;
};

export type GqlTag = {
  __typename?: 'Tag';
  count?: Maybe<Scalars['Int']['output']>;
  id: Scalars['UUID']['output'];
  name: Scalars['NonEmptyString']['output'];
  partner: GqlPartner;
};

export type GqlTagAutomation = {
  __typename?: 'TagAutomation';
  actionType: GqlTagAutomationActionType;
  createdAt: Scalars['DateTime']['output'];
  criteria?: Maybe<Scalars['JSON']['output']>;
  deactivatedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['UUID']['output'];
  partner?: Maybe<GqlPartner>;
  partnerId: Scalars['UUID']['output'];
  program?: Maybe<GqlProgram>;
  programId?: Maybe<Scalars['UUID']['output']>;
  tags: Array<GqlTag>;
  triggerType: GqlTagAutomationTriggerType;
  updatedAt: Scalars['DateTime']['output'];
};

export enum GqlTagAutomationActionType {
  AddTag = 'ADD_TAG'
}

export type GqlTagAutomationMutationResponse = {
  __typename?: 'TagAutomationMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlTagAutomation>;
};

export enum GqlTagAutomationTriggerType {
  ApplicationSubmitted = 'APPLICATION_SUBMITTED'
}

export type GqlTagAutomationsMutations = {
  __typename?: 'TagAutomationsMutations';
  create: GqlTagAutomationMutationResponse;
  delete: GqlTagAutomationMutationResponse;
  update: GqlTagAutomationMutationResponse;
};


export type GqlTagAutomationsMutationsCreateArgs = {
  input: GqlCreateTagAutomationInput;
};


export type GqlTagAutomationsMutationsDeleteArgs = {
  input: GqlDeleteTagAutomationInput;
};


export type GqlTagAutomationsMutationsUpdateArgs = {
  input: GqlUpdateTagAutomationInput;
};

export type GqlTagMutationResponse = {
  __typename?: 'TagMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record: GqlTag;
};

export type GqlTagMutations = {
  __typename?: 'TagMutations';
  create: GqlTagsMutationResponse;
  delete: GqlTagsMutationResponse;
  update: GqlTagMutationResponse;
};


export type GqlTagMutationsCreateArgs = {
  input: GqlCreateTagsInput;
};


export type GqlTagMutationsDeleteArgs = {
  input: GqlDeleteTagsInput;
};


export type GqlTagMutationsUpdateArgs = {
  input: GqlUpdateTagInput;
};

export type GqlTagsMutationResponse = {
  __typename?: 'TagsMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<Array<GqlTag>>;
};

export type GqlTaxForm = {
  __typename?: 'TaxForm';
  id: Scalars['UUID']['output'];
  type: GqlTaxFormType;
  user: GqlUser;
};

export type GqlTaxFormMutationResponse = {
  __typename?: 'TaxFormMutationResponse';
  metadata: GqlResponseMetadata;
  query: GqlQuery;
  record?: Maybe<GqlUser>;
};

export enum GqlTaxFormType {
  W9 = 'W9'
}

export type GqlTemplateContent = GqlEmailTemplateContent | GqlSmsTemplateContent;

export type GqlTextField = {
  __typename?: 'TextField';
  copy?: Maybe<Scalars['JSON']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  inputType: Scalars['NonEmptyString']['output'];
  isSensitive?: Maybe<Scalars['Boolean']['output']>;
  key: Scalars['NonEmptyString']['output'];
  props?: Maybe<Scalars['JSON']['output']>;
  type: GqlApplicationFieldType;
  validation?: Maybe<GqlApplicationFieldValidation>;
};

export type GqlTransitionCaseStatusesInput = {
  assignment?: InputMaybe<GqlTransitionStatusAssignment>;
  ids: Array<Scalars['UUID']['input']>;
  reason?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<GqlCaseStatus>;
};

export type GqlTransitionStatusAssignment = {
  assigneeId?: InputMaybe<Scalars['UUID']['input']>;
};

export type GqlTypographyField = {
  __typename?: 'TypographyField';
  copy?: Maybe<Scalars['JSON']['output']>;
  dynamicLogic?: Maybe<Scalars['JSON']['output']>;
  key: Scalars['NonEmptyString']['output'];
  props?: Maybe<Scalars['JSON']['output']>;
  type: GqlApplicationFieldType;
};

export type GqlUnlinkCaseParticipantInput = {
  caseParticipantId: Scalars['UUID']['input'];
  id: Scalars['UUID']['input'];
};

export type GqlUpdateAdminInput = {
  email: Scalars['EmailAddress']['input'];
  id: Scalars['UUID']['input'];
  name?: InputMaybe<Scalars['NonEmptyString']['input']>;
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
  roles?: InputMaybe<Array<GqlPortalRole>>;
};

export type GqlUpdateApplicationInput = {
  content: GqlApplicationContentInput;
  id: Scalars['UUID']['input'];
};

export type GqlUpdateFundInput = {
  awardAmountMax?: InputMaybe<Scalars['BigInt']['input']>;
  id: Scalars['UUID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  startingBalance?: InputMaybe<Scalars['BigInt']['input']>;
};

export type GqlUpdateNoteInput = {
  content: Scalars['NonEmptyString']['input'];
  id: Scalars['UUID']['input'];
};

export type GqlUpdatePaymentInput = {
  id: Scalars['UUID']['input'];
  payment: GqlPaymentInput;
};

export type GqlUpdateProgramInput = {
  applicationReviewFields?: InputMaybe<Array<GqlApplicationConfigReviewFieldsInput>>;
  id: Scalars['UUID']['input'];
  nameMatchingEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  programContext?: InputMaybe<GqlProgramContextInput>;
  rulesEvaluationEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  status?: InputMaybe<GqlProgramStatus>;
};

export type GqlUpdateSavedViewInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  filters?: InputMaybe<Scalars['JSON']['input']>;
  id: Scalars['UUID']['input'];
  isPublic?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['NonEmptyString']['input']>;
};

export type GqlUpdateTagAutomationInput = {
  actionType?: InputMaybe<GqlTagAutomationActionType>;
  criteria?: InputMaybe<Scalars['JSON']['input']>;
  id: Scalars['UUID']['input'];
  programId?: InputMaybe<Scalars['UUID']['input']>;
  tagIds?: InputMaybe<Array<Scalars['UUID']['input']>>;
  triggerType?: InputMaybe<GqlTagAutomationTriggerType>;
};

export type GqlUpdateTagInput = {
  id: Scalars['UUID']['input'];
  name: Scalars['NonEmptyString']['input'];
};

export type GqlUpdateUserInput = {
  bankAccount?: InputMaybe<GqlBankAccountInput>;
  email?: InputMaybe<Scalars['EmailAddress']['input']>;
  id: Scalars['UUID']['input'];
  name: Scalars['NonEmptyString']['input'];
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
  secondaryEmail?: InputMaybe<Scalars['EmailAddress']['input']>;
  taxId?: InputMaybe<Scalars['NonEmptyString']['input']>;
};

export type GqlUpdateUserProfileInput = {
  applicantTypeId?: InputMaybe<Scalars['UUID']['input']>;
  bankAccount?: InputMaybe<GqlBankAccountInput>;
  communicationPreferences?: InputMaybe<GqlCommunicationPreferencesInput>;
  email?: InputMaybe<Scalars['EmailAddress']['input']>;
  mailingAddress?: InputMaybe<GqlCreateAddressInput>;
  name: Scalars['NonEmptyString']['input'];
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
  secondaryEmail?: InputMaybe<Scalars['EmailAddress']['input']>;
  taxId?: InputMaybe<Scalars['NonEmptyString']['input']>;
};

export type GqlUpdateVendorInput = {
  bankAccount?: InputMaybe<GqlBankAccountInput>;
  email?: InputMaybe<Scalars['EmailAddress']['input']>;
  externalId?: InputMaybe<Scalars['NonEmptyString']['input']>;
  files?: InputMaybe<Array<Scalars['Upload']['input']>>;
  id: Scalars['UUID']['input'];
  mailingAddress?: InputMaybe<GqlCreateAddressInput>;
  name?: InputMaybe<Scalars['NonEmptyString']['input']>;
  phone?: InputMaybe<Scalars['PhoneNumber']['input']>;
  taxId?: InputMaybe<Scalars['NonEmptyString']['input']>;
  typeIds?: InputMaybe<Array<Scalars['UUID']['input']>>;
};

export type GqlUploadDocumentsInput = {
  files: Array<Scalars['Upload']['input']>;
  id: Scalars['UUID']['input'];
};

export type GqlUploadVendorDocumentsInput = {
  files: Array<Scalars['Upload']['input']>;
  id: Scalars['UUID']['input'];
};

export type GqlUploadVerificationFileInput = {
  file: Scalars['Upload']['input'];
  id: Scalars['UUID']['input'];
};

export type GqlUpsertEnrollmentInput = {
  endDate?: InputMaybe<Scalars['Date']['input']>;
  identifier: GqlEnrollmentIdentifier;
  startDate: Scalars['Date']['input'];
};

export type GqlUpsertEnrollmentOutcomeInput = {
  identifier: GqlEnrollmentIdentifier;
  outcomeDate: Scalars['Date']['input'];
  outcomeId?: InputMaybe<Scalars['UUID']['input']>;
  outcomes: Array<Scalars['UUID']['input']>;
};

export type GqlUpsertEnrollmentServiceInput = {
  identifier: GqlEnrollmentIdentifier;
  serviceDate: Scalars['Date']['input'];
  serviceId?: InputMaybe<Scalars['UUID']['input']>;
  services: Array<Scalars['UUID']['input']>;
};

export type GqlUpsertLookupConfigInput = {
  applicantTypeId: Scalars['UUID']['input'];
  fields: Array<GqlDataLookupFieldInput>;
  id: Scalars['UUID']['input'];
};

export type GqlUser = {
  __typename?: 'User';
  admin?: Maybe<GqlAdmin>;
  aggregatePayments: GqlAggregatePayment;
  applicantProfile?: Maybe<GqlApplicantProfile>;
  applications: Array<GqlApplication>;
  bankAccount?: Maybe<GqlBankAccount>;
  communicationPreferences?: Maybe<GqlCommunicationPreferences>;
  createdAt: Scalars['DateTime']['output'];
  displayId: Scalars['String']['output'];
  documents: Array<GqlDocument>;
  email?: Maybe<Scalars['EmailAddress']['output']>;
  enrollments: Array<GqlEnrollment>;
  hasPotentialDuplicates: Scalars['Boolean']['output'];
  id: Scalars['UUID']['output'];
  legacyId?: Maybe<Scalars['String']['output']>;
  mailingAddress?: Maybe<GqlAddress>;
  name: Scalars['NonEmptyString']['output'];
  /** @deprecated Deprecated feature, need to implement in GCIP */
  newEmail?: Maybe<Scalars['EmailAddress']['output']>;
  partner: GqlPartner;
  partnerId: Scalars['UUID']['output'];
  payeeType: GqlPayeeType;
  phone?: Maybe<Scalars['PhoneNumber']['output']>;
  recentLogin?: Maybe<GqlLoginDetails>;
  referrals: Array<GqlProgramReferral>;
  savedViews?: Maybe<Array<GqlSavedView>>;
  taxForms: Array<GqlTaxForm>;
  taxId?: Maybe<Scalars['NonEmptyString']['output']>;
  validatedEmail: Scalars['Boolean']['output'];
};

export type GqlUserFilter = {
  hasMailingAddress?: InputMaybe<Scalars['Boolean']['input']>;
  hasPhone?: InputMaybe<Scalars['Boolean']['input']>;
  hasPotentialDuplicates?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['UUID']['input']>;
  legacyUserType?: InputMaybe<GqlLegacyUserType>;
  profileId?: InputMaybe<Scalars['UUID']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};

export type GqlUserMutations = {
  __typename?: 'UserMutations';
  create: GqlUserResponse;
  createW9TaxForm: GqlTaxFormMutationResponse;
  linkLegacyUser: GqlUserResponse;
  saveBankAccount: GqlUserResponse;
  update: GqlUserResponse;
  updateProfile: GqlUserResponse;
};


export type GqlUserMutationsCreateArgs = {
  input: GqlCreateUserInput;
};


export type GqlUserMutationsCreateW9TaxFormArgs = {
  input: GqlCreateW9TaxFormInput;
};


export type GqlUserMutationsLinkLegacyUserArgs = {
  input: GqlLinkLegacyUserInput;
};


export type GqlUserMutationsSaveBankAccountArgs = {
  input: GqlSaveUserBankAccountInput;
};


export type GqlUserMutationsUpdateArgs = {
  input: GqlUpdateUserInput;
};


export type GqlUserMutationsUpdateProfileArgs = {
  input: GqlUpdateUserProfileInput;
};

export type GqlUserPage = {
  __typename?: 'UserPage';
  pageInfo: GqlPageInfo;
  users: Array<GqlUser>;
};

export type GqlUserResponse = {
  __typename?: 'UserResponse';
  metadata: GqlResponseMetadata;
  record?: Maybe<GqlUser>;
};

export type GqlUserSort = {
  column: GqlUserSortColumn;
  direction: GqlSortDirection;
};

export enum GqlUserSortColumn {
  Name = 'Name',
  TotalPaid = 'TotalPaid'
}

export enum GqlUtilityType {
  Electric = 'Electric',
  Gas = 'Gas',
  Oil = 'Oil',
  Other = 'Other',
  Propane = 'Propane',
  Telephone = 'Telephone',
  WaterSewer = 'WaterSewer',
  Wood = 'Wood'
}

export type GqlVendor = {
  __typename?: 'Vendor';
  aggregatePayments: GqlAggregatePayment;
  bankAccount?: Maybe<GqlBankAccount>;
  createdAt: Scalars['DateTime']['output'];
  documents: Array<GqlDocument>;
  email?: Maybe<Scalars['EmailAddress']['output']>;
  externalId?: Maybe<Scalars['NonEmptyString']['output']>;
  id: Scalars['UUID']['output'];
  mailingAddress?: Maybe<GqlAddress>;
  name: Scalars['NonEmptyString']['output'];
  notes: Array<GqlNote>;
  payeeType: GqlPayeeType;
  payments: Array<GqlPayment>;
  phone?: Maybe<Scalars['PhoneNumber']['output']>;
  taxId?: Maybe<Scalars['NonEmptyString']['output']>;
  types: Array<GqlVendorType>;
  updatedAt: Scalars['DateTime']['output'];
};

export type GqlVendorFilter = {
  id?: InputMaybe<Scalars['UUID']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Array<Scalars['UUID']['input']>>;
};

export type GqlVendorMutationResponse = {
  __typename?: 'VendorMutationResponse';
  metadata: GqlResponseMetadata;
  record?: Maybe<GqlVendor>;
};

export type GqlVendorMutations = {
  __typename?: 'VendorMutations';
  create: GqlVendorMutationResponse;
  delete: GqlVendorMutationResponse;
  removeDocuments: GqlVendorMutationResponse;
  update: GqlVendorMutationResponse;
  uploadDocuments: GqlVendorMutationResponse;
};


export type GqlVendorMutationsCreateArgs = {
  input: GqlCreateVendorInput;
};


export type GqlVendorMutationsDeleteArgs = {
  input: GqlDeleteVendorInput;
};


export type GqlVendorMutationsRemoveDocumentsArgs = {
  input: GqlRemoveVendorDocumentsInput;
};


export type GqlVendorMutationsUpdateArgs = {
  input: GqlUpdateVendorInput;
};


export type GqlVendorMutationsUploadDocumentsArgs = {
  input: GqlUploadVendorDocumentsInput;
};

export type GqlVendorPage = {
  __typename?: 'VendorPage';
  nodes: Array<GqlVendor>;
  pageInfo: GqlPageInfo;
  /** @deprecated use 'nodes' instead */
  vendors: Array<GqlVendor>;
};

export type GqlVendorSort = {
  column: GqlVendorSortColumn;
  direction: GqlSortDirection;
};

export enum GqlVendorSortColumn {
  Name = 'Name',
  Payments = 'Payments',
  Type = 'Type'
}

export type GqlVendorType = {
  __typename?: 'VendorType';
  id: Scalars['UUID']['output'];
  name: Scalars['NonEmptyString']['output'];
};

export type GqlVerificationConfig = {
  __typename?: 'VerificationConfig';
  dataLookup?: Maybe<Array<GqlVerificationFileKey>>;
};

export type GqlVerificationConfiguration = {
  __typename?: 'VerificationConfiguration';
  dataLookup?: Maybe<GqlDataLookupConfiguration>;
  id: Scalars['UUID']['output'];
  service: GqlVerificationServiceType;
};

export type GqlVerificationDetail = {
  __typename?: 'VerificationDetail';
  confidence: Scalars['Float']['output'];
  description?: Maybe<Scalars['String']['output']>;
  diff?: Maybe<GqlVerificationDiff>;
  key: Scalars['String']['output'];
  weight: Scalars['Float']['output'];
};

export type GqlVerificationDiff = {
  __typename?: 'VerificationDiff';
  provided: Scalars['String']['output'];
  verified: Scalars['String']['output'];
};

export type GqlVerificationFileKey = {
  __typename?: 'VerificationFileKey';
  details?: Maybe<Scalars['String']['output']>;
  key?: Maybe<Scalars['String']['output']>;
  sample?: Maybe<Scalars['String']['output']>;
};

export type GqlVerificationMetadata = {
  __typename?: 'VerificationMetadata';
  key: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export enum GqlVerificationServiceType {
  DataLookup = 'DataLookup'
}

export enum GqlVerificationStatus {
  PartiallyVerified = 'PartiallyVerified',
  Unverified = 'Unverified',
  Verified = 'Verified'
}

export type GqlVerifiedPageInfo = {
  __typename?: 'VerifiedPageInfo';
  count: Scalars['NonNegativeInt']['output'];
  nextCursor?: Maybe<Scalars['NonEmptyString']['output']>;
  verifiedIds: Array<Scalars['NonEmptyString']['output']>;
};

export type GqlVerifyIdentityEmailInput = {
  tenantId: Scalars['NonEmptyString']['input'];
  token: Scalars['JWT']['input'];
};

export enum GqlW9BusinessTaxClassifications {
  CCorporation = 'CCorporation',
  Llc = 'Llc',
  Other = 'Other',
  Partnership = 'Partnership',
  SCorporation = 'SCorporation',
  SolePropietor = 'SolePropietor',
  TrustEstate = 'TrustEstate'
}

export type GqlW9FormData = {
  addressLine1: Scalars['String']['input'];
  addressLine2?: InputMaybe<Scalars['String']['input']>;
  businessTaxClassification?: InputMaybe<GqlW9BusinessTaxClassifications>;
  city: Scalars['String']['input'];
  dba?: InputMaybe<Scalars['String']['input']>;
  ein?: InputMaybe<Scalars['String']['input']>;
  exemptFATCACode?: InputMaybe<Scalars['String']['input']>;
  exemptPayeeCode?: InputMaybe<Scalars['NonNegativeInt']['input']>;
  llcBusinessTaxClassification?: InputMaybe<GqlW9LlcTaxClassifications>;
  name: Scalars['String']['input'];
  otherEntityType?: InputMaybe<Scalars['String']['input']>;
  signature: Scalars['String']['input'];
  ssn?: InputMaybe<Scalars['String']['input']>;
  state: Scalars['String']['input'];
  taxClassification?: InputMaybe<GqlW9TaxClassifications>;
  tin?: InputMaybe<GqlW9Tin>;
  zip: Scalars['String']['input'];
};

export enum GqlW9LlcTaxClassifications {
  C = 'C',
  P = 'P',
  S = 'S'
}

export enum GqlW9TaxClassifications {
  Business = 'Business',
  Individual = 'Individual'
}

export enum GqlW9Tin {
  EmployerIdentificationNumber = 'EmployerIdentificationNumber',
  SocialSecurityNumber = 'SocialSecurityNumber'
}

export type GqlWorkflow = {
  __typename?: 'Workflow';
  workflowStages: Array<GqlWorkflowStage>;
};

export type GqlWorkflowEvent = {
  __typename?: 'WorkflowEvent';
  action?: Maybe<Scalars['String']['output']>;
  /** @deprecated Use 'user' instead */
  author?: Maybe<GqlUser>;
  createdAt: Scalars['DateTime']['output'];
  details?: Maybe<Scalars['String']['output']>;
  entityId: Scalars['UUID']['output'];
  entityType?: Maybe<Scalars['String']['output']>;
  id: Scalars['UUID']['output'];
  newAssignee?: Maybe<GqlUser>;
  newValue?: Maybe<Scalars['String']['output']>;
  previousAssignee?: Maybe<GqlUser>;
  previousValue?: Maybe<Scalars['String']['output']>;
  updatedAt?: Maybe<Scalars['DateTime']['output']>;
  user?: Maybe<GqlUser>;
  userId?: Maybe<Scalars['UUID']['output']>;
};

export type GqlWorkflowNotification = {
  __typename?: 'WorkflowNotification';
  template: GqlNotificationTemplate;
};

export type GqlWorkflowStage = {
  __typename?: 'WorkflowStage';
  description: Scalars['String']['output'];
  order: Scalars['Int']['output'];
  status: GqlCaseStatus;
  workflowNotifications: Array<GqlWorkflowNotification>;
};

export type GqlWorkflowSummary = {
  __typename?: 'WorkflowSummary';
  casesApproved: Scalars['Int']['output'];
  casesCertified: Scalars['Int']['output'];
  casesDenied: Scalars['Int']['output'];
  casesReviewed: Scalars['Int']['output'];
};

export type GqlWorkflowSummaryFilter = {
  createdAt?: InputMaybe<GqlDateRange>;
};

export enum GqlYesNoUnsure {
  No = 'no',
  Unsure = 'unsure',
  Yes = 'yes'
}



export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

/** Mapping of union types */
export type GqlResolversUnionTypes<_RefType extends Record<string, unknown>> = {
  ApplicationField: ( Partial<GqlAddressField> ) | ( Partial<GqlCalculatedField> ) | ( Partial<GqlCheckboxField> ) | ( Partial<GqlCheckboxGroupField> ) | ( Partial<Omit<GqlComplexField, 'subFields'> & { subFields: Array<_RefType['ApplicationField']> }> ) | ( Partial<GqlDateField> ) | ( Partial<GqlDocumentField> ) | ( Partial<GqlDropdownField> ) | ( Partial<GqlRadioListField> ) | ( Partial<GqlTextField> ) | ( Partial<GqlTypographyField> );
  Payee: ( Partial<Omit<GqlUser, 'admin' | 'aggregatePayments' | 'applicantProfile' | 'applications' | 'documents' | 'enrollments' | 'partner' | 'referrals' | 'savedViews' | 'taxForms'> & { admin?: Maybe<_RefType['Admin']>, aggregatePayments: _RefType['AggregatePayment'], applicantProfile?: Maybe<_RefType['ApplicantProfile']>, applications: Array<_RefType['Application']>, documents: Array<_RefType['Document']>, enrollments: Array<_RefType['Enrollment']>, partner: _RefType['Partner'], referrals: Array<_RefType['ProgramReferral']>, savedViews?: Maybe<Array<_RefType['SavedView']>>, taxForms: Array<_RefType['TaxForm']> }> ) | ( Partial<Omit<GqlVendor, 'aggregatePayments' | 'documents' | 'notes' | 'payments'> & { aggregatePayments: _RefType['AggregatePayment'], documents: Array<_RefType['Document']>, notes: Array<_RefType['Note']>, payments: Array<_RefType['Payment']> }> );
  TemplateContent: ( Partial<GqlEmailTemplateContent> ) | ( Partial<GqlSmsTemplateContent> );
};


/** Mapping between all available schema types and the resolvers types */
export type GqlResolversTypes = {
  AccessRequest: ResolverTypeWrapper<Partial<GqlAccessRequest>>;
  AccessRequestFilter: ResolverTypeWrapper<Partial<GqlAccessRequestFilter>>;
  AccessRequestMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlAccessRequestMutationResponse, 'query'> & { query: GqlResolversTypes['Query'] }>>;
  AccessRequestMutations: ResolverTypeWrapper<Partial<Omit<GqlAccessRequestMutations, 'createAccessRequest' | 'reviewAccessRequest'> & { createAccessRequest: GqlResolversTypes['AccessRequestMutationResponse'], reviewAccessRequest: GqlResolversTypes['AccessRequestMutationResponse'] }>>;
  AccessRequestPage: ResolverTypeWrapper<Partial<GqlAccessRequestPage>>;
  AccountNumber: ResolverTypeWrapper<Partial<Scalars['AccountNumber']['output']>>;
  AccountType: ResolverTypeWrapper<Partial<GqlAccountType>>;
  ActiveLabel: ResolverTypeWrapper<Partial<GqlActiveLabel>>;
  AddApplicationVersionInput: ResolverTypeWrapper<Partial<GqlAddApplicationVersionInput>>;
  AddBulkCommentInput: ResolverTypeWrapper<Partial<GqlAddBulkCommentInput>>;
  AddCommentInput: ResolverTypeWrapper<Partial<GqlAddCommentInput>>;
  AddNoteInput: ResolverTypeWrapper<Partial<GqlAddNoteInput>>;
  AddNoteToApplicationAnswerInput: ResolverTypeWrapper<Partial<GqlAddNoteToApplicationAnswerInput>>;
  AddNoteToApplicationAnswerPayload: ResolverTypeWrapper<Partial<Omit<GqlAddNoteToApplicationAnswerPayload, 'applicationAnswer' | 'applicationAnswerNote' | 'note'> & { applicationAnswer?: Maybe<GqlResolversTypes['ApplicationAnswer']>, applicationAnswerNote?: Maybe<GqlResolversTypes['ApplicationAnswerNote']>, note?: Maybe<GqlResolversTypes['Note']> }>>;
  AddProgramFundInput: ResolverTypeWrapper<Partial<GqlAddProgramFundInput>>;
  AddReviewToApplicationAnswerInput: ResolverTypeWrapper<Partial<GqlAddReviewToApplicationAnswerInput>>;
  AddReviewToApplicationAnswerPayload: ResolverTypeWrapper<Partial<Omit<GqlAddReviewToApplicationAnswerPayload, 'applicationAnswer' | 'applicationAnswerReview' | 'note'> & { applicationAnswer?: Maybe<GqlResolversTypes['ApplicationAnswer']>, applicationAnswerReview?: Maybe<GqlResolversTypes['ApplicationAnswerReview']>, note?: Maybe<GqlResolversTypes['Note']> }>>;
  Address: ResolverTypeWrapper<Partial<GqlAddress>>;
  AddressField: ResolverTypeWrapper<Partial<GqlAddressField>>;
  AddressFieldFilter: ResolverTypeWrapper<Partial<GqlAddressFieldFilter>>;
  AddressFilter: ResolverTypeWrapper<Partial<GqlAddressFilter>>;
  AddressPage: ResolverTypeWrapper<Partial<GqlAddressPage>>;
  Admin: ResolverTypeWrapper<Partial<Omit<GqlAdmin, 'assignments' | 'user'> & { assignments: Array<GqlResolversTypes['Assignment']>, user: GqlResolversTypes['User'] }>>;
  AdminMutations: ResolverTypeWrapper<Partial<Omit<GqlAdminMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversTypes['AdminResponse'], delete: GqlResolversTypes['AdminResponse'], update: GqlResolversTypes['AdminResponse'] }>>;
  AdminPage: ResolverTypeWrapper<Partial<Omit<GqlAdminPage, 'nodes'> & { nodes: Array<GqlResolversTypes['Admin']> }>>;
  AdminResponse: ResolverTypeWrapper<Partial<Omit<GqlAdminResponse, 'query'> & { query: GqlResolversTypes['Query'] }>>;
  AdvocateIdentityConfig: ResolverTypeWrapper<Partial<GqlAdvocateIdentityConfig>>;
  AggregateApplicantsReferred: ResolverTypeWrapper<Partial<GqlAggregateApplicantsReferred>>;
  AggregateApplication: ResolverTypeWrapper<Partial<GqlAggregateApplication>>;
  AggregateApplicationFilter: ResolverTypeWrapper<Partial<GqlAggregateApplicationFilter>>;
  AggregatePayment: ResolverTypeWrapper<Partial<GqlAggregatePayment>>;
  AggregatePaymentFilter: ResolverTypeWrapper<Partial<GqlAggregatePaymentFilter>>;
  AnalyticsResource: ResolverTypeWrapper<Partial<GqlAnalyticsResource>>;
  ApplicantIdentityConfig: ResolverTypeWrapper<Partial<GqlApplicantIdentityConfig>>;
  ApplicantNameConsistency: ResolverTypeWrapper<Partial<GqlApplicantNameConsistency>>;
  ApplicantNameConsistencyResult: ResolverTypeWrapper<Partial<GqlApplicantNameConsistencyResult>>;
  ApplicantProfile: ResolverTypeWrapper<Partial<Omit<GqlApplicantProfile, 'documents' | 'notes' | 'user'> & { documents: Array<GqlResolversTypes['Document']>, notes: Array<GqlResolversTypes['Note']>, user: GqlResolversTypes['User'] }>>;
  ApplicantProfileConfig: ResolverTypeWrapper<Partial<GqlApplicantProfileConfig>>;
  ApplicantProfileConfiguration: ResolverTypeWrapper<Partial<GqlApplicantProfileConfiguration>>;
  ApplicantRolesConfig: ResolverTypeWrapper<Partial<GqlApplicantRolesConfig>>;
  ApplicantType: ResolverTypeWrapper<Partial<GqlApplicantType>>;
  ApplicantTypeRole: ResolverTypeWrapper<Partial<GqlApplicantTypeRole>>;
  Application: ResolverTypeWrapper<Partial<Omit<GqlApplication, 'case' | 'documents' | 'linkAttempts' | 'referral' | 'submitter' | 'versions'> & { case: GqlResolversTypes['Case'], documents: Array<GqlResolversTypes['Document']>, linkAttempts: Array<GqlResolversTypes['LinkAttempt']>, referral?: Maybe<GqlResolversTypes['ProgramReferral']>, submitter: GqlResolversTypes['User'], versions: Array<GqlResolversTypes['ApplicationVersion']> }>>;
  ApplicationAddressUpdate: ResolverTypeWrapper<Partial<GqlApplicationAddressUpdate>>;
  ApplicationAnswer: ResolverTypeWrapper<Partial<Omit<GqlApplicationAnswer, 'applicationAnswerNotes' | 'fieldReviews' | 'fieldReviewsRequiringResubmission'> & { applicationAnswerNotes: Array<GqlResolversTypes['ApplicationAnswerNote']>, fieldReviews: Array<GqlResolversTypes['ApplicationAnswerReview']>, fieldReviewsRequiringResubmission: Array<GqlResolversTypes['ApplicationAnswerReview']> }>>;
  ApplicationAnswerNote: ResolverTypeWrapper<Partial<Omit<GqlApplicationAnswerNote, 'applicationAnswer' | 'note'> & { applicationAnswer: GqlResolversTypes['ApplicationAnswer'], note: GqlResolversTypes['Note'] }>>;
  ApplicationAnswerReview: ResolverTypeWrapper<Partial<Omit<GqlApplicationAnswerReview, 'note'> & { note?: Maybe<GqlResolversTypes['Note']> }>>;
  ApplicationAnswerReviewStatus: ResolverTypeWrapper<Partial<GqlApplicationAnswerReviewStatus>>;
  ApplicationConfigOverrides: ResolverTypeWrapper<Partial<GqlApplicationConfigOverrides>>;
  ApplicationConfigReviewFields: ResolverTypeWrapper<Partial<GqlApplicationConfigReviewFields>>;
  ApplicationConfigReviewFieldsInput: ResolverTypeWrapper<Partial<GqlApplicationConfigReviewFieldsInput>>;
  ApplicationConfiguration: ResolverTypeWrapper<Partial<Omit<GqlApplicationConfiguration, 'sections'> & { sections: Array<GqlResolversTypes['ApplicationSection']> }>>;
  ApplicationContentInput: ResolverTypeWrapper<Partial<GqlApplicationContentInput>>;
  ApplicationDocumentUpdate: ResolverTypeWrapper<Partial<GqlApplicationDocumentUpdate>>;
  ApplicationField: Partial<ResolverTypeWrapper<GqlResolversUnionTypes<GqlResolversTypes>['ApplicationField']>>;
  ApplicationFieldType: ResolverTypeWrapper<Partial<GqlApplicationFieldType>>;
  ApplicationFieldValidation: ResolverTypeWrapper<Partial<GqlApplicationFieldValidation>>;
  ApplicationFieldValidationRule: ResolverTypeWrapper<Partial<GqlApplicationFieldValidationRule>>;
  ApplicationFilter: ResolverTypeWrapper<Partial<GqlApplicationFilter>>;
  ApplicationMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlApplicationMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Application']> }>>;
  ApplicationMutations: ResolverTypeWrapper<Partial<Omit<GqlApplicationMutations, 'addVersion' | 'create' | 'evaluateEligibility' | 'submit' | 'update'> & { addVersion: GqlResolversTypes['ApplicationMutationResponse'], create: GqlResolversTypes['ApplicationMutationResponse'], evaluateEligibility: GqlResolversTypes['ApplicationMutationResponse'], submit: GqlResolversTypes['ApplicationMutationResponse'], update: GqlResolversTypes['ApplicationMutationResponse'] }>>;
  ApplicationPage: ResolverTypeWrapper<Partial<Omit<GqlApplicationPage, 'applications'> & { applications: Array<GqlResolversTypes['Application']> }>>;
  ApplicationQuestion: ResolverTypeWrapper<Partial<Omit<GqlApplicationQuestion, 'copy' | 'fields'> & { copy: GqlResolversTypes['ApplicationQuestionCopy'], fields: Array<GqlResolversTypes['ApplicationField']> }>>;
  ApplicationQuestionCopy: ResolverTypeWrapper<Partial<GqlApplicationQuestionCopy>>;
  ApplicationQuestionGroup: ResolverTypeWrapper<Partial<Omit<GqlApplicationQuestionGroup, 'overview' | 'questions'> & { overview?: Maybe<GqlResolversTypes['ApplicationQuestionGroupOverview']>, questions: Array<GqlResolversTypes['ApplicationQuestion']> }>>;
  ApplicationQuestionGroupOverview: ResolverTypeWrapper<Partial<GqlApplicationQuestionGroupOverview>>;
  ApplicationQuestionLayout: ResolverTypeWrapper<Partial<GqlApplicationQuestionLayout>>;
  ApplicationScore: ResolverTypeWrapper<Partial<GqlApplicationScore>>;
  ApplicationSection: ResolverTypeWrapper<Partial<Omit<GqlApplicationSection, 'overview' | 'questionGroups'> & { overview: GqlResolversTypes['ApplicationSectionOverview'], questionGroups: Array<GqlResolversTypes['ApplicationQuestionGroup']> }>>;
  ApplicationSectionOverview: ResolverTypeWrapper<Partial<GqlApplicationSectionOverview>>;
  ApplicationStatus: ResolverTypeWrapper<Partial<GqlApplicationStatus>>;
  ApplicationSubmission: ResolverTypeWrapper<Partial<Omit<GqlApplicationSubmission, 'currentVersion' | 'documents' | 'latestSubmission' | 'submitter' | 'versions'> & { currentVersion: GqlResolversTypes['ApplicationVersion'], documents: Array<GqlResolversTypes['Document']>, latestSubmission: GqlResolversTypes['ApplicationVersion'], submitter: GqlResolversTypes['User'], versions: Array<GqlResolversTypes['ApplicationVersion']> }>>;
  ApplicationVerification: ResolverTypeWrapper<Partial<GqlApplicationVerification>>;
  ApplicationVersion: ResolverTypeWrapper<Partial<Omit<GqlApplicationVersion, 'creator' | 'mappedAnswers'> & { creator: GqlResolversTypes['User'], mappedAnswers: Array<GqlResolversTypes['SectionResponse']> }>>;
  ApproveAmountInput: ResolverTypeWrapper<Partial<GqlApproveAmountInput>>;
  ApproveCasePaymentsInput: ResolverTypeWrapper<Partial<GqlApproveCasePaymentsInput>>;
  AssignToCaseInput: ResolverTypeWrapper<Partial<GqlAssignToCaseInput>>;
  AssigneeId: ResolverTypeWrapper<Partial<Scalars['AssigneeId']['output']>>;
  Assignment: ResolverTypeWrapper<Partial<Omit<GqlAssignment, 'case'> & { case?: Maybe<GqlResolversTypes['Case']> }>>;
  BankAccount: ResolverTypeWrapper<Partial<GqlBankAccount>>;
  BankAccountInput: ResolverTypeWrapper<Partial<GqlBankAccountInput>>;
  BeginSessionInput: ResolverTypeWrapper<Partial<GqlBeginSessionInput>>;
  BenefitType: ResolverTypeWrapper<Partial<GqlBenefitType>>;
  BigInt: ResolverTypeWrapper<Partial<Scalars['BigInt']['output']>>;
  Boolean: ResolverTypeWrapper<Partial<Scalars['Boolean']['output']>>;
  BulkCaseMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlBulkCaseMutationResponse, 'query' | 'records'> & { query: GqlResolversTypes['Query'], records?: Maybe<Array<GqlResolversTypes['Case']>> }>>;
  BulkFulfillmentResponse: ResolverTypeWrapper<Partial<Omit<GqlBulkFulfillmentResponse, 'records'> & { records?: Maybe<Array<GqlResolversTypes['Fulfillment']>> }>>;
  BulkIssueFundsInput: ResolverTypeWrapper<Partial<GqlBulkIssueFundsInput>>;
  BulkResponseError: ResolverTypeWrapper<Partial<GqlBulkResponseError>>;
  BulkResponseMetadata: ResolverTypeWrapper<Partial<GqlBulkResponseMetadata>>;
  BulkUserMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlBulkUserMutationResponse, 'query' | 'records'> & { query: GqlResolversTypes['Query'], records?: Maybe<Array<GqlResolversTypes['User']>> }>>;
  ButtonVariant: ResolverTypeWrapper<Partial<GqlButtonVariant>>;
  Byte: ResolverTypeWrapper<Partial<Scalars['Byte']['output']>>;
  CalculatedField: ResolverTypeWrapper<Partial<GqlCalculatedField>>;
  CalculatedFieldDisplay: ResolverTypeWrapper<Partial<GqlCalculatedFieldDisplay>>;
  Case: ResolverTypeWrapper<Case>;
  CaseApplicationAnswer: ResolverTypeWrapper<Partial<GqlCaseApplicationAnswer>>;
  CaseApplicationsDocument: ResolverTypeWrapper<Partial<Omit<GqlCaseApplicationsDocument, 'data'> & { data: GqlResolversTypes['CasesApplicationsSource'] }>>;
  CaseCounts: ResolverTypeWrapper<Partial<GqlCaseCounts>>;
  CaseFilter: ResolverTypeWrapper<Partial<GqlCaseFilter>>;
  CaseMetadata: ResolverTypeWrapper<Partial<GqlCaseMetadata>>;
  CaseMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlCaseMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Case']> }>>;
  CaseMutations: ResolverTypeWrapper<Partial<Omit<GqlCaseMutations, 'addBulkComment' | 'addComment' | 'addParticipantComment' | 'addTagToCase' | 'approvePayments' | 'expedite' | 'inviteParticipant' | 'overridePayments' | 'removeDocuments' | 'removeTagFromCase' | 'transitionStatuses' | 'undoExpedite' | 'unlinkParticipant' | 'uploadDocuments'> & { addBulkComment: GqlResolversTypes['BulkCaseMutationResponse'], addComment: GqlResolversTypes['CaseMutationResponse'], addParticipantComment: GqlResolversTypes['CaseMutationResponse'], addTagToCase: GqlResolversTypes['CaseTagMutationResponse'], approvePayments: GqlResolversTypes['BulkCaseMutationResponse'], expedite: GqlResolversTypes['BulkCaseMutationResponse'], inviteParticipant: GqlResolversTypes['CaseMutationResponse'], overridePayments: GqlResolversTypes['BulkCaseMutationResponse'], removeDocuments: GqlResolversTypes['CaseMutationResponse'], removeTagFromCase: GqlResolversTypes['CaseTagMutationResponse'], transitionStatuses: GqlResolversTypes['BulkCaseMutationResponse'], undoExpedite: GqlResolversTypes['BulkCaseMutationResponse'], unlinkParticipant: GqlResolversTypes['CaseMutationResponse'], uploadDocuments: GqlResolversTypes['CaseMutationResponse'] }>>;
  CasePage: ResolverTypeWrapper<Partial<Omit<GqlCasePage, 'cases'> & { cases: Array<GqlResolversTypes['Case']> }>>;
  CaseParticipant: ResolverTypeWrapper<Partial<Omit<GqlCaseParticipant, 'case' | 'invitationCodes' | 'linkAttempts'> & { case: GqlResolversTypes['Case'], invitationCodes: Array<GqlResolversTypes['InvitationCode']>, linkAttempts: Array<GqlResolversTypes['LinkAttempt']> }>>;
  CaseParticipantStatus: ResolverTypeWrapper<Partial<GqlCaseParticipantStatus>>;
  CaseSort: ResolverTypeWrapper<Partial<GqlCaseSort>>;
  CaseSortColumn: ResolverTypeWrapper<Partial<GqlCaseSortColumn>>;
  CaseStatus: ResolverTypeWrapper<Partial<GqlCaseStatus>>;
  CaseTag: ResolverTypeWrapper<Partial<Omit<GqlCaseTag, 'case' | 'tag'> & { case: GqlResolversTypes['Case'], tag: GqlResolversTypes['Tag'] }>>;
  CaseTagMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlCaseTagMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['CaseTag']> }>>;
  CasesApplicationsSource: ResolverTypeWrapper<Partial<Omit<GqlCasesApplicationsSource, 'applicationAnswers'> & { applicationAnswers?: Maybe<Array<GqlResolversTypes['ApplicationAnswer']>> }>>;
  CasesMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlCasesMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<Array<GqlResolversTypes['Case']>> }>>;
  Changelog: ResolverTypeWrapper<Partial<GqlChangelog>>;
  ChannelConfiguration: ResolverTypeWrapper<Partial<Omit<GqlChannelConfiguration, 'template'> & { template: GqlResolversTypes['NotificationTemplate'] }>>;
  CheckboxField: ResolverTypeWrapper<Partial<GqlCheckboxField>>;
  CheckboxGroupField: ResolverTypeWrapper<Partial<GqlCheckboxGroupField>>;
  ClaimCheckInput: ResolverTypeWrapper<Partial<GqlClaimCheckInput>>;
  ClaimDirectDepositInput: ResolverTypeWrapper<Partial<GqlClaimDirectDepositInput>>;
  ClaimFulfillmentResponse: ResolverTypeWrapper<Partial<Omit<GqlClaimFulfillmentResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Fulfillment']> }>>;
  ClaimPhysicalCardInput: ResolverTypeWrapper<Partial<GqlClaimPhysicalCardInput>>;
  ClaimVirtualCardInput: ResolverTypeWrapper<Partial<GqlClaimVirtualCardInput>>;
  ClaimZelleInput: ResolverTypeWrapper<Partial<GqlClaimZelleInput>>;
  Comment: ResolverTypeWrapper<Partial<Omit<GqlComment, 'author' | 'case' | 'children' | 'parent'> & { author?: Maybe<GqlResolversTypes['User']>, case?: Maybe<GqlResolversTypes['Case']>, children?: Maybe<Array<GqlResolversTypes['Comment']>>, parent?: Maybe<GqlResolversTypes['Comment']> }>>;
  CommunicationChannelsConfig: ResolverTypeWrapper<Partial<GqlCommunicationChannelsConfig>>;
  CommunicationChannelsConfiguration: ResolverTypeWrapper<Partial<GqlCommunicationChannelsConfiguration>>;
  CommunicationPreferences: ResolverTypeWrapper<Partial<GqlCommunicationPreferences>>;
  CommunicationPreferencesInput: ResolverTypeWrapper<Partial<GqlCommunicationPreferencesInput>>;
  ComplexField: ResolverTypeWrapper<Partial<Omit<GqlComplexField, 'subFields'> & { subFields: Array<GqlResolversTypes['ApplicationField']> }>>;
  ContentBlockType: ResolverTypeWrapper<Partial<GqlContentBlockType>>;
  CountryCode: ResolverTypeWrapper<Partial<Scalars['CountryCode']['output']>>;
  CreateAccessRequestInput: ResolverTypeWrapper<Partial<GqlCreateAccessRequestInput>>;
  CreateAddressInput: ResolverTypeWrapper<Partial<GqlCreateAddressInput>>;
  CreateAdminInput: ResolverTypeWrapper<Partial<GqlCreateAdminInput>>;
  CreateApplicationInput: ResolverTypeWrapper<Partial<GqlCreateApplicationInput>>;
  CreateBulkProgramReferralInput: ResolverTypeWrapper<Partial<GqlCreateBulkProgramReferralInput>>;
  CreateCaseTagInput: ResolverTypeWrapper<Partial<GqlCreateCaseTagInput>>;
  CreateFundInput: ResolverTypeWrapper<Partial<GqlCreateFundInput>>;
  CreateIncidentInput: ResolverTypeWrapper<Partial<GqlCreateIncidentInput>>;
  CreatePaymentInput: ResolverTypeWrapper<Partial<GqlCreatePaymentInput>>;
  CreateProgramInput: ResolverTypeWrapper<Partial<GqlCreateProgramInput>>;
  CreateReferralInput: ResolverTypeWrapper<Partial<GqlCreateReferralInput>>;
  CreateSavedViewInput: ResolverTypeWrapper<Partial<GqlCreateSavedViewInput>>;
  CreateTag: ResolverTypeWrapper<Partial<GqlCreateTag>>;
  CreateTagAutomationInput: ResolverTypeWrapper<Partial<GqlCreateTagAutomationInput>>;
  CreateTagsInput: ResolverTypeWrapper<Partial<GqlCreateTagsInput>>;
  CreateUserInput: ResolverTypeWrapper<Partial<GqlCreateUserInput>>;
  CreateVendorInput: ResolverTypeWrapper<Partial<GqlCreateVendorInput>>;
  CreateW9TaxFormInput: ResolverTypeWrapper<Partial<GqlCreateW9TaxFormInput>>;
  Cuid: ResolverTypeWrapper<Partial<Scalars['Cuid']['output']>>;
  Currency: ResolverTypeWrapper<Partial<Scalars['Currency']['output']>>;
  CursorPageInfo: ResolverTypeWrapper<Partial<GqlCursorPageInfo>>;
  CursorPagination: ResolverTypeWrapper<Partial<GqlCursorPagination>>;
  DID: ResolverTypeWrapper<Partial<Scalars['DID']['output']>>;
  DataLookupConfiguration: ResolverTypeWrapper<Partial<GqlDataLookupConfiguration>>;
  DataLookupField: ResolverTypeWrapper<Partial<GqlDataLookupField>>;
  DataLookupFieldInput: ResolverTypeWrapper<Partial<GqlDataLookupFieldInput>>;
  Date: ResolverTypeWrapper<Partial<Scalars['Date']['output']>>;
  DateField: ResolverTypeWrapper<Partial<GqlDateField>>;
  DateFieldRange: ResolverTypeWrapper<Partial<GqlDateFieldRange>>;
  DateFieldValidation: ResolverTypeWrapper<Partial<GqlDateFieldValidation>>;
  DateInterval: ResolverTypeWrapper<Partial<GqlDateInterval>>;
  DateRange: ResolverTypeWrapper<Partial<GqlDateRange>>;
  DateTime: ResolverTypeWrapper<Partial<Scalars['DateTime']['output']>>;
  DateTimeISO: ResolverTypeWrapper<Partial<Scalars['DateTimeISO']['output']>>;
  DeleteAdminInput: ResolverTypeWrapper<Partial<GqlDeleteAdminInput>>;
  DeleteCaseTagInput: ResolverTypeWrapper<Partial<GqlDeleteCaseTagInput>>;
  DeleteSavedViewInput: ResolverTypeWrapper<Partial<GqlDeleteSavedViewInput>>;
  DeleteTagAutomationInput: ResolverTypeWrapper<Partial<GqlDeleteTagAutomationInput>>;
  DeleteTagsInput: ResolverTypeWrapper<Partial<GqlDeleteTagsInput>>;
  DeleteVendorInput: ResolverTypeWrapper<Partial<GqlDeleteVendorInput>>;
  DeweyDecimal: ResolverTypeWrapper<Partial<Scalars['DeweyDecimal']['output']>>;
  DoctopusTag: ResolverTypeWrapper<Partial<GqlDoctopusTag>>;
  Document: ResolverTypeWrapper<Partial<Omit<GqlDocument, 'summary' | 'uploader'> & { summary?: Maybe<GqlResolversTypes['DocumentSummary']>, uploader?: Maybe<GqlResolversTypes['User']> }>>;
  DocumentConsistency: ResolverTypeWrapper<Partial<GqlDocumentConsistency>>;
  DocumentField: ResolverTypeWrapper<Partial<GqlDocumentField>>;
  DocumentFieldCopy: ResolverTypeWrapper<Partial<GqlDocumentFieldCopy>>;
  DocumentMutations: ResolverTypeWrapper<Partial<Omit<GqlDocumentMutations, 'pinDocument' | 'submitFeedback'> & { pinDocument: GqlResolversTypes['DocumentsMutationResponse'], submitFeedback: GqlResolversTypes['DocumentsMutationResponse'] }>>;
  DocumentSummary: ResolverTypeWrapper<Partial<Omit<GqlDocumentSummary, 'prediction'> & { prediction?: Maybe<GqlResolversTypes['Prediction']> }>>;
  DocumentTag: ResolverTypeWrapper<Partial<GqlDocumentTag>>;
  DocumentsMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlDocumentsMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Document']> }>>;
  Domain: ResolverTypeWrapper<Partial<GqlDomain>>;
  DropdownChildMode: ResolverTypeWrapper<Partial<GqlDropdownChildMode>>;
  DropdownField: ResolverTypeWrapper<Partial<GqlDropdownField>>;
  Duration: ResolverTypeWrapper<Partial<Scalars['Duration']['output']>>;
  EligibilityConfig: ResolverTypeWrapper<Partial<GqlEligibilityConfig>>;
  EligibilityQuestion: ResolverTypeWrapper<Partial<GqlEligibilityQuestion>>;
  EligibilityQuestionType: ResolverTypeWrapper<Partial<GqlEligibilityQuestionType>>;
  EligibilityStatus: ResolverTypeWrapper<Partial<GqlEligibilityStatus>>;
  EmailAddress: ResolverTypeWrapper<Partial<Scalars['EmailAddress']['output']>>;
  EmailTemplateContent: ResolverTypeWrapper<Partial<GqlEmailTemplateContent>>;
  EndSessionInput: ResolverTypeWrapper<Partial<GqlEndSessionInput>>;
  Enrollment: ResolverTypeWrapper<Partial<Omit<GqlEnrollment, 'program'> & { program: GqlResolversTypes['Program'] }>>;
  EnrollmentIdentifier: ResolverTypeWrapper<Partial<GqlEnrollmentIdentifier>>;
  EnrollmentMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlEnrollmentMutationResponse, 'record'> & { record?: Maybe<GqlResolversTypes['Enrollment']> }>>;
  EnrollmentMutations: ResolverTypeWrapper<Partial<Omit<GqlEnrollmentMutations, 'removeOutcome' | 'removeService' | 'upsert' | 'upsertOutcome' | 'upsertService'> & { removeOutcome: GqlResolversTypes['EnrollmentMutationResponse'], removeService: GqlResolversTypes['EnrollmentMutationResponse'], upsert: GqlResolversTypes['EnrollmentMutationResponse'], upsertOutcome: GqlResolversTypes['EnrollmentMutationResponse'], upsertService: GqlResolversTypes['EnrollmentMutationResponse'] }>>;
  EnrollmentOutcome: ResolverTypeWrapper<Partial<GqlEnrollmentOutcome>>;
  EnrollmentService: ResolverTypeWrapper<Partial<GqlEnrollmentService>>;
  ExpediteCasesInput: ResolverTypeWrapper<Partial<GqlExpediteCasesInput>>;
  ExpenseType: ResolverTypeWrapper<Partial<GqlExpenseType>>;
  ExportTableToCSVInput: ResolverTypeWrapper<Partial<GqlExportTableToCsvInput>>;
  Feature: ResolverTypeWrapper<Partial<GqlFeature>>;
  FeatureSetting: ResolverTypeWrapper<Partial<GqlFeatureSetting>>;
  Feedback: ResolverTypeWrapper<Partial<Omit<GqlFeedback, 'admin'> & { admin: GqlResolversTypes['Admin'] }>>;
  FieldResponse: ResolverTypeWrapper<Partial<Omit<GqlFieldResponse, 'answer' | 'field'> & { answer?: Maybe<GqlResolversTypes['ApplicationAnswer']>, field: GqlResolversTypes['ApplicationField'] }>>;
  Float: ResolverTypeWrapper<Partial<Scalars['Float']['output']>>;
  FormDefinition: ResolverTypeWrapper<Partial<GqlFormDefinition>>;
  Fulfillment: ResolverTypeWrapper<Partial<Omit<GqlFulfillment, 'case' | 'fulfillmentMeta' | 'fund' | 'payments'> & { case: GqlResolversTypes['Case'], fulfillmentMeta?: Maybe<GqlResolversTypes['FulfillmentMeta']>, fund: GqlResolversTypes['Fund'], payments?: Maybe<Array<GqlResolversTypes['Payment']>> }>>;
  FulfillmentFilter: ResolverTypeWrapper<Partial<GqlFulfillmentFilter>>;
  FulfillmentMeta: ResolverTypeWrapper<Partial<Omit<GqlFulfillmentMeta, 'fulfillment'> & { fulfillment: GqlResolversTypes['Fulfillment'] }>>;
  FulfillmentMutations: ResolverTypeWrapper<Partial<Omit<GqlFulfillmentMutations, 'approveAmount' | 'bulkIssueFunds' | 'claimCheck' | 'claimDirectDeposit' | 'claimPhysicalCard' | 'claimVirtualCard' | 'claimZelle' | 'issueFunds'> & { approveAmount: GqlResolversTypes['FulfillmentResponse'], bulkIssueFunds: GqlResolversTypes['BulkFulfillmentResponse'], claimCheck: GqlResolversTypes['ClaimFulfillmentResponse'], claimDirectDeposit: GqlResolversTypes['ClaimFulfillmentResponse'], claimPhysicalCard: GqlResolversTypes['ClaimFulfillmentResponse'], claimVirtualCard: GqlResolversTypes['ClaimFulfillmentResponse'], claimZelle: GqlResolversTypes['ClaimFulfillmentResponse'], issueFunds: GqlResolversTypes['ClaimFulfillmentResponse'] }>>;
  FulfillmentPage: ResolverTypeWrapper<Partial<Omit<GqlFulfillmentPage, 'fulfillments'> & { fulfillments: Array<GqlResolversTypes['Fulfillment']> }>>;
  FulfillmentResponse: ResolverTypeWrapper<Partial<Omit<GqlFulfillmentResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Fulfillment']> }>>;
  Fund: ResolverTypeWrapper<Partial<Omit<GqlFund, 'config' | 'programs'> & { config?: Maybe<GqlResolversTypes['FundConfig']>, programs: Array<GqlResolversTypes['Program']> }>>;
  FundConfig: ResolverTypeWrapper<Partial<GqlFundConfig>>;
  FundFilter: ResolverTypeWrapper<Partial<GqlFundFilter>>;
  FundMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlFundMutationResponse, 'record'> & { record?: Maybe<GqlResolversTypes['Fund']> }>>;
  FundMutations: ResolverTypeWrapper<Partial<Omit<GqlFundMutations, 'create' | 'remove' | 'update'> & { create: GqlResolversTypes['FundMutationResponse'], remove: GqlResolversTypes['FundMutationResponse'], update: GqlResolversTypes['FundMutationResponse'] }>>;
  FundPage: ResolverTypeWrapper<Partial<Omit<GqlFundPage, 'nodes'> & { nodes: Array<GqlResolversTypes['Fund']> }>>;
  FundSort: ResolverTypeWrapper<Partial<GqlFundSort>>;
  FundSortColumn: ResolverTypeWrapper<Partial<GqlFundSortColumn>>;
  FundStats: ResolverTypeWrapper<Partial<GqlFundStats>>;
  GUID: ResolverTypeWrapper<Partial<Scalars['GUID']['output']>>;
  GeoPoint: ResolverTypeWrapper<Partial<GqlGeoPoint>>;
  HSL: ResolverTypeWrapper<Partial<Scalars['HSL']['output']>>;
  HSLA: ResolverTypeWrapper<Partial<Scalars['HSLA']['output']>>;
  HasAccessInput: ResolverTypeWrapper<Partial<GqlHasAccessInput>>;
  HasAccessResponse: ResolverTypeWrapper<Partial<GqlHasAccessResponse>>;
  HexColorCode: ResolverTypeWrapper<Partial<Scalars['HexColorCode']['output']>>;
  Hexadecimal: ResolverTypeWrapper<Partial<Scalars['Hexadecimal']['output']>>;
  HouseholdLimit: ResolverTypeWrapper<Partial<GqlHouseholdLimit>>;
  IBAN: ResolverTypeWrapper<Partial<Scalars['IBAN']['output']>>;
  ID: ResolverTypeWrapper<Partial<Scalars['ID']['output']>>;
  IP: ResolverTypeWrapper<Partial<Scalars['IP']['output']>>;
  IPCPatent: ResolverTypeWrapper<Partial<Scalars['IPCPatent']['output']>>;
  IPv4: ResolverTypeWrapper<Partial<Scalars['IPv4']['output']>>;
  IPv6: ResolverTypeWrapper<Partial<Scalars['IPv6']['output']>>;
  ISBN: ResolverTypeWrapper<Partial<Scalars['ISBN']['output']>>;
  ISO8601Duration: ResolverTypeWrapper<Partial<Scalars['ISO8601Duration']['output']>>;
  IdentityConfig: ResolverTypeWrapper<Partial<GqlIdentityConfig>>;
  IdentityMutations: ResolverTypeWrapper<Partial<GqlIdentityMutations>>;
  IdentityResponse: ResolverTypeWrapper<Partial<GqlIdentityResponse>>;
  IdentityUser: ResolverTypeWrapper<Partial<GqlIdentityUser>>;
  IncidentMessage: ResolverTypeWrapper<Partial<Omit<GqlIncidentMessage, 'partners'> & { partners?: Maybe<Array<GqlResolversTypes['Partner']>> }>>;
  IncidentMessageFilter: ResolverTypeWrapper<Partial<GqlIncidentMessageFilter>>;
  IncidentMessagePage: ResolverTypeWrapper<Partial<Omit<GqlIncidentMessagePage, 'incidentMessages' | 'nodes'> & { incidentMessages: Array<GqlResolversTypes['IncidentMessage']>, nodes: Array<GqlResolversTypes['IncidentMessage']> }>>;
  IncidentMutationResponse: ResolverTypeWrapper<Partial<GqlIncidentMutationResponse>>;
  IncomeLimit: ResolverTypeWrapper<Partial<GqlIncomeLimit>>;
  IncomeLimitArea: ResolverTypeWrapper<Partial<GqlIncomeLimitArea>>;
  IncomeLimitAreaFilter: ResolverTypeWrapper<Partial<GqlIncomeLimitAreaFilter>>;
  IncomeLimitAreaPage: ResolverTypeWrapper<Partial<GqlIncomeLimitAreaPage>>;
  Index: ResolverTypeWrapper<Partial<GqlIndex>>;
  InputMonth: ResolverTypeWrapper<Partial<GqlInputMonth>>;
  Int: ResolverTypeWrapper<Partial<Scalars['Int']['output']>>;
  IntroPage: ResolverTypeWrapper<Partial<GqlIntroPage>>;
  IntroPageContentBlock: ResolverTypeWrapper<Partial<GqlIntroPageContentBlock>>;
  IntroPageLayoutType: ResolverTypeWrapper<Partial<GqlIntroPageLayoutType>>;
  InvitationCode: ResolverTypeWrapper<Partial<Omit<GqlInvitationCode, 'caseParticipant'> & { caseParticipant: GqlResolversTypes['CaseParticipant'] }>>;
  InviteCaseParticipantInput: ResolverTypeWrapper<Partial<GqlInviteCaseParticipantInput>>;
  IssueFundsInput: ResolverTypeWrapper<Partial<GqlIssueFundsInput>>;
  JSON: ResolverTypeWrapper<Partial<Scalars['JSON']['output']>>;
  JSONObject: ResolverTypeWrapper<Partial<Scalars['JSONObject']['output']>>;
  JWT: ResolverTypeWrapper<Partial<Scalars['JWT']['output']>>;
  LCCSubclass: ResolverTypeWrapper<Partial<Scalars['LCCSubclass']['output']>>;
  Latitude: ResolverTypeWrapper<Partial<Scalars['Latitude']['output']>>;
  LegacyUserType: ResolverTypeWrapper<Partial<GqlLegacyUserType>>;
  Limits: ResolverTypeWrapper<Partial<GqlLimits>>;
  LinkAttempt: ResolverTypeWrapper<Partial<Omit<GqlLinkAttempt, 'application' | 'caseParticipant' | 'originalCase'> & { application: GqlResolversTypes['Application'], caseParticipant: GqlResolversTypes['CaseParticipant'], originalCase: GqlResolversTypes['Case'] }>>;
  LinkAttemptResult: ResolverTypeWrapper<Partial<GqlLinkAttemptResult>>;
  LinkLegacyUserInput: ResolverTypeWrapper<Partial<GqlLinkLegacyUserInput>>;
  LocalDate: ResolverTypeWrapper<Partial<Scalars['LocalDate']['output']>>;
  LocalDateTime: ResolverTypeWrapper<Partial<Scalars['LocalDateTime']['output']>>;
  LocalEndTime: ResolverTypeWrapper<Partial<Scalars['LocalEndTime']['output']>>;
  LocalTime: ResolverTypeWrapper<Partial<Scalars['LocalTime']['output']>>;
  Locale: ResolverTypeWrapper<Partial<Scalars['Locale']['output']>>;
  LoginDetails: ResolverTypeWrapper<Partial<GqlLoginDetails>>;
  Long: ResolverTypeWrapper<Partial<Scalars['Long']['output']>>;
  Longitude: ResolverTypeWrapper<Partial<Scalars['Longitude']['output']>>;
  MAC: ResolverTypeWrapper<Partial<Scalars['MAC']['output']>>;
  MailingAddressType: ResolverTypeWrapper<Partial<GqlMailingAddressType>>;
  ModelVersion: ResolverTypeWrapper<Partial<GqlModelVersion>>;
  Month: ResolverTypeWrapper<Partial<GqlMonth>>;
  Mutation: ResolverTypeWrapper<{}>;
  NegativeFloat: ResolverTypeWrapper<Partial<Scalars['NegativeFloat']['output']>>;
  NegativeInt: ResolverTypeWrapper<Partial<Scalars['NegativeInt']['output']>>;
  NonEmptyString: ResolverTypeWrapper<Partial<Scalars['NonEmptyString']['output']>>;
  NonNegativeFloat: ResolverTypeWrapper<Partial<Scalars['NonNegativeFloat']['output']>>;
  NonNegativeInt: ResolverTypeWrapper<Partial<Scalars['NonNegativeInt']['output']>>;
  NonPositiveFloat: ResolverTypeWrapper<Partial<Scalars['NonPositiveFloat']['output']>>;
  NonPositiveInt: ResolverTypeWrapper<Partial<Scalars['NonPositiveInt']['output']>>;
  Note: ResolverTypeWrapper<Partial<Omit<GqlNote, 'author' | 'case'> & { author: GqlResolversTypes['Admin'], case: GqlResolversTypes['Case'] }>>;
  NoteMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlNoteMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Note']> }>>;
  NoteMutations: ResolverTypeWrapper<Partial<Omit<GqlNoteMutations, 'add' | 'update'> & { add: GqlResolversTypes['NoteMutationResponse'], update: GqlResolversTypes['NoteMutationResponse'] }>>;
  NoteRelationType: ResolverTypeWrapper<Partial<GqlNoteRelationType>>;
  NotificationAction: ResolverTypeWrapper<Partial<GqlNotificationAction>>;
  NotificationChannel: ResolverTypeWrapper<Partial<GqlNotificationChannel>>;
  NotificationConfig: ResolverTypeWrapper<Partial<Omit<GqlNotificationConfig, 'channelConfigurations'> & { channelConfigurations: Array<GqlResolversTypes['ChannelConfiguration']> }>>;
  NotificationTemplate: ResolverTypeWrapper<Partial<Omit<GqlNotificationTemplate, 'content'> & { content: GqlResolversTypes['TemplateContent'] }>>;
  OCRResult: ResolverTypeWrapper<Partial<GqlOcrResult>>;
  ObjectID: ResolverTypeWrapper<Partial<Scalars['ObjectID']['output']>>;
  ObjectReference: ResolverTypeWrapper<Partial<GqlObjectReference>>;
  OffsetPagination: ResolverTypeWrapper<Partial<GqlOffsetPagination>>;
  OptionLabelValue: ResolverTypeWrapper<Partial<GqlOptionLabelValue>>;
  Outcome: ResolverTypeWrapper<Partial<GqlOutcome>>;
  OverrideCasePaymentsInput: ResolverTypeWrapper<Partial<GqlOverrideCasePaymentsInput>>;
  OverridePaymentInput: ResolverTypeWrapper<Partial<GqlOverridePaymentInput>>;
  PageInfo: ResolverTypeWrapper<Partial<GqlPageInfo>>;
  ParticipantInput: ResolverTypeWrapper<Partial<GqlParticipantInput>>;
  Partner: ResolverTypeWrapper<Partial<Omit<GqlPartner, 'analyticsResources' | 'funds' | 'parent' | 'programs' | 'savedViews' | 'tagAutomations' | 'tags'> & { analyticsResources: Array<GqlResolversTypes['AnalyticsResource']>, funds: Array<GqlResolversTypes['Fund']>, parent?: Maybe<GqlResolversTypes['Partner']>, programs: Array<GqlResolversTypes['Program']>, savedViews?: Maybe<Array<GqlResolversTypes['SavedView']>>, tagAutomations: Array<GqlResolversTypes['TagAutomation']>, tags: Array<GqlResolversTypes['Tag']> }>>;
  PartnerConfig: ResolverTypeWrapper<Partial<GqlPartnerConfig>>;
  PartnerFilter: ResolverTypeWrapper<Partial<GqlPartnerFilter>>;
  PartnerIncident: ResolverTypeWrapper<Partial<GqlPartnerIncident>>;
  PartnerIncidentMutations: ResolverTypeWrapper<Partial<GqlPartnerIncidentMutations>>;
  PartnerPage: ResolverTypeWrapper<Partial<Omit<GqlPartnerPage, 'partners'> & { partners: Array<GqlResolversTypes['Partner']> }>>;
  PartnerWhitelabeling: ResolverTypeWrapper<Partial<GqlPartnerWhitelabeling>>;
  Password: ResolverTypeWrapper<Partial<Scalars['Password']['output']>>;
  Payee: Partial<ResolverTypeWrapper<GqlResolversUnionTypes<GqlResolversTypes>['Payee']>>;
  PayeeType: ResolverTypeWrapper<Partial<GqlPayeeType>>;
  Payment: ResolverTypeWrapper<Partial<Omit<GqlPayment, 'fulfillment' | 'payee'> & { fulfillment: GqlResolversTypes['Fulfillment'], payee?: Maybe<GqlResolversTypes['Payee']> }>>;
  PaymentInput: ResolverTypeWrapper<Partial<GqlPaymentInput>>;
  PaymentMailingAddress: ResolverTypeWrapper<Partial<GqlPaymentMailingAddress>>;
  PaymentMethod: ResolverTypeWrapper<Partial<GqlPaymentMethod>>;
  PaymentMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlPaymentMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Payment']> }>>;
  PaymentMutations: ResolverTypeWrapper<Partial<Omit<GqlPaymentMutations, 'create' | 'remove' | 'update'> & { create: GqlResolversTypes['PaymentMutationResponse'], remove: GqlResolversTypes['PaymentMutationResponse'], update: GqlResolversTypes['PaymentMutationResponse'] }>>;
  PaymentPage: ResolverTypeWrapper<Partial<Omit<GqlPaymentPage, 'payments'> & { payments: Array<GqlResolversTypes['Payment']> }>>;
  PaymentPattern: ResolverTypeWrapper<Partial<GqlPaymentPattern>>;
  PaymentSchedule: ResolverTypeWrapper<Partial<GqlPaymentSchedule>>;
  PaymentSort: ResolverTypeWrapper<Partial<GqlPaymentSort>>;
  PaymentSortColumn: ResolverTypeWrapper<Partial<GqlPaymentSortColumn>>;
  PaymentStatus: ResolverTypeWrapper<Partial<GqlPaymentStatus>>;
  PaymentsConfig: ResolverTypeWrapper<Partial<GqlPaymentsConfig>>;
  PhoneNumber: ResolverTypeWrapper<Partial<Scalars['PhoneNumber']['output']>>;
  PinDocumentInput: ResolverTypeWrapper<Partial<GqlPinDocumentInput>>;
  Port: ResolverTypeWrapper<Partial<Scalars['Port']['output']>>;
  PortalRole: ResolverTypeWrapper<Partial<GqlPortalRole>>;
  PositiveFloat: ResolverTypeWrapper<Partial<Scalars['PositiveFloat']['output']>>;
  PositiveInt: ResolverTypeWrapper<Partial<Scalars['PositiveInt']['output']>>;
  PostalCode: ResolverTypeWrapper<Partial<Scalars['PostalCode']['output']>>;
  Prediction: ResolverTypeWrapper<Partial<Omit<GqlPrediction, 'feedback'> & { feedback: Array<GqlResolversTypes['Feedback']> }>>;
  PresetGuestTokenInput: ResolverTypeWrapper<Partial<GqlPresetGuestTokenInput>>;
  PresetMutations: ResolverTypeWrapper<Partial<GqlPresetMutations>>;
  Primitive: ResolverTypeWrapper<Partial<Scalars['Primitive']['output']>>;
  ProfileAnswer: ResolverTypeWrapper<Partial<Omit<GqlProfileAnswer, 'profile'> & { profile: GqlResolversTypes['ApplicantProfile'] }>>;
  ProfileKey: ResolverTypeWrapper<Partial<GqlProfileKey>>;
  Program: ResolverTypeWrapper<Partial<Omit<GqlProgram, 'applicantTypes' | 'applicationConfiguration' | 'applicationConfigurations' | 'documents' | 'funds' | 'notifications' | 'workflow'> & { applicantTypes: Array<GqlResolversTypes['ProgramApplicantType']>, applicationConfiguration?: Maybe<GqlResolversTypes['ApplicationConfiguration']>, applicationConfigurations: Array<GqlResolversTypes['ProgramApplicationConfiguration']>, documents?: Maybe<Array<GqlResolversTypes['ProgramDocument']>>, funds: Array<GqlResolversTypes['Fund']>, notifications?: Maybe<Array<GqlResolversTypes['NotificationConfig']>>, workflow: GqlResolversTypes['Workflow'] }>>;
  ProgramApplicantType: ResolverTypeWrapper<Partial<GqlProgramApplicantType>>;
  ProgramApplicationConfiguration: ResolverTypeWrapper<Partial<Omit<GqlProgramApplicationConfiguration, 'configuration'> & { configuration: GqlResolversTypes['ApplicationConfiguration'] }>>;
  ProgramConfig: ResolverTypeWrapper<Partial<GqlProgramConfig>>;
  ProgramContext: ResolverTypeWrapper<Partial<GqlProgramContext>>;
  ProgramContextInput: ResolverTypeWrapper<Partial<GqlProgramContextInput>>;
  ProgramDocument: ResolverTypeWrapper<Partial<Omit<GqlProgramDocument, 'document'> & { document?: Maybe<GqlResolversTypes['Document']> }>>;
  ProgramDocumentStatus: ResolverTypeWrapper<Partial<GqlProgramDocumentStatus>>;
  ProgramFilter: ResolverTypeWrapper<Partial<GqlProgramFilter>>;
  ProgramFundStats: ResolverTypeWrapper<Partial<GqlProgramFundStats>>;
  ProgramMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlProgramMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['Program']> }>>;
  ProgramMutations: ResolverTypeWrapper<Partial<Omit<GqlProgramMutations, 'addProgramFund' | 'create' | 'removeProgramFund' | 'update' | 'uploadVerificationFile' | 'upsertLookupConfig'> & { addProgramFund: GqlResolversTypes['ProgramMutationResponse'], create: GqlResolversTypes['ProgramMutationResponse'], removeProgramFund: GqlResolversTypes['ProgramMutationResponse'], update: GqlResolversTypes['ProgramsMutationResponse'], uploadVerificationFile: GqlResolversTypes['ProgramMutationResponse'], upsertLookupConfig: GqlResolversTypes['ProgramMutationResponse'] }>>;
  ProgramPage: ResolverTypeWrapper<Partial<Omit<GqlProgramPage, 'programs'> & { programs: Array<GqlResolversTypes['Program']> }>>;
  ProgramReferral: ResolverTypeWrapper<Partial<Omit<GqlProgramReferral, 'admin' | 'program' | 'user'> & { admin: GqlResolversTypes['Admin'], program: GqlResolversTypes['Program'], user: GqlResolversTypes['User'] }>>;
  ProgramReferralMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlProgramReferralMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['ProgramReferral']> }>>;
  ProgramReferralMutations: ResolverTypeWrapper<Partial<Omit<GqlProgramReferralMutations, 'create' | 'createBulkProgramReferral'> & { create: GqlResolversTypes['ProgramReferralMutationResponse'], createBulkProgramReferral: GqlResolversTypes['BulkUserMutationResponse'] }>>;
  ProgramStats: ResolverTypeWrapper<Partial<GqlProgramStats>>;
  ProgramStatus: ResolverTypeWrapper<Partial<GqlProgramStatus>>;
  ProgramsMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlProgramsMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<Array<GqlResolversTypes['Program']>> }>>;
  Query: ResolverTypeWrapper<{}>;
  QuestionGroupResponse: ResolverTypeWrapper<Partial<Omit<GqlQuestionGroupResponse, 'questions'> & { questions: Array<GqlResolversTypes['QuestionResponse']> }>>;
  QuestionResponse: ResolverTypeWrapper<Partial<Omit<GqlQuestionResponse, 'fields'> & { fields: Array<GqlResolversTypes['FieldResponse']> }>>;
  RGB: ResolverTypeWrapper<Partial<Scalars['RGB']['output']>>;
  RGBA: ResolverTypeWrapper<Partial<Scalars['RGBA']['output']>>;
  RadioListField: ResolverTypeWrapper<Partial<GqlRadioListField>>;
  ReapplicationRules: ResolverTypeWrapper<Partial<GqlReapplicationRules>>;
  ReapplicationValidationType: ResolverTypeWrapper<Partial<GqlReapplicationValidationType>>;
  RecurringPaymentConfig: ResolverTypeWrapper<Partial<GqlRecurringPaymentConfig>>;
  RemoveDocumentsInput: ResolverTypeWrapper<Partial<GqlRemoveDocumentsInput>>;
  RemoveEnrollmentOutcomeInput: ResolverTypeWrapper<Partial<GqlRemoveEnrollmentOutcomeInput>>;
  RemoveEnrollmentServiceInput: ResolverTypeWrapper<Partial<GqlRemoveEnrollmentServiceInput>>;
  RemoveFundInput: ResolverTypeWrapper<Partial<GqlRemoveFundInput>>;
  RemoveIncidentInput: ResolverTypeWrapper<Partial<GqlRemoveIncidentInput>>;
  RemovePaymentInput: ResolverTypeWrapper<Partial<GqlRemovePaymentInput>>;
  RemoveProgramFundInput: ResolverTypeWrapper<Partial<GqlRemoveProgramFundInput>>;
  RemoveVendorDocumentsInput: ResolverTypeWrapper<Partial<GqlRemoveVendorDocumentsInput>>;
  ResolvedEntities: ResolverTypeWrapper<Partial<GqlResolvedEntities>>;
  ResolverStatus: ResolverTypeWrapper<Partial<GqlResolverStatus>>;
  ResponseMetadata: ResolverTypeWrapper<Partial<GqlResponseMetadata>>;
  ReviewAccessRequestInput: ResolverTypeWrapper<Partial<GqlReviewAccessRequestInput>>;
  RoleConfig: ResolverTypeWrapper<Partial<GqlRoleConfig>>;
  RoutingNumber: ResolverTypeWrapper<Partial<Scalars['RoutingNumber']['output']>>;
  Ruleset: ResolverTypeWrapper<Partial<GqlRuleset>>;
  RulesetPage: ResolverTypeWrapper<Partial<GqlRulesetPage>>;
  RulesetSort: ResolverTypeWrapper<Partial<GqlRulesetSort>>;
  RulesetSortColumn: ResolverTypeWrapper<Partial<GqlRulesetSortColumn>>;
  SAMLConfig: ResolverTypeWrapper<Partial<GqlSamlConfig>>;
  SESSN: ResolverTypeWrapper<Partial<Scalars['SESSN']['output']>>;
  SafeInt: ResolverTypeWrapper<Partial<Scalars['SafeInt']['output']>>;
  SaveUserBankAccountInput: ResolverTypeWrapper<Partial<GqlSaveUserBankAccountInput>>;
  SavedView: ResolverTypeWrapper<Partial<Omit<GqlSavedView, 'author' | 'partner'> & { author: GqlResolversTypes['User'], partner: GqlResolversTypes['Partner'] }>>;
  SavedViewMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlSavedViewMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['SavedView']> }>>;
  SavedViewMutations: ResolverTypeWrapper<Partial<Omit<GqlSavedViewMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversTypes['SavedViewMutationResponse'], delete: GqlResolversTypes['SavedViewMutationResponse'], update: GqlResolversTypes['SavedViewMutationResponse'] }>>;
  ScheduleType: ResolverTypeWrapper<Partial<GqlScheduleType>>;
  SearchFilter: ResolverTypeWrapper<Partial<GqlSearchFilter>>;
  SearchInput: ResolverTypeWrapper<Partial<GqlSearchInput>>;
  SearchPage: ResolverTypeWrapper<Partial<Omit<GqlSearchPage, 'nodes'> & { nodes: Array<GqlResolversTypes['CaseApplicationsDocument']> }>>;
  SearchSort: ResolverTypeWrapper<Partial<GqlSearchSort>>;
  SearchSortColumn: ResolverTypeWrapper<Partial<GqlSearchSortColumn>>;
  SectionResponse: ResolverTypeWrapper<Partial<Omit<GqlSectionResponse, 'questionGroups'> & { questionGroups: Array<GqlResolversTypes['QuestionGroupResponse']> }>>;
  SemVer: ResolverTypeWrapper<Partial<Scalars['SemVer']['output']>>;
  SendIdentityVerificationEmailInput: ResolverTypeWrapper<Partial<GqlSendIdentityVerificationEmailInput>>;
  SendMagicLinkInput: ResolverTypeWrapper<Partial<GqlSendMagicLinkInput>>;
  Service: ResolverTypeWrapper<Partial<GqlService>>;
  Similarity: ResolverTypeWrapper<Partial<GqlSimilarity>>;
  SmsTemplateContent: ResolverTypeWrapper<Partial<GqlSmsTemplateContent>>;
  SortDirection: ResolverTypeWrapper<Partial<GqlSortDirection>>;
  StatusFilters: ResolverTypeWrapper<Partial<GqlStatusFilters>>;
  StatusOverride: ResolverTypeWrapper<Partial<GqlStatusOverride>>;
  String: ResolverTypeWrapper<Partial<Scalars['String']['output']>>;
  SubBenefitType: ResolverTypeWrapper<Partial<GqlSubBenefitType>>;
  SubmissionBullet: ResolverTypeWrapper<Partial<GqlSubmissionBullet>>;
  SubmissionOverrides: ResolverTypeWrapper<Partial<GqlSubmissionOverrides>>;
  SubmitApplicationInput: ResolverTypeWrapper<Partial<GqlSubmitApplicationInput>>;
  SubmitPredictionFeedbackInput: ResolverTypeWrapper<Partial<GqlSubmitPredictionFeedbackInput>>;
  Tag: ResolverTypeWrapper<Partial<Omit<GqlTag, 'partner'> & { partner: GqlResolversTypes['Partner'] }>>;
  TagAutomation: ResolverTypeWrapper<TagAutomation>;
  TagAutomationActionType: ResolverTypeWrapper<Partial<GqlTagAutomationActionType>>;
  TagAutomationMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlTagAutomationMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['TagAutomation']> }>>;
  TagAutomationTriggerType: ResolverTypeWrapper<Partial<GqlTagAutomationTriggerType>>;
  TagAutomationsMutations: ResolverTypeWrapper<Partial<Omit<GqlTagAutomationsMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversTypes['TagAutomationMutationResponse'], delete: GqlResolversTypes['TagAutomationMutationResponse'], update: GqlResolversTypes['TagAutomationMutationResponse'] }>>;
  TagMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlTagMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record: GqlResolversTypes['Tag'] }>>;
  TagMutations: ResolverTypeWrapper<Partial<Omit<GqlTagMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversTypes['TagsMutationResponse'], delete: GqlResolversTypes['TagsMutationResponse'], update: GqlResolversTypes['TagMutationResponse'] }>>;
  TagsMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlTagsMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<Array<GqlResolversTypes['Tag']>> }>>;
  TaxForm: ResolverTypeWrapper<Partial<Omit<GqlTaxForm, 'user'> & { user: GqlResolversTypes['User'] }>>;
  TaxFormMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlTaxFormMutationResponse, 'query' | 'record'> & { query: GqlResolversTypes['Query'], record?: Maybe<GqlResolversTypes['User']> }>>;
  TaxFormType: ResolverTypeWrapper<Partial<GqlTaxFormType>>;
  TemplateContent: Partial<ResolverTypeWrapper<GqlResolversUnionTypes<GqlResolversTypes>['TemplateContent']>>;
  TextField: ResolverTypeWrapper<Partial<GqlTextField>>;
  Time: ResolverTypeWrapper<Partial<Scalars['Time']['output']>>;
  TimeZone: ResolverTypeWrapper<Partial<Scalars['TimeZone']['output']>>;
  Timestamp: ResolverTypeWrapper<Partial<Scalars['Timestamp']['output']>>;
  TransitionCaseStatusesInput: ResolverTypeWrapper<Partial<GqlTransitionCaseStatusesInput>>;
  TransitionStatusAssignment: ResolverTypeWrapper<Partial<GqlTransitionStatusAssignment>>;
  TypographyField: ResolverTypeWrapper<Partial<GqlTypographyField>>;
  URL: ResolverTypeWrapper<Partial<Scalars['URL']['output']>>;
  USCurrency: ResolverTypeWrapper<Partial<Scalars['USCurrency']['output']>>;
  UUID: ResolverTypeWrapper<Partial<Scalars['UUID']['output']>>;
  UnlinkCaseParticipantInput: ResolverTypeWrapper<Partial<GqlUnlinkCaseParticipantInput>>;
  UnsignedFloat: ResolverTypeWrapper<Partial<Scalars['UnsignedFloat']['output']>>;
  UnsignedInt: ResolverTypeWrapper<Partial<Scalars['UnsignedInt']['output']>>;
  UpdateAdminInput: ResolverTypeWrapper<Partial<GqlUpdateAdminInput>>;
  UpdateApplicationInput: ResolverTypeWrapper<Partial<GqlUpdateApplicationInput>>;
  UpdateFundInput: ResolverTypeWrapper<Partial<GqlUpdateFundInput>>;
  UpdateNoteInput: ResolverTypeWrapper<Partial<GqlUpdateNoteInput>>;
  UpdatePaymentInput: ResolverTypeWrapper<Partial<GqlUpdatePaymentInput>>;
  UpdateProgramInput: ResolverTypeWrapper<Partial<GqlUpdateProgramInput>>;
  UpdateSavedViewInput: ResolverTypeWrapper<Partial<GqlUpdateSavedViewInput>>;
  UpdateTagAutomationInput: ResolverTypeWrapper<Partial<GqlUpdateTagAutomationInput>>;
  UpdateTagInput: ResolverTypeWrapper<Partial<GqlUpdateTagInput>>;
  UpdateUserInput: ResolverTypeWrapper<Partial<GqlUpdateUserInput>>;
  UpdateUserProfileInput: ResolverTypeWrapper<Partial<GqlUpdateUserProfileInput>>;
  UpdateVendorInput: ResolverTypeWrapper<Partial<GqlUpdateVendorInput>>;
  Upload: ResolverTypeWrapper<Partial<Scalars['Upload']['output']>>;
  UploadDocumentsInput: ResolverTypeWrapper<Partial<GqlUploadDocumentsInput>>;
  UploadVendorDocumentsInput: ResolverTypeWrapper<Partial<GqlUploadVendorDocumentsInput>>;
  UploadVerificationFileInput: ResolverTypeWrapper<Partial<GqlUploadVerificationFileInput>>;
  UpsertEnrollmentInput: ResolverTypeWrapper<Partial<GqlUpsertEnrollmentInput>>;
  UpsertEnrollmentOutcomeInput: ResolverTypeWrapper<Partial<GqlUpsertEnrollmentOutcomeInput>>;
  UpsertEnrollmentServiceInput: ResolverTypeWrapper<Partial<GqlUpsertEnrollmentServiceInput>>;
  UpsertLookupConfigInput: ResolverTypeWrapper<Partial<GqlUpsertLookupConfigInput>>;
  User: ResolverTypeWrapper<Partial<Omit<GqlUser, 'admin' | 'aggregatePayments' | 'applicantProfile' | 'applications' | 'documents' | 'enrollments' | 'partner' | 'referrals' | 'savedViews' | 'taxForms'> & { admin?: Maybe<GqlResolversTypes['Admin']>, aggregatePayments: GqlResolversTypes['AggregatePayment'], applicantProfile?: Maybe<GqlResolversTypes['ApplicantProfile']>, applications: Array<GqlResolversTypes['Application']>, documents: Array<GqlResolversTypes['Document']>, enrollments: Array<GqlResolversTypes['Enrollment']>, partner: GqlResolversTypes['Partner'], referrals: Array<GqlResolversTypes['ProgramReferral']>, savedViews?: Maybe<Array<GqlResolversTypes['SavedView']>>, taxForms: Array<GqlResolversTypes['TaxForm']> }>>;
  UserFilter: ResolverTypeWrapper<Partial<GqlUserFilter>>;
  UserMutations: ResolverTypeWrapper<Partial<Omit<GqlUserMutations, 'create' | 'createW9TaxForm' | 'linkLegacyUser' | 'saveBankAccount' | 'update' | 'updateProfile'> & { create: GqlResolversTypes['UserResponse'], createW9TaxForm: GqlResolversTypes['TaxFormMutationResponse'], linkLegacyUser: GqlResolversTypes['UserResponse'], saveBankAccount: GqlResolversTypes['UserResponse'], update: GqlResolversTypes['UserResponse'], updateProfile: GqlResolversTypes['UserResponse'] }>>;
  UserPage: ResolverTypeWrapper<Partial<Omit<GqlUserPage, 'users'> & { users: Array<GqlResolversTypes['User']> }>>;
  UserResponse: ResolverTypeWrapper<Partial<Omit<GqlUserResponse, 'record'> & { record?: Maybe<GqlResolversTypes['User']> }>>;
  UserSort: ResolverTypeWrapper<Partial<GqlUserSort>>;
  UserSortColumn: ResolverTypeWrapper<Partial<GqlUserSortColumn>>;
  UtcOffset: ResolverTypeWrapper<Partial<Scalars['UtcOffset']['output']>>;
  UtilityType: ResolverTypeWrapper<Partial<GqlUtilityType>>;
  Vendor: ResolverTypeWrapper<Partial<Omit<GqlVendor, 'aggregatePayments' | 'documents' | 'notes' | 'payments'> & { aggregatePayments: GqlResolversTypes['AggregatePayment'], documents: Array<GqlResolversTypes['Document']>, notes: Array<GqlResolversTypes['Note']>, payments: Array<GqlResolversTypes['Payment']> }>>;
  VendorFilter: ResolverTypeWrapper<Partial<GqlVendorFilter>>;
  VendorMutationResponse: ResolverTypeWrapper<Partial<Omit<GqlVendorMutationResponse, 'record'> & { record?: Maybe<GqlResolversTypes['Vendor']> }>>;
  VendorMutations: ResolverTypeWrapper<Partial<Omit<GqlVendorMutations, 'create' | 'delete' | 'removeDocuments' | 'update' | 'uploadDocuments'> & { create: GqlResolversTypes['VendorMutationResponse'], delete: GqlResolversTypes['VendorMutationResponse'], removeDocuments: GqlResolversTypes['VendorMutationResponse'], update: GqlResolversTypes['VendorMutationResponse'], uploadDocuments: GqlResolversTypes['VendorMutationResponse'] }>>;
  VendorPage: ResolverTypeWrapper<Partial<Omit<GqlVendorPage, 'nodes' | 'vendors'> & { nodes: Array<GqlResolversTypes['Vendor']>, vendors: Array<GqlResolversTypes['Vendor']> }>>;
  VendorSort: ResolverTypeWrapper<Partial<GqlVendorSort>>;
  VendorSortColumn: ResolverTypeWrapper<Partial<GqlVendorSortColumn>>;
  VendorType: ResolverTypeWrapper<Partial<GqlVendorType>>;
  VerificationConfig: ResolverTypeWrapper<Partial<GqlVerificationConfig>>;
  VerificationConfiguration: ResolverTypeWrapper<Partial<GqlVerificationConfiguration>>;
  VerificationDetail: ResolverTypeWrapper<Partial<GqlVerificationDetail>>;
  VerificationDiff: ResolverTypeWrapper<Partial<GqlVerificationDiff>>;
  VerificationFileKey: ResolverTypeWrapper<Partial<GqlVerificationFileKey>>;
  VerificationMetadata: ResolverTypeWrapper<Partial<GqlVerificationMetadata>>;
  VerificationServiceType: ResolverTypeWrapper<Partial<GqlVerificationServiceType>>;
  VerificationStatus: ResolverTypeWrapper<Partial<GqlVerificationStatus>>;
  VerifiedPageInfo: ResolverTypeWrapper<Partial<GqlVerifiedPageInfo>>;
  VerifyIdentityEmailInput: ResolverTypeWrapper<Partial<GqlVerifyIdentityEmailInput>>;
  Void: ResolverTypeWrapper<Partial<Scalars['Void']['output']>>;
  W9BusinessTaxClassifications: ResolverTypeWrapper<Partial<GqlW9BusinessTaxClassifications>>;
  W9FormData: ResolverTypeWrapper<Partial<GqlW9FormData>>;
  W9LLCTaxClassifications: ResolverTypeWrapper<Partial<GqlW9LlcTaxClassifications>>;
  W9TaxClassifications: ResolverTypeWrapper<Partial<GqlW9TaxClassifications>>;
  W9Tin: ResolverTypeWrapper<Partial<GqlW9Tin>>;
  Workflow: ResolverTypeWrapper<Partial<Omit<GqlWorkflow, 'workflowStages'> & { workflowStages: Array<GqlResolversTypes['WorkflowStage']> }>>;
  WorkflowEvent: ResolverTypeWrapper<Partial<Omit<GqlWorkflowEvent, 'author' | 'newAssignee' | 'previousAssignee' | 'user'> & { author?: Maybe<GqlResolversTypes['User']>, newAssignee?: Maybe<GqlResolversTypes['User']>, previousAssignee?: Maybe<GqlResolversTypes['User']>, user?: Maybe<GqlResolversTypes['User']> }>>;
  WorkflowNotification: ResolverTypeWrapper<Partial<Omit<GqlWorkflowNotification, 'template'> & { template: GqlResolversTypes['NotificationTemplate'] }>>;
  WorkflowStage: ResolverTypeWrapper<Partial<Omit<GqlWorkflowStage, 'workflowNotifications'> & { workflowNotifications: Array<GqlResolversTypes['WorkflowNotification']> }>>;
  WorkflowSummary: ResolverTypeWrapper<Partial<GqlWorkflowSummary>>;
  WorkflowSummaryFilter: ResolverTypeWrapper<Partial<GqlWorkflowSummaryFilter>>;
  YesNoUnsure: ResolverTypeWrapper<Partial<GqlYesNoUnsure>>;
};

/** Mapping between all available schema types and the resolvers parents */
export type GqlResolversParentTypes = {
  AccessRequest: Partial<GqlAccessRequest>;
  AccessRequestFilter: Partial<GqlAccessRequestFilter>;
  AccessRequestMutationResponse: Partial<Omit<GqlAccessRequestMutationResponse, 'query'> & { query: GqlResolversParentTypes['Query'] }>;
  AccessRequestMutations: Partial<Omit<GqlAccessRequestMutations, 'createAccessRequest' | 'reviewAccessRequest'> & { createAccessRequest: GqlResolversParentTypes['AccessRequestMutationResponse'], reviewAccessRequest: GqlResolversParentTypes['AccessRequestMutationResponse'] }>;
  AccessRequestPage: Partial<GqlAccessRequestPage>;
  AccountNumber: Partial<Scalars['AccountNumber']['output']>;
  ActiveLabel: Partial<GqlActiveLabel>;
  AddApplicationVersionInput: Partial<GqlAddApplicationVersionInput>;
  AddBulkCommentInput: Partial<GqlAddBulkCommentInput>;
  AddCommentInput: Partial<GqlAddCommentInput>;
  AddNoteInput: Partial<GqlAddNoteInput>;
  AddNoteToApplicationAnswerInput: Partial<GqlAddNoteToApplicationAnswerInput>;
  AddNoteToApplicationAnswerPayload: Partial<Omit<GqlAddNoteToApplicationAnswerPayload, 'applicationAnswer' | 'applicationAnswerNote' | 'note'> & { applicationAnswer?: Maybe<GqlResolversParentTypes['ApplicationAnswer']>, applicationAnswerNote?: Maybe<GqlResolversParentTypes['ApplicationAnswerNote']>, note?: Maybe<GqlResolversParentTypes['Note']> }>;
  AddProgramFundInput: Partial<GqlAddProgramFundInput>;
  AddReviewToApplicationAnswerInput: Partial<GqlAddReviewToApplicationAnswerInput>;
  AddReviewToApplicationAnswerPayload: Partial<Omit<GqlAddReviewToApplicationAnswerPayload, 'applicationAnswer' | 'applicationAnswerReview' | 'note'> & { applicationAnswer?: Maybe<GqlResolversParentTypes['ApplicationAnswer']>, applicationAnswerReview?: Maybe<GqlResolversParentTypes['ApplicationAnswerReview']>, note?: Maybe<GqlResolversParentTypes['Note']> }>;
  Address: Partial<GqlAddress>;
  AddressField: Partial<GqlAddressField>;
  AddressFieldFilter: Partial<GqlAddressFieldFilter>;
  AddressFilter: Partial<GqlAddressFilter>;
  AddressPage: Partial<GqlAddressPage>;
  Admin: Partial<Omit<GqlAdmin, 'assignments' | 'user'> & { assignments: Array<GqlResolversParentTypes['Assignment']>, user: GqlResolversParentTypes['User'] }>;
  AdminMutations: Partial<Omit<GqlAdminMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversParentTypes['AdminResponse'], delete: GqlResolversParentTypes['AdminResponse'], update: GqlResolversParentTypes['AdminResponse'] }>;
  AdminPage: Partial<Omit<GqlAdminPage, 'nodes'> & { nodes: Array<GqlResolversParentTypes['Admin']> }>;
  AdminResponse: Partial<Omit<GqlAdminResponse, 'query'> & { query: GqlResolversParentTypes['Query'] }>;
  AdvocateIdentityConfig: Partial<GqlAdvocateIdentityConfig>;
  AggregateApplicantsReferred: Partial<GqlAggregateApplicantsReferred>;
  AggregateApplication: Partial<GqlAggregateApplication>;
  AggregateApplicationFilter: Partial<GqlAggregateApplicationFilter>;
  AggregatePayment: Partial<GqlAggregatePayment>;
  AggregatePaymentFilter: Partial<GqlAggregatePaymentFilter>;
  AnalyticsResource: Partial<GqlAnalyticsResource>;
  ApplicantIdentityConfig: Partial<GqlApplicantIdentityConfig>;
  ApplicantNameConsistency: Partial<GqlApplicantNameConsistency>;
  ApplicantNameConsistencyResult: Partial<GqlApplicantNameConsistencyResult>;
  ApplicantProfile: Partial<Omit<GqlApplicantProfile, 'documents' | 'notes' | 'user'> & { documents: Array<GqlResolversParentTypes['Document']>, notes: Array<GqlResolversParentTypes['Note']>, user: GqlResolversParentTypes['User'] }>;
  ApplicantProfileConfig: Partial<GqlApplicantProfileConfig>;
  ApplicantProfileConfiguration: Partial<GqlApplicantProfileConfiguration>;
  ApplicantRolesConfig: Partial<GqlApplicantRolesConfig>;
  ApplicantType: Partial<GqlApplicantType>;
  Application: Partial<Omit<GqlApplication, 'case' | 'documents' | 'linkAttempts' | 'referral' | 'submitter' | 'versions'> & { case: GqlResolversParentTypes['Case'], documents: Array<GqlResolversParentTypes['Document']>, linkAttempts: Array<GqlResolversParentTypes['LinkAttempt']>, referral?: Maybe<GqlResolversParentTypes['ProgramReferral']>, submitter: GqlResolversParentTypes['User'], versions: Array<GqlResolversParentTypes['ApplicationVersion']> }>;
  ApplicationAddressUpdate: Partial<GqlApplicationAddressUpdate>;
  ApplicationAnswer: Partial<Omit<GqlApplicationAnswer, 'applicationAnswerNotes' | 'fieldReviews' | 'fieldReviewsRequiringResubmission'> & { applicationAnswerNotes: Array<GqlResolversParentTypes['ApplicationAnswerNote']>, fieldReviews: Array<GqlResolversParentTypes['ApplicationAnswerReview']>, fieldReviewsRequiringResubmission: Array<GqlResolversParentTypes['ApplicationAnswerReview']> }>;
  ApplicationAnswerNote: Partial<Omit<GqlApplicationAnswerNote, 'applicationAnswer' | 'note'> & { applicationAnswer: GqlResolversParentTypes['ApplicationAnswer'], note: GqlResolversParentTypes['Note'] }>;
  ApplicationAnswerReview: Partial<Omit<GqlApplicationAnswerReview, 'note'> & { note?: Maybe<GqlResolversParentTypes['Note']> }>;
  ApplicationConfigOverrides: Partial<GqlApplicationConfigOverrides>;
  ApplicationConfigReviewFields: Partial<GqlApplicationConfigReviewFields>;
  ApplicationConfigReviewFieldsInput: Partial<GqlApplicationConfigReviewFieldsInput>;
  ApplicationConfiguration: Partial<Omit<GqlApplicationConfiguration, 'sections'> & { sections: Array<GqlResolversParentTypes['ApplicationSection']> }>;
  ApplicationContentInput: Partial<GqlApplicationContentInput>;
  ApplicationDocumentUpdate: Partial<GqlApplicationDocumentUpdate>;
  ApplicationField: Partial<GqlResolversUnionTypes<GqlResolversParentTypes>['ApplicationField']>;
  ApplicationFieldValidation: Partial<GqlApplicationFieldValidation>;
  ApplicationFieldValidationRule: Partial<GqlApplicationFieldValidationRule>;
  ApplicationFilter: Partial<GqlApplicationFilter>;
  ApplicationMutationResponse: Partial<Omit<GqlApplicationMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Application']> }>;
  ApplicationMutations: Partial<Omit<GqlApplicationMutations, 'addVersion' | 'create' | 'evaluateEligibility' | 'submit' | 'update'> & { addVersion: GqlResolversParentTypes['ApplicationMutationResponse'], create: GqlResolversParentTypes['ApplicationMutationResponse'], evaluateEligibility: GqlResolversParentTypes['ApplicationMutationResponse'], submit: GqlResolversParentTypes['ApplicationMutationResponse'], update: GqlResolversParentTypes['ApplicationMutationResponse'] }>;
  ApplicationPage: Partial<Omit<GqlApplicationPage, 'applications'> & { applications: Array<GqlResolversParentTypes['Application']> }>;
  ApplicationQuestion: Partial<Omit<GqlApplicationQuestion, 'copy' | 'fields'> & { copy: GqlResolversParentTypes['ApplicationQuestionCopy'], fields: Array<GqlResolversParentTypes['ApplicationField']> }>;
  ApplicationQuestionCopy: Partial<GqlApplicationQuestionCopy>;
  ApplicationQuestionGroup: Partial<Omit<GqlApplicationQuestionGroup, 'overview' | 'questions'> & { overview?: Maybe<GqlResolversParentTypes['ApplicationQuestionGroupOverview']>, questions: Array<GqlResolversParentTypes['ApplicationQuestion']> }>;
  ApplicationQuestionGroupOverview: Partial<GqlApplicationQuestionGroupOverview>;
  ApplicationScore: Partial<GqlApplicationScore>;
  ApplicationSection: Partial<Omit<GqlApplicationSection, 'overview' | 'questionGroups'> & { overview: GqlResolversParentTypes['ApplicationSectionOverview'], questionGroups: Array<GqlResolversParentTypes['ApplicationQuestionGroup']> }>;
  ApplicationSectionOverview: Partial<GqlApplicationSectionOverview>;
  ApplicationSubmission: Partial<Omit<GqlApplicationSubmission, 'currentVersion' | 'documents' | 'latestSubmission' | 'submitter' | 'versions'> & { currentVersion: GqlResolversParentTypes['ApplicationVersion'], documents: Array<GqlResolversParentTypes['Document']>, latestSubmission: GqlResolversParentTypes['ApplicationVersion'], submitter: GqlResolversParentTypes['User'], versions: Array<GqlResolversParentTypes['ApplicationVersion']> }>;
  ApplicationVerification: Partial<GqlApplicationVerification>;
  ApplicationVersion: Partial<Omit<GqlApplicationVersion, 'creator' | 'mappedAnswers'> & { creator: GqlResolversParentTypes['User'], mappedAnswers: Array<GqlResolversParentTypes['SectionResponse']> }>;
  ApproveAmountInput: Partial<GqlApproveAmountInput>;
  ApproveCasePaymentsInput: Partial<GqlApproveCasePaymentsInput>;
  AssignToCaseInput: Partial<GqlAssignToCaseInput>;
  AssigneeId: Partial<Scalars['AssigneeId']['output']>;
  Assignment: Partial<Omit<GqlAssignment, 'case'> & { case?: Maybe<GqlResolversParentTypes['Case']> }>;
  BankAccount: Partial<GqlBankAccount>;
  BankAccountInput: Partial<GqlBankAccountInput>;
  BeginSessionInput: Partial<GqlBeginSessionInput>;
  BigInt: Partial<Scalars['BigInt']['output']>;
  Boolean: Partial<Scalars['Boolean']['output']>;
  BulkCaseMutationResponse: Partial<Omit<GqlBulkCaseMutationResponse, 'query' | 'records'> & { query: GqlResolversParentTypes['Query'], records?: Maybe<Array<GqlResolversParentTypes['Case']>> }>;
  BulkFulfillmentResponse: Partial<Omit<GqlBulkFulfillmentResponse, 'records'> & { records?: Maybe<Array<GqlResolversParentTypes['Fulfillment']>> }>;
  BulkIssueFundsInput: Partial<GqlBulkIssueFundsInput>;
  BulkResponseError: Partial<GqlBulkResponseError>;
  BulkResponseMetadata: Partial<GqlBulkResponseMetadata>;
  BulkUserMutationResponse: Partial<Omit<GqlBulkUserMutationResponse, 'query' | 'records'> & { query: GqlResolversParentTypes['Query'], records?: Maybe<Array<GqlResolversParentTypes['User']>> }>;
  Byte: Partial<Scalars['Byte']['output']>;
  CalculatedField: Partial<GqlCalculatedField>;
  CalculatedFieldDisplay: Partial<GqlCalculatedFieldDisplay>;
  Case: Case;
  CaseApplicationAnswer: Partial<GqlCaseApplicationAnswer>;
  CaseApplicationsDocument: Partial<Omit<GqlCaseApplicationsDocument, 'data'> & { data: GqlResolversParentTypes['CasesApplicationsSource'] }>;
  CaseCounts: Partial<GqlCaseCounts>;
  CaseFilter: Partial<GqlCaseFilter>;
  CaseMetadata: Partial<GqlCaseMetadata>;
  CaseMutationResponse: Partial<Omit<GqlCaseMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Case']> }>;
  CaseMutations: Partial<Omit<GqlCaseMutations, 'addBulkComment' | 'addComment' | 'addParticipantComment' | 'addTagToCase' | 'approvePayments' | 'expedite' | 'inviteParticipant' | 'overridePayments' | 'removeDocuments' | 'removeTagFromCase' | 'transitionStatuses' | 'undoExpedite' | 'unlinkParticipant' | 'uploadDocuments'> & { addBulkComment: GqlResolversParentTypes['BulkCaseMutationResponse'], addComment: GqlResolversParentTypes['CaseMutationResponse'], addParticipantComment: GqlResolversParentTypes['CaseMutationResponse'], addTagToCase: GqlResolversParentTypes['CaseTagMutationResponse'], approvePayments: GqlResolversParentTypes['BulkCaseMutationResponse'], expedite: GqlResolversParentTypes['BulkCaseMutationResponse'], inviteParticipant: GqlResolversParentTypes['CaseMutationResponse'], overridePayments: GqlResolversParentTypes['BulkCaseMutationResponse'], removeDocuments: GqlResolversParentTypes['CaseMutationResponse'], removeTagFromCase: GqlResolversParentTypes['CaseTagMutationResponse'], transitionStatuses: GqlResolversParentTypes['BulkCaseMutationResponse'], undoExpedite: GqlResolversParentTypes['BulkCaseMutationResponse'], unlinkParticipant: GqlResolversParentTypes['CaseMutationResponse'], uploadDocuments: GqlResolversParentTypes['CaseMutationResponse'] }>;
  CasePage: Partial<Omit<GqlCasePage, 'cases'> & { cases: Array<GqlResolversParentTypes['Case']> }>;
  CaseParticipant: Partial<Omit<GqlCaseParticipant, 'case' | 'invitationCodes' | 'linkAttempts'> & { case: GqlResolversParentTypes['Case'], invitationCodes: Array<GqlResolversParentTypes['InvitationCode']>, linkAttempts: Array<GqlResolversParentTypes['LinkAttempt']> }>;
  CaseSort: Partial<GqlCaseSort>;
  CaseTag: Partial<Omit<GqlCaseTag, 'case' | 'tag'> & { case: GqlResolversParentTypes['Case'], tag: GqlResolversParentTypes['Tag'] }>;
  CaseTagMutationResponse: Partial<Omit<GqlCaseTagMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['CaseTag']> }>;
  CasesApplicationsSource: Partial<Omit<GqlCasesApplicationsSource, 'applicationAnswers'> & { applicationAnswers?: Maybe<Array<GqlResolversParentTypes['ApplicationAnswer']>> }>;
  CasesMutationResponse: Partial<Omit<GqlCasesMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<Array<GqlResolversParentTypes['Case']>> }>;
  Changelog: Partial<GqlChangelog>;
  ChannelConfiguration: Partial<Omit<GqlChannelConfiguration, 'template'> & { template: GqlResolversParentTypes['NotificationTemplate'] }>;
  CheckboxField: Partial<GqlCheckboxField>;
  CheckboxGroupField: Partial<GqlCheckboxGroupField>;
  ClaimCheckInput: Partial<GqlClaimCheckInput>;
  ClaimDirectDepositInput: Partial<GqlClaimDirectDepositInput>;
  ClaimFulfillmentResponse: Partial<Omit<GqlClaimFulfillmentResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Fulfillment']> }>;
  ClaimPhysicalCardInput: Partial<GqlClaimPhysicalCardInput>;
  ClaimVirtualCardInput: Partial<GqlClaimVirtualCardInput>;
  ClaimZelleInput: Partial<GqlClaimZelleInput>;
  Comment: Partial<Omit<GqlComment, 'author' | 'case' | 'children' | 'parent'> & { author?: Maybe<GqlResolversParentTypes['User']>, case?: Maybe<GqlResolversParentTypes['Case']>, children?: Maybe<Array<GqlResolversParentTypes['Comment']>>, parent?: Maybe<GqlResolversParentTypes['Comment']> }>;
  CommunicationChannelsConfig: Partial<GqlCommunicationChannelsConfig>;
  CommunicationChannelsConfiguration: Partial<GqlCommunicationChannelsConfiguration>;
  CommunicationPreferences: Partial<GqlCommunicationPreferences>;
  CommunicationPreferencesInput: Partial<GqlCommunicationPreferencesInput>;
  ComplexField: Partial<Omit<GqlComplexField, 'subFields'> & { subFields: Array<GqlResolversParentTypes['ApplicationField']> }>;
  CountryCode: Partial<Scalars['CountryCode']['output']>;
  CreateAccessRequestInput: Partial<GqlCreateAccessRequestInput>;
  CreateAddressInput: Partial<GqlCreateAddressInput>;
  CreateAdminInput: Partial<GqlCreateAdminInput>;
  CreateApplicationInput: Partial<GqlCreateApplicationInput>;
  CreateBulkProgramReferralInput: Partial<GqlCreateBulkProgramReferralInput>;
  CreateCaseTagInput: Partial<GqlCreateCaseTagInput>;
  CreateFundInput: Partial<GqlCreateFundInput>;
  CreateIncidentInput: Partial<GqlCreateIncidentInput>;
  CreatePaymentInput: Partial<GqlCreatePaymentInput>;
  CreateProgramInput: Partial<GqlCreateProgramInput>;
  CreateReferralInput: Partial<GqlCreateReferralInput>;
  CreateSavedViewInput: Partial<GqlCreateSavedViewInput>;
  CreateTag: Partial<GqlCreateTag>;
  CreateTagAutomationInput: Partial<GqlCreateTagAutomationInput>;
  CreateTagsInput: Partial<GqlCreateTagsInput>;
  CreateUserInput: Partial<GqlCreateUserInput>;
  CreateVendorInput: Partial<GqlCreateVendorInput>;
  CreateW9TaxFormInput: Partial<GqlCreateW9TaxFormInput>;
  Cuid: Partial<Scalars['Cuid']['output']>;
  Currency: Partial<Scalars['Currency']['output']>;
  CursorPageInfo: Partial<GqlCursorPageInfo>;
  CursorPagination: Partial<GqlCursorPagination>;
  DID: Partial<Scalars['DID']['output']>;
  DataLookupConfiguration: Partial<GqlDataLookupConfiguration>;
  DataLookupField: Partial<GqlDataLookupField>;
  DataLookupFieldInput: Partial<GqlDataLookupFieldInput>;
  Date: Partial<Scalars['Date']['output']>;
  DateField: Partial<GqlDateField>;
  DateFieldValidation: Partial<GqlDateFieldValidation>;
  DateRange: Partial<GqlDateRange>;
  DateTime: Partial<Scalars['DateTime']['output']>;
  DateTimeISO: Partial<Scalars['DateTimeISO']['output']>;
  DeleteAdminInput: Partial<GqlDeleteAdminInput>;
  DeleteCaseTagInput: Partial<GqlDeleteCaseTagInput>;
  DeleteSavedViewInput: Partial<GqlDeleteSavedViewInput>;
  DeleteTagAutomationInput: Partial<GqlDeleteTagAutomationInput>;
  DeleteTagsInput: Partial<GqlDeleteTagsInput>;
  DeleteVendorInput: Partial<GqlDeleteVendorInput>;
  DeweyDecimal: Partial<Scalars['DeweyDecimal']['output']>;
  DoctopusTag: Partial<GqlDoctopusTag>;
  Document: Partial<Omit<GqlDocument, 'summary' | 'uploader'> & { summary?: Maybe<GqlResolversParentTypes['DocumentSummary']>, uploader?: Maybe<GqlResolversParentTypes['User']> }>;
  DocumentConsistency: Partial<GqlDocumentConsistency>;
  DocumentField: Partial<GqlDocumentField>;
  DocumentFieldCopy: Partial<GqlDocumentFieldCopy>;
  DocumentMutations: Partial<Omit<GqlDocumentMutations, 'pinDocument' | 'submitFeedback'> & { pinDocument: GqlResolversParentTypes['DocumentsMutationResponse'], submitFeedback: GqlResolversParentTypes['DocumentsMutationResponse'] }>;
  DocumentSummary: Partial<Omit<GqlDocumentSummary, 'prediction'> & { prediction?: Maybe<GqlResolversParentTypes['Prediction']> }>;
  DocumentTag: Partial<GqlDocumentTag>;
  DocumentsMutationResponse: Partial<Omit<GqlDocumentsMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Document']> }>;
  DropdownField: Partial<GqlDropdownField>;
  Duration: Partial<Scalars['Duration']['output']>;
  EligibilityConfig: Partial<GqlEligibilityConfig>;
  EligibilityQuestion: Partial<GqlEligibilityQuestion>;
  EmailAddress: Partial<Scalars['EmailAddress']['output']>;
  EmailTemplateContent: Partial<GqlEmailTemplateContent>;
  EndSessionInput: Partial<GqlEndSessionInput>;
  Enrollment: Partial<Omit<GqlEnrollment, 'program'> & { program: GqlResolversParentTypes['Program'] }>;
  EnrollmentIdentifier: Partial<GqlEnrollmentIdentifier>;
  EnrollmentMutationResponse: Partial<Omit<GqlEnrollmentMutationResponse, 'record'> & { record?: Maybe<GqlResolversParentTypes['Enrollment']> }>;
  EnrollmentMutations: Partial<Omit<GqlEnrollmentMutations, 'removeOutcome' | 'removeService' | 'upsert' | 'upsertOutcome' | 'upsertService'> & { removeOutcome: GqlResolversParentTypes['EnrollmentMutationResponse'], removeService: GqlResolversParentTypes['EnrollmentMutationResponse'], upsert: GqlResolversParentTypes['EnrollmentMutationResponse'], upsertOutcome: GqlResolversParentTypes['EnrollmentMutationResponse'], upsertService: GqlResolversParentTypes['EnrollmentMutationResponse'] }>;
  EnrollmentOutcome: Partial<GqlEnrollmentOutcome>;
  EnrollmentService: Partial<GqlEnrollmentService>;
  ExpediteCasesInput: Partial<GqlExpediteCasesInput>;
  ExportTableToCSVInput: Partial<GqlExportTableToCsvInput>;
  Feature: Partial<GqlFeature>;
  FeatureSetting: Partial<GqlFeatureSetting>;
  Feedback: Partial<Omit<GqlFeedback, 'admin'> & { admin: GqlResolversParentTypes['Admin'] }>;
  FieldResponse: Partial<Omit<GqlFieldResponse, 'answer' | 'field'> & { answer?: Maybe<GqlResolversParentTypes['ApplicationAnswer']>, field: GqlResolversParentTypes['ApplicationField'] }>;
  Float: Partial<Scalars['Float']['output']>;
  FormDefinition: Partial<GqlFormDefinition>;
  Fulfillment: Partial<Omit<GqlFulfillment, 'case' | 'fulfillmentMeta' | 'fund' | 'payments'> & { case: GqlResolversParentTypes['Case'], fulfillmentMeta?: Maybe<GqlResolversParentTypes['FulfillmentMeta']>, fund: GqlResolversParentTypes['Fund'], payments?: Maybe<Array<GqlResolversParentTypes['Payment']>> }>;
  FulfillmentFilter: Partial<GqlFulfillmentFilter>;
  FulfillmentMeta: Partial<Omit<GqlFulfillmentMeta, 'fulfillment'> & { fulfillment: GqlResolversParentTypes['Fulfillment'] }>;
  FulfillmentMutations: Partial<Omit<GqlFulfillmentMutations, 'approveAmount' | 'bulkIssueFunds' | 'claimCheck' | 'claimDirectDeposit' | 'claimPhysicalCard' | 'claimVirtualCard' | 'claimZelle' | 'issueFunds'> & { approveAmount: GqlResolversParentTypes['FulfillmentResponse'], bulkIssueFunds: GqlResolversParentTypes['BulkFulfillmentResponse'], claimCheck: GqlResolversParentTypes['ClaimFulfillmentResponse'], claimDirectDeposit: GqlResolversParentTypes['ClaimFulfillmentResponse'], claimPhysicalCard: GqlResolversParentTypes['ClaimFulfillmentResponse'], claimVirtualCard: GqlResolversParentTypes['ClaimFulfillmentResponse'], claimZelle: GqlResolversParentTypes['ClaimFulfillmentResponse'], issueFunds: GqlResolversParentTypes['ClaimFulfillmentResponse'] }>;
  FulfillmentPage: Partial<Omit<GqlFulfillmentPage, 'fulfillments'> & { fulfillments: Array<GqlResolversParentTypes['Fulfillment']> }>;
  FulfillmentResponse: Partial<Omit<GqlFulfillmentResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Fulfillment']> }>;
  Fund: Partial<Omit<GqlFund, 'config' | 'programs'> & { config?: Maybe<GqlResolversParentTypes['FundConfig']>, programs: Array<GqlResolversParentTypes['Program']> }>;
  FundConfig: Partial<GqlFundConfig>;
  FundFilter: Partial<GqlFundFilter>;
  FundMutationResponse: Partial<Omit<GqlFundMutationResponse, 'record'> & { record?: Maybe<GqlResolversParentTypes['Fund']> }>;
  FundMutations: Partial<Omit<GqlFundMutations, 'create' | 'remove' | 'update'> & { create: GqlResolversParentTypes['FundMutationResponse'], remove: GqlResolversParentTypes['FundMutationResponse'], update: GqlResolversParentTypes['FundMutationResponse'] }>;
  FundPage: Partial<Omit<GqlFundPage, 'nodes'> & { nodes: Array<GqlResolversParentTypes['Fund']> }>;
  FundSort: Partial<GqlFundSort>;
  FundStats: Partial<GqlFundStats>;
  GUID: Partial<Scalars['GUID']['output']>;
  GeoPoint: Partial<GqlGeoPoint>;
  HSL: Partial<Scalars['HSL']['output']>;
  HSLA: Partial<Scalars['HSLA']['output']>;
  HasAccessInput: Partial<GqlHasAccessInput>;
  HasAccessResponse: Partial<GqlHasAccessResponse>;
  HexColorCode: Partial<Scalars['HexColorCode']['output']>;
  Hexadecimal: Partial<Scalars['Hexadecimal']['output']>;
  HouseholdLimit: Partial<GqlHouseholdLimit>;
  IBAN: Partial<Scalars['IBAN']['output']>;
  ID: Partial<Scalars['ID']['output']>;
  IP: Partial<Scalars['IP']['output']>;
  IPCPatent: Partial<Scalars['IPCPatent']['output']>;
  IPv4: Partial<Scalars['IPv4']['output']>;
  IPv6: Partial<Scalars['IPv6']['output']>;
  ISBN: Partial<Scalars['ISBN']['output']>;
  ISO8601Duration: Partial<Scalars['ISO8601Duration']['output']>;
  IdentityConfig: Partial<GqlIdentityConfig>;
  IdentityMutations: Partial<GqlIdentityMutations>;
  IdentityResponse: Partial<GqlIdentityResponse>;
  IdentityUser: Partial<GqlIdentityUser>;
  IncidentMessage: Partial<Omit<GqlIncidentMessage, 'partners'> & { partners?: Maybe<Array<GqlResolversParentTypes['Partner']>> }>;
  IncidentMessageFilter: Partial<GqlIncidentMessageFilter>;
  IncidentMessagePage: Partial<Omit<GqlIncidentMessagePage, 'incidentMessages' | 'nodes'> & { incidentMessages: Array<GqlResolversParentTypes['IncidentMessage']>, nodes: Array<GqlResolversParentTypes['IncidentMessage']> }>;
  IncidentMutationResponse: Partial<GqlIncidentMutationResponse>;
  IncomeLimit: Partial<GqlIncomeLimit>;
  IncomeLimitArea: Partial<GqlIncomeLimitArea>;
  IncomeLimitAreaFilter: Partial<GqlIncomeLimitAreaFilter>;
  IncomeLimitAreaPage: Partial<GqlIncomeLimitAreaPage>;
  InputMonth: Partial<GqlInputMonth>;
  Int: Partial<Scalars['Int']['output']>;
  IntroPage: Partial<GqlIntroPage>;
  IntroPageContentBlock: Partial<GqlIntroPageContentBlock>;
  InvitationCode: Partial<Omit<GqlInvitationCode, 'caseParticipant'> & { caseParticipant: GqlResolversParentTypes['CaseParticipant'] }>;
  InviteCaseParticipantInput: Partial<GqlInviteCaseParticipantInput>;
  IssueFundsInput: Partial<GqlIssueFundsInput>;
  JSON: Partial<Scalars['JSON']['output']>;
  JSONObject: Partial<Scalars['JSONObject']['output']>;
  JWT: Partial<Scalars['JWT']['output']>;
  LCCSubclass: Partial<Scalars['LCCSubclass']['output']>;
  Latitude: Partial<Scalars['Latitude']['output']>;
  Limits: Partial<GqlLimits>;
  LinkAttempt: Partial<Omit<GqlLinkAttempt, 'application' | 'caseParticipant' | 'originalCase'> & { application: GqlResolversParentTypes['Application'], caseParticipant: GqlResolversParentTypes['CaseParticipant'], originalCase: GqlResolversParentTypes['Case'] }>;
  LinkLegacyUserInput: Partial<GqlLinkLegacyUserInput>;
  LocalDate: Partial<Scalars['LocalDate']['output']>;
  LocalDateTime: Partial<Scalars['LocalDateTime']['output']>;
  LocalEndTime: Partial<Scalars['LocalEndTime']['output']>;
  LocalTime: Partial<Scalars['LocalTime']['output']>;
  Locale: Partial<Scalars['Locale']['output']>;
  LoginDetails: Partial<GqlLoginDetails>;
  Long: Partial<Scalars['Long']['output']>;
  Longitude: Partial<Scalars['Longitude']['output']>;
  MAC: Partial<Scalars['MAC']['output']>;
  ModelVersion: Partial<GqlModelVersion>;
  Month: Partial<GqlMonth>;
  Mutation: {};
  NegativeFloat: Partial<Scalars['NegativeFloat']['output']>;
  NegativeInt: Partial<Scalars['NegativeInt']['output']>;
  NonEmptyString: Partial<Scalars['NonEmptyString']['output']>;
  NonNegativeFloat: Partial<Scalars['NonNegativeFloat']['output']>;
  NonNegativeInt: Partial<Scalars['NonNegativeInt']['output']>;
  NonPositiveFloat: Partial<Scalars['NonPositiveFloat']['output']>;
  NonPositiveInt: Partial<Scalars['NonPositiveInt']['output']>;
  Note: Partial<Omit<GqlNote, 'author' | 'case'> & { author: GqlResolversParentTypes['Admin'], case: GqlResolversParentTypes['Case'] }>;
  NoteMutationResponse: Partial<Omit<GqlNoteMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Note']> }>;
  NoteMutations: Partial<Omit<GqlNoteMutations, 'add' | 'update'> & { add: GqlResolversParentTypes['NoteMutationResponse'], update: GqlResolversParentTypes['NoteMutationResponse'] }>;
  NotificationAction: Partial<GqlNotificationAction>;
  NotificationConfig: Partial<Omit<GqlNotificationConfig, 'channelConfigurations'> & { channelConfigurations: Array<GqlResolversParentTypes['ChannelConfiguration']> }>;
  NotificationTemplate: Partial<Omit<GqlNotificationTemplate, 'content'> & { content: GqlResolversParentTypes['TemplateContent'] }>;
  OCRResult: Partial<GqlOcrResult>;
  ObjectID: Partial<Scalars['ObjectID']['output']>;
  ObjectReference: Partial<GqlObjectReference>;
  OffsetPagination: Partial<GqlOffsetPagination>;
  OptionLabelValue: Partial<GqlOptionLabelValue>;
  Outcome: Partial<GqlOutcome>;
  OverrideCasePaymentsInput: Partial<GqlOverrideCasePaymentsInput>;
  OverridePaymentInput: Partial<GqlOverridePaymentInput>;
  PageInfo: Partial<GqlPageInfo>;
  ParticipantInput: Partial<GqlParticipantInput>;
  Partner: Partial<Omit<GqlPartner, 'analyticsResources' | 'funds' | 'parent' | 'programs' | 'savedViews' | 'tagAutomations' | 'tags'> & { analyticsResources: Array<GqlResolversParentTypes['AnalyticsResource']>, funds: Array<GqlResolversParentTypes['Fund']>, parent?: Maybe<GqlResolversParentTypes['Partner']>, programs: Array<GqlResolversParentTypes['Program']>, savedViews?: Maybe<Array<GqlResolversParentTypes['SavedView']>>, tagAutomations: Array<GqlResolversParentTypes['TagAutomation']>, tags: Array<GqlResolversParentTypes['Tag']> }>;
  PartnerConfig: Partial<GqlPartnerConfig>;
  PartnerFilter: Partial<GqlPartnerFilter>;
  PartnerIncident: Partial<GqlPartnerIncident>;
  PartnerIncidentMutations: Partial<GqlPartnerIncidentMutations>;
  PartnerPage: Partial<Omit<GqlPartnerPage, 'partners'> & { partners: Array<GqlResolversParentTypes['Partner']> }>;
  PartnerWhitelabeling: Partial<GqlPartnerWhitelabeling>;
  Password: Partial<Scalars['Password']['output']>;
  Payee: Partial<GqlResolversUnionTypes<GqlResolversParentTypes>['Payee']>;
  Payment: Partial<Omit<GqlPayment, 'fulfillment' | 'payee'> & { fulfillment: GqlResolversParentTypes['Fulfillment'], payee?: Maybe<GqlResolversParentTypes['Payee']> }>;
  PaymentInput: Partial<GqlPaymentInput>;
  PaymentMailingAddress: Partial<GqlPaymentMailingAddress>;
  PaymentMutationResponse: Partial<Omit<GqlPaymentMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Payment']> }>;
  PaymentMutations: Partial<Omit<GqlPaymentMutations, 'create' | 'remove' | 'update'> & { create: GqlResolversParentTypes['PaymentMutationResponse'], remove: GqlResolversParentTypes['PaymentMutationResponse'], update: GqlResolversParentTypes['PaymentMutationResponse'] }>;
  PaymentPage: Partial<Omit<GqlPaymentPage, 'payments'> & { payments: Array<GqlResolversParentTypes['Payment']> }>;
  PaymentPattern: Partial<GqlPaymentPattern>;
  PaymentSort: Partial<GqlPaymentSort>;
  PaymentsConfig: Partial<GqlPaymentsConfig>;
  PhoneNumber: Partial<Scalars['PhoneNumber']['output']>;
  PinDocumentInput: Partial<GqlPinDocumentInput>;
  Port: Partial<Scalars['Port']['output']>;
  PositiveFloat: Partial<Scalars['PositiveFloat']['output']>;
  PositiveInt: Partial<Scalars['PositiveInt']['output']>;
  PostalCode: Partial<Scalars['PostalCode']['output']>;
  Prediction: Partial<Omit<GqlPrediction, 'feedback'> & { feedback: Array<GqlResolversParentTypes['Feedback']> }>;
  PresetGuestTokenInput: Partial<GqlPresetGuestTokenInput>;
  PresetMutations: Partial<GqlPresetMutations>;
  Primitive: Partial<Scalars['Primitive']['output']>;
  ProfileAnswer: Partial<Omit<GqlProfileAnswer, 'profile'> & { profile: GqlResolversParentTypes['ApplicantProfile'] }>;
  ProfileKey: Partial<GqlProfileKey>;
  Program: Partial<Omit<GqlProgram, 'applicantTypes' | 'applicationConfiguration' | 'applicationConfigurations' | 'documents' | 'funds' | 'notifications' | 'workflow'> & { applicantTypes: Array<GqlResolversParentTypes['ProgramApplicantType']>, applicationConfiguration?: Maybe<GqlResolversParentTypes['ApplicationConfiguration']>, applicationConfigurations: Array<GqlResolversParentTypes['ProgramApplicationConfiguration']>, documents?: Maybe<Array<GqlResolversParentTypes['ProgramDocument']>>, funds: Array<GqlResolversParentTypes['Fund']>, notifications?: Maybe<Array<GqlResolversParentTypes['NotificationConfig']>>, workflow: GqlResolversParentTypes['Workflow'] }>;
  ProgramApplicantType: Partial<GqlProgramApplicantType>;
  ProgramApplicationConfiguration: Partial<Omit<GqlProgramApplicationConfiguration, 'configuration'> & { configuration: GqlResolversParentTypes['ApplicationConfiguration'] }>;
  ProgramConfig: Partial<GqlProgramConfig>;
  ProgramContext: Partial<GqlProgramContext>;
  ProgramContextInput: Partial<GqlProgramContextInput>;
  ProgramDocument: Partial<Omit<GqlProgramDocument, 'document'> & { document?: Maybe<GqlResolversParentTypes['Document']> }>;
  ProgramFilter: Partial<GqlProgramFilter>;
  ProgramFundStats: Partial<GqlProgramFundStats>;
  ProgramMutationResponse: Partial<Omit<GqlProgramMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['Program']> }>;
  ProgramMutations: Partial<Omit<GqlProgramMutations, 'addProgramFund' | 'create' | 'removeProgramFund' | 'update' | 'uploadVerificationFile' | 'upsertLookupConfig'> & { addProgramFund: GqlResolversParentTypes['ProgramMutationResponse'], create: GqlResolversParentTypes['ProgramMutationResponse'], removeProgramFund: GqlResolversParentTypes['ProgramMutationResponse'], update: GqlResolversParentTypes['ProgramsMutationResponse'], uploadVerificationFile: GqlResolversParentTypes['ProgramMutationResponse'], upsertLookupConfig: GqlResolversParentTypes['ProgramMutationResponse'] }>;
  ProgramPage: Partial<Omit<GqlProgramPage, 'programs'> & { programs: Array<GqlResolversParentTypes['Program']> }>;
  ProgramReferral: Partial<Omit<GqlProgramReferral, 'admin' | 'program' | 'user'> & { admin: GqlResolversParentTypes['Admin'], program: GqlResolversParentTypes['Program'], user: GqlResolversParentTypes['User'] }>;
  ProgramReferralMutationResponse: Partial<Omit<GqlProgramReferralMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['ProgramReferral']> }>;
  ProgramReferralMutations: Partial<Omit<GqlProgramReferralMutations, 'create' | 'createBulkProgramReferral'> & { create: GqlResolversParentTypes['ProgramReferralMutationResponse'], createBulkProgramReferral: GqlResolversParentTypes['BulkUserMutationResponse'] }>;
  ProgramStats: Partial<GqlProgramStats>;
  ProgramsMutationResponse: Partial<Omit<GqlProgramsMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<Array<GqlResolversParentTypes['Program']>> }>;
  Query: {};
  QuestionGroupResponse: Partial<Omit<GqlQuestionGroupResponse, 'questions'> & { questions: Array<GqlResolversParentTypes['QuestionResponse']> }>;
  QuestionResponse: Partial<Omit<GqlQuestionResponse, 'fields'> & { fields: Array<GqlResolversParentTypes['FieldResponse']> }>;
  RGB: Partial<Scalars['RGB']['output']>;
  RGBA: Partial<Scalars['RGBA']['output']>;
  RadioListField: Partial<GqlRadioListField>;
  ReapplicationRules: Partial<GqlReapplicationRules>;
  RecurringPaymentConfig: Partial<GqlRecurringPaymentConfig>;
  RemoveDocumentsInput: Partial<GqlRemoveDocumentsInput>;
  RemoveEnrollmentOutcomeInput: Partial<GqlRemoveEnrollmentOutcomeInput>;
  RemoveEnrollmentServiceInput: Partial<GqlRemoveEnrollmentServiceInput>;
  RemoveFundInput: Partial<GqlRemoveFundInput>;
  RemoveIncidentInput: Partial<GqlRemoveIncidentInput>;
  RemovePaymentInput: Partial<GqlRemovePaymentInput>;
  RemoveProgramFundInput: Partial<GqlRemoveProgramFundInput>;
  RemoveVendorDocumentsInput: Partial<GqlRemoveVendorDocumentsInput>;
  ResolvedEntities: Partial<GqlResolvedEntities>;
  ResponseMetadata: Partial<GqlResponseMetadata>;
  ReviewAccessRequestInput: Partial<GqlReviewAccessRequestInput>;
  RoleConfig: Partial<GqlRoleConfig>;
  RoutingNumber: Partial<Scalars['RoutingNumber']['output']>;
  Ruleset: Partial<GqlRuleset>;
  RulesetPage: Partial<GqlRulesetPage>;
  RulesetSort: Partial<GqlRulesetSort>;
  SAMLConfig: Partial<GqlSamlConfig>;
  SESSN: Partial<Scalars['SESSN']['output']>;
  SafeInt: Partial<Scalars['SafeInt']['output']>;
  SaveUserBankAccountInput: Partial<GqlSaveUserBankAccountInput>;
  SavedView: Partial<Omit<GqlSavedView, 'author' | 'partner'> & { author: GqlResolversParentTypes['User'], partner: GqlResolversParentTypes['Partner'] }>;
  SavedViewMutationResponse: Partial<Omit<GqlSavedViewMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['SavedView']> }>;
  SavedViewMutations: Partial<Omit<GqlSavedViewMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversParentTypes['SavedViewMutationResponse'], delete: GqlResolversParentTypes['SavedViewMutationResponse'], update: GqlResolversParentTypes['SavedViewMutationResponse'] }>;
  SearchFilter: Partial<GqlSearchFilter>;
  SearchInput: Partial<GqlSearchInput>;
  SearchPage: Partial<Omit<GqlSearchPage, 'nodes'> & { nodes: Array<GqlResolversParentTypes['CaseApplicationsDocument']> }>;
  SearchSort: Partial<GqlSearchSort>;
  SectionResponse: Partial<Omit<GqlSectionResponse, 'questionGroups'> & { questionGroups: Array<GqlResolversParentTypes['QuestionGroupResponse']> }>;
  SemVer: Partial<Scalars['SemVer']['output']>;
  SendIdentityVerificationEmailInput: Partial<GqlSendIdentityVerificationEmailInput>;
  SendMagicLinkInput: Partial<GqlSendMagicLinkInput>;
  Service: Partial<GqlService>;
  SmsTemplateContent: Partial<GqlSmsTemplateContent>;
  StatusFilters: Partial<GqlStatusFilters>;
  StatusOverride: Partial<GqlStatusOverride>;
  String: Partial<Scalars['String']['output']>;
  SubmissionBullet: Partial<GqlSubmissionBullet>;
  SubmissionOverrides: Partial<GqlSubmissionOverrides>;
  SubmitApplicationInput: Partial<GqlSubmitApplicationInput>;
  SubmitPredictionFeedbackInput: Partial<GqlSubmitPredictionFeedbackInput>;
  Tag: Partial<Omit<GqlTag, 'partner'> & { partner: GqlResolversParentTypes['Partner'] }>;
  TagAutomation: TagAutomation;
  TagAutomationMutationResponse: Partial<Omit<GqlTagAutomationMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['TagAutomation']> }>;
  TagAutomationsMutations: Partial<Omit<GqlTagAutomationsMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversParentTypes['TagAutomationMutationResponse'], delete: GqlResolversParentTypes['TagAutomationMutationResponse'], update: GqlResolversParentTypes['TagAutomationMutationResponse'] }>;
  TagMutationResponse: Partial<Omit<GqlTagMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record: GqlResolversParentTypes['Tag'] }>;
  TagMutations: Partial<Omit<GqlTagMutations, 'create' | 'delete' | 'update'> & { create: GqlResolversParentTypes['TagsMutationResponse'], delete: GqlResolversParentTypes['TagsMutationResponse'], update: GqlResolversParentTypes['TagMutationResponse'] }>;
  TagsMutationResponse: Partial<Omit<GqlTagsMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<Array<GqlResolversParentTypes['Tag']>> }>;
  TaxForm: Partial<Omit<GqlTaxForm, 'user'> & { user: GqlResolversParentTypes['User'] }>;
  TaxFormMutationResponse: Partial<Omit<GqlTaxFormMutationResponse, 'query' | 'record'> & { query: GqlResolversParentTypes['Query'], record?: Maybe<GqlResolversParentTypes['User']> }>;
  TemplateContent: Partial<GqlResolversUnionTypes<GqlResolversParentTypes>['TemplateContent']>;
  TextField: Partial<GqlTextField>;
  Time: Partial<Scalars['Time']['output']>;
  TimeZone: Partial<Scalars['TimeZone']['output']>;
  Timestamp: Partial<Scalars['Timestamp']['output']>;
  TransitionCaseStatusesInput: Partial<GqlTransitionCaseStatusesInput>;
  TransitionStatusAssignment: Partial<GqlTransitionStatusAssignment>;
  TypographyField: Partial<GqlTypographyField>;
  URL: Partial<Scalars['URL']['output']>;
  USCurrency: Partial<Scalars['USCurrency']['output']>;
  UUID: Partial<Scalars['UUID']['output']>;
  UnlinkCaseParticipantInput: Partial<GqlUnlinkCaseParticipantInput>;
  UnsignedFloat: Partial<Scalars['UnsignedFloat']['output']>;
  UnsignedInt: Partial<Scalars['UnsignedInt']['output']>;
  UpdateAdminInput: Partial<GqlUpdateAdminInput>;
  UpdateApplicationInput: Partial<GqlUpdateApplicationInput>;
  UpdateFundInput: Partial<GqlUpdateFundInput>;
  UpdateNoteInput: Partial<GqlUpdateNoteInput>;
  UpdatePaymentInput: Partial<GqlUpdatePaymentInput>;
  UpdateProgramInput: Partial<GqlUpdateProgramInput>;
  UpdateSavedViewInput: Partial<GqlUpdateSavedViewInput>;
  UpdateTagAutomationInput: Partial<GqlUpdateTagAutomationInput>;
  UpdateTagInput: Partial<GqlUpdateTagInput>;
  UpdateUserInput: Partial<GqlUpdateUserInput>;
  UpdateUserProfileInput: Partial<GqlUpdateUserProfileInput>;
  UpdateVendorInput: Partial<GqlUpdateVendorInput>;
  Upload: Partial<Scalars['Upload']['output']>;
  UploadDocumentsInput: Partial<GqlUploadDocumentsInput>;
  UploadVendorDocumentsInput: Partial<GqlUploadVendorDocumentsInput>;
  UploadVerificationFileInput: Partial<GqlUploadVerificationFileInput>;
  UpsertEnrollmentInput: Partial<GqlUpsertEnrollmentInput>;
  UpsertEnrollmentOutcomeInput: Partial<GqlUpsertEnrollmentOutcomeInput>;
  UpsertEnrollmentServiceInput: Partial<GqlUpsertEnrollmentServiceInput>;
  UpsertLookupConfigInput: Partial<GqlUpsertLookupConfigInput>;
  User: Partial<Omit<GqlUser, 'admin' | 'aggregatePayments' | 'applicantProfile' | 'applications' | 'documents' | 'enrollments' | 'partner' | 'referrals' | 'savedViews' | 'taxForms'> & { admin?: Maybe<GqlResolversParentTypes['Admin']>, aggregatePayments: GqlResolversParentTypes['AggregatePayment'], applicantProfile?: Maybe<GqlResolversParentTypes['ApplicantProfile']>, applications: Array<GqlResolversParentTypes['Application']>, documents: Array<GqlResolversParentTypes['Document']>, enrollments: Array<GqlResolversParentTypes['Enrollment']>, partner: GqlResolversParentTypes['Partner'], referrals: Array<GqlResolversParentTypes['ProgramReferral']>, savedViews?: Maybe<Array<GqlResolversParentTypes['SavedView']>>, taxForms: Array<GqlResolversParentTypes['TaxForm']> }>;
  UserFilter: Partial<GqlUserFilter>;
  UserMutations: Partial<Omit<GqlUserMutations, 'create' | 'createW9TaxForm' | 'linkLegacyUser' | 'saveBankAccount' | 'update' | 'updateProfile'> & { create: GqlResolversParentTypes['UserResponse'], createW9TaxForm: GqlResolversParentTypes['TaxFormMutationResponse'], linkLegacyUser: GqlResolversParentTypes['UserResponse'], saveBankAccount: GqlResolversParentTypes['UserResponse'], update: GqlResolversParentTypes['UserResponse'], updateProfile: GqlResolversParentTypes['UserResponse'] }>;
  UserPage: Partial<Omit<GqlUserPage, 'users'> & { users: Array<GqlResolversParentTypes['User']> }>;
  UserResponse: Partial<Omit<GqlUserResponse, 'record'> & { record?: Maybe<GqlResolversParentTypes['User']> }>;
  UserSort: Partial<GqlUserSort>;
  UtcOffset: Partial<Scalars['UtcOffset']['output']>;
  Vendor: Partial<Omit<GqlVendor, 'aggregatePayments' | 'documents' | 'notes' | 'payments'> & { aggregatePayments: GqlResolversParentTypes['AggregatePayment'], documents: Array<GqlResolversParentTypes['Document']>, notes: Array<GqlResolversParentTypes['Note']>, payments: Array<GqlResolversParentTypes['Payment']> }>;
  VendorFilter: Partial<GqlVendorFilter>;
  VendorMutationResponse: Partial<Omit<GqlVendorMutationResponse, 'record'> & { record?: Maybe<GqlResolversParentTypes['Vendor']> }>;
  VendorMutations: Partial<Omit<GqlVendorMutations, 'create' | 'delete' | 'removeDocuments' | 'update' | 'uploadDocuments'> & { create: GqlResolversParentTypes['VendorMutationResponse'], delete: GqlResolversParentTypes['VendorMutationResponse'], removeDocuments: GqlResolversParentTypes['VendorMutationResponse'], update: GqlResolversParentTypes['VendorMutationResponse'], uploadDocuments: GqlResolversParentTypes['VendorMutationResponse'] }>;
  VendorPage: Partial<Omit<GqlVendorPage, 'nodes' | 'vendors'> & { nodes: Array<GqlResolversParentTypes['Vendor']>, vendors: Array<GqlResolversParentTypes['Vendor']> }>;
  VendorSort: Partial<GqlVendorSort>;
  VendorType: Partial<GqlVendorType>;
  VerificationConfig: Partial<GqlVerificationConfig>;
  VerificationConfiguration: Partial<GqlVerificationConfiguration>;
  VerificationDetail: Partial<GqlVerificationDetail>;
  VerificationDiff: Partial<GqlVerificationDiff>;
  VerificationFileKey: Partial<GqlVerificationFileKey>;
  VerificationMetadata: Partial<GqlVerificationMetadata>;
  VerifiedPageInfo: Partial<GqlVerifiedPageInfo>;
  VerifyIdentityEmailInput: Partial<GqlVerifyIdentityEmailInput>;
  Void: Partial<Scalars['Void']['output']>;
  W9FormData: Partial<GqlW9FormData>;
  Workflow: Partial<Omit<GqlWorkflow, 'workflowStages'> & { workflowStages: Array<GqlResolversParentTypes['WorkflowStage']> }>;
  WorkflowEvent: Partial<Omit<GqlWorkflowEvent, 'author' | 'newAssignee' | 'previousAssignee' | 'user'> & { author?: Maybe<GqlResolversParentTypes['User']>, newAssignee?: Maybe<GqlResolversParentTypes['User']>, previousAssignee?: Maybe<GqlResolversParentTypes['User']>, user?: Maybe<GqlResolversParentTypes['User']> }>;
  WorkflowNotification: Partial<Omit<GqlWorkflowNotification, 'template'> & { template: GqlResolversParentTypes['NotificationTemplate'] }>;
  WorkflowStage: Partial<Omit<GqlWorkflowStage, 'workflowNotifications'> & { workflowNotifications: Array<GqlResolversParentTypes['WorkflowNotification']> }>;
  WorkflowSummary: Partial<GqlWorkflowSummary>;
  WorkflowSummaryFilter: Partial<GqlWorkflowSummaryFilter>;
};

export type GqlConstraintDirectiveArgs = {
  contains?: Maybe<Scalars['String']['input']>;
  endsWith?: Maybe<Scalars['String']['input']>;
  exclusiveMax?: Maybe<Scalars['Float']['input']>;
  exclusiveMin?: Maybe<Scalars['Float']['input']>;
  format?: Maybe<Scalars['String']['input']>;
  max?: Maybe<Scalars['Float']['input']>;
  maxItems?: Maybe<Scalars['Int']['input']>;
  maxLength?: Maybe<Scalars['Int']['input']>;
  min?: Maybe<Scalars['Float']['input']>;
  minItems?: Maybe<Scalars['Int']['input']>;
  minLength?: Maybe<Scalars['Int']['input']>;
  multipleOf?: Maybe<Scalars['Float']['input']>;
  notContains?: Maybe<Scalars['String']['input']>;
  pattern?: Maybe<Scalars['String']['input']>;
  startsWith?: Maybe<Scalars['String']['input']>;
  uniqueTypeName?: Maybe<Scalars['String']['input']>;
};

export type GqlConstraintDirectiveResolver<Result, Parent, ContextType = AuthenticatedContext, Args = GqlConstraintDirectiveArgs> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type GqlExperimentalDirectiveArgs = {
  reason: Scalars['String']['input'];
};

export type GqlExperimentalDirectiveResolver<Result, Parent, ContextType = AuthenticatedContext, Args = GqlExperimentalDirectiveArgs> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type GqlMaskedDirectiveArgs = {
  prefix: Scalars['String']['input'];
  shorten?: Maybe<Scalars['Int']['input']>;
};

export type GqlMaskedDirectiveResolver<Result, Parent, ContextType = AuthenticatedContext, Args = GqlMaskedDirectiveArgs> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type GqlAccessRequestResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AccessRequest'] = GqlResolversParentTypes['AccessRequest']> = {
  approved?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  partnerId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  relation?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  requestDetail?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  requester?: Resolver<GqlResolversTypes['IdentityUser'], ParentType, ContextType>;
  resourceId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  resourceType?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  reviewDetail?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  reviewer?: Resolver<Maybe<GqlResolversTypes['IdentityUser']>, ParentType, ContextType>;
  subjectId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  subjectType?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAccessRequestMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AccessRequestMutationResponse'] = GqlResolversParentTypes['AccessRequestMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['AccessRequest']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAccessRequestMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AccessRequestMutations'] = GqlResolversParentTypes['AccessRequestMutations']> = {
  createAccessRequest?: Resolver<GqlResolversTypes['AccessRequestMutationResponse'], ParentType, ContextType, RequireFields<GqlAccessRequestMutationsCreateAccessRequestArgs, 'input'>>;
  reviewAccessRequest?: Resolver<GqlResolversTypes['AccessRequestMutationResponse'], ParentType, ContextType, RequireFields<GqlAccessRequestMutationsReviewAccessRequestArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAccessRequestPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AccessRequestPage'] = GqlResolversParentTypes['AccessRequestPage']> = {
  accessRequests?: Resolver<Array<GqlResolversTypes['AccessRequest']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlAccountNumberScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['AccountNumber'], any> {
  name: 'AccountNumber';
}

export type GqlActiveLabelResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ActiveLabel'] = GqlResolversParentTypes['ActiveLabel']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  modelVersion?: Resolver<Maybe<GqlResolversTypes['ModelVersion']>, ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAddNoteToApplicationAnswerPayloadResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AddNoteToApplicationAnswerPayload'] = GqlResolversParentTypes['AddNoteToApplicationAnswerPayload']> = {
  applicationAnswer?: Resolver<Maybe<GqlResolversTypes['ApplicationAnswer']>, ParentType, ContextType>;
  applicationAnswerNote?: Resolver<Maybe<GqlResolversTypes['ApplicationAnswerNote']>, ParentType, ContextType>;
  message?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  note?: Resolver<Maybe<GqlResolversTypes['Note']>, ParentType, ContextType>;
  success?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAddReviewToApplicationAnswerPayloadResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AddReviewToApplicationAnswerPayload'] = GqlResolversParentTypes['AddReviewToApplicationAnswerPayload']> = {
  applicationAnswer?: Resolver<Maybe<GqlResolversTypes['ApplicationAnswer']>, ParentType, ContextType>;
  applicationAnswerReview?: Resolver<Maybe<GqlResolversTypes['ApplicationAnswerReview']>, ParentType, ContextType>;
  message?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  note?: Resolver<Maybe<GqlResolversTypes['Note']>, ParentType, ContextType>;
  success?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAddressResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Address'] = GqlResolversParentTypes['Address']> = {
  addressLine1?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  addressLine2?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  city?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  incomeLimitArea?: Resolver<Maybe<GqlResolversTypes['IncomeLimitArea']>, ParentType, ContextType>;
  latitude?: Resolver<Maybe<GqlResolversTypes['Latitude']>, ParentType, ContextType>;
  longitude?: Resolver<Maybe<GqlResolversTypes['Longitude']>, ParentType, ContextType>;
  state?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  zip?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAddressFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AddressField'] = GqlResolversParentTypes['AddressField']> = {
  allowUnhoused?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  copyAddressKey?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  filter?: Resolver<Maybe<GqlResolversTypes['AddressFieldFilter']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAddressFieldFilterResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AddressFieldFilter'] = GqlResolversParentTypes['AddressFieldFilter']> = {
  city?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  state?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAddressPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AddressPage'] = GqlResolversParentTypes['AddressPage']> = {
  addresses?: Resolver<Array<GqlResolversTypes['Address']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAdminResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Admin'] = GqlResolversParentTypes['Admin']> = {
  archivedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  assignments?: Resolver<Array<GqlResolversTypes['Assignment']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  identityUser?: Resolver<GqlResolversTypes['IdentityUser'], ParentType, ContextType>;
  roles?: Resolver<Array<GqlResolversTypes['PortalRole']>, ParentType, ContextType>;
  user?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAdminMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AdminMutations'] = GqlResolversParentTypes['AdminMutations']> = {
  create?: Resolver<GqlResolversTypes['AdminResponse'], ParentType, ContextType, RequireFields<GqlAdminMutationsCreateArgs, 'input'>>;
  delete?: Resolver<GqlResolversTypes['AdminResponse'], ParentType, ContextType, RequireFields<GqlAdminMutationsDeleteArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['AdminResponse'], ParentType, ContextType, RequireFields<GqlAdminMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAdminPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AdminPage'] = GqlResolversParentTypes['AdminPage']> = {
  nodes?: Resolver<Array<GqlResolversTypes['Admin']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAdminResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AdminResponse'] = GqlResolversParentTypes['AdminResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAdvocateIdentityConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AdvocateIdentityConfig'] = GqlResolversParentTypes['AdvocateIdentityConfig']> = {
  saml?: Resolver<Maybe<GqlResolversTypes['SAMLConfig']>, ParentType, ContextType>;
  tenantId?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAggregateApplicantsReferredResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AggregateApplicantsReferred'] = GqlResolversParentTypes['AggregateApplicantsReferred']> = {
  count?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAggregateApplicationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AggregateApplication'] = GqlResolversParentTypes['AggregateApplication']> = {
  count?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAggregatePaymentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AggregatePayment'] = GqlResolversParentTypes['AggregatePayment']> = {
  count?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  sum?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlAnalyticsResourceResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['AnalyticsResource'] = GqlResolversParentTypes['AnalyticsResource']> = {
  dashboardId?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  displayInPortal?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  url?: Resolver<GqlResolversTypes['URL'], ParentType, ContextType>;
  workspaceId?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantIdentityConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantIdentityConfig'] = GqlResolversParentTypes['ApplicantIdentityConfig']> = {
  tenantId?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantNameConsistencyResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantNameConsistency'] = GqlResolversParentTypes['ApplicantNameConsistency']> = {
  result?: Resolver<Maybe<GqlResolversTypes['ApplicantNameConsistencyResult']>, ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['ResolverStatus'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantNameConsistencyResultResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantNameConsistencyResult'] = GqlResolversParentTypes['ApplicantNameConsistencyResult']> = {
  canonicalName?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  documents?: Resolver<Array<GqlResolversTypes['DocumentConsistency']>, ParentType, ContextType>;
  overallSimilarity?: Resolver<GqlResolversTypes['Similarity'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantProfileResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantProfile'] = GqlResolversParentTypes['ApplicantProfile']> = {
  addresses?: Resolver<Array<GqlResolversTypes['Address']>, ParentType, ContextType>;
  answers?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  applicantType?: Resolver<Maybe<GqlResolversTypes['ApplicantType']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  documents?: Resolver<Array<GqlResolversTypes['Document']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  mailingAddress?: Resolver<Maybe<GqlResolversTypes['Address']>, ParentType, ContextType>;
  notes?: Resolver<Array<GqlResolversTypes['Note']>, ParentType, ContextType>;
  secondaryEmail?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  secondaryPhone?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  user?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantProfileConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantProfileConfig'] = GqlResolversParentTypes['ApplicantProfileConfig']> = {
  profileKeys?: Resolver<Maybe<Array<GqlResolversTypes['ProfileKey']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantProfileConfigurationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantProfileConfiguration'] = GqlResolversParentTypes['ApplicantProfileConfiguration']> = {
  applicantTypeId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  config?: Resolver<Maybe<GqlResolversTypes['ApplicantProfileConfig']>, ParentType, ContextType>;
  partnerId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantRolesConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantRolesConfig'] = GqlResolversParentTypes['ApplicantRolesConfig']> = {
  FIRST_PARTY?: Resolver<Maybe<GqlResolversTypes['RoleConfig']>, ParentType, ContextType>;
  THIRD_PARTY?: Resolver<Maybe<GqlResolversTypes['RoleConfig']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicantTypeResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicantType'] = GqlResolversParentTypes['ApplicantType']> = {
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  role?: Resolver<GqlResolversTypes['ApplicantTypeRole'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Application'] = GqlResolversParentTypes['Application']> = {
  addresses?: Resolver<Array<GqlResolversTypes['Address']>, ParentType, ContextType>;
  answers?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  case?: Resolver<GqlResolversTypes['Case'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  displayId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  documents?: Resolver<Array<GqlResolversTypes['Document']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  linkAttempts?: Resolver<Array<GqlResolversTypes['LinkAttempt']>, ParentType, ContextType>;
  referral?: Resolver<Maybe<GqlResolversTypes['ProgramReferral']>, ParentType, ContextType>;
  score?: Resolver<Maybe<GqlResolversTypes['ApplicationScore']>, ParentType, ContextType>;
  submittedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  submitter?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  verification?: Resolver<Maybe<GqlResolversTypes['ApplicationVerification']>, ParentType, ContextType>;
  versions?: Resolver<Array<GqlResolversTypes['ApplicationVersion']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationAnswerResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationAnswer'] = GqlResolversParentTypes['ApplicationAnswer']> = {
  applicationAnswerNotes?: Resolver<Array<GqlResolversTypes['ApplicationAnswerNote']>, ParentType, ContextType>;
  fieldReviews?: Resolver<Array<GqlResolversTypes['ApplicationAnswerReview']>, ParentType, ContextType>;
  fieldReviewsRequiringResubmission?: Resolver<Array<GqlResolversTypes['ApplicationAnswerReview']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  key?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationAnswerNoteResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationAnswerNote'] = GqlResolversParentTypes['ApplicationAnswerNote']> = {
  applicationAnswer?: Resolver<GqlResolversTypes['ApplicationAnswer'], ParentType, ContextType>;
  applicationAnswerId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  caseManagerId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  note?: Resolver<GqlResolversTypes['Note'], ParentType, ContextType>;
  noteId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationAnswerReviewResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationAnswerReview'] = GqlResolversParentTypes['ApplicationAnswerReview']> = {
  applicationAnswerId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  caseManagerId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  note?: Resolver<Maybe<GqlResolversTypes['Note']>, ParentType, ContextType>;
  noteId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  reviewStatus?: Resolver<GqlResolversTypes['ApplicationAnswerReviewStatus'], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationConfigOverridesResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationConfigOverrides'] = GqlResolversParentTypes['ApplicationConfigOverrides']> = {
  statuses?: Resolver<Maybe<Array<GqlResolversTypes['StatusOverride']>>, ParentType, ContextType>;
  submission?: Resolver<Maybe<GqlResolversTypes['SubmissionOverrides']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationConfigReviewFieldsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationConfigReviewFields'] = GqlResolversParentTypes['ApplicationConfigReviewFields']> = {
  appConfigId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  fields?: Resolver<Array<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationConfigurationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationConfiguration'] = GqlResolversParentTypes['ApplicationConfiguration']> = {
  introPages?: Resolver<Array<GqlResolversTypes['IntroPage']>, ParentType, ContextType>;
  overrides?: Resolver<Maybe<GqlResolversTypes['ApplicationConfigOverrides']>, ParentType, ContextType>;
  sections?: Resolver<Array<GqlResolversTypes['ApplicationSection']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationField'] = GqlResolversParentTypes['ApplicationField']> = {
  __resolveType: TypeResolveFn<'AddressField' | 'CalculatedField' | 'CheckboxField' | 'CheckboxGroupField' | 'ComplexField' | 'DateField' | 'DocumentField' | 'DropdownField' | 'RadioListField' | 'TextField' | 'TypographyField', ParentType, ContextType>;
};

export type GqlApplicationFieldValidationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationFieldValidation'] = GqlResolversParentTypes['ApplicationFieldValidation']> = {
  condition?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  required?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  rules?: Resolver<Maybe<Array<GqlResolversTypes['ApplicationFieldValidationRule']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationFieldValidationRuleResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationFieldValidationRule'] = GqlResolversParentTypes['ApplicationFieldValidationRule']> = {
  message?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  rule?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationMutationResponse'] = GqlResolversParentTypes['ApplicationMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Application']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationMutations'] = GqlResolversParentTypes['ApplicationMutations']> = {
  addVersion?: Resolver<GqlResolversTypes['ApplicationMutationResponse'], ParentType, ContextType, RequireFields<GqlApplicationMutationsAddVersionArgs, 'input'>>;
  create?: Resolver<GqlResolversTypes['ApplicationMutationResponse'], ParentType, ContextType, RequireFields<GqlApplicationMutationsCreateArgs, 'input'>>;
  evaluateEligibility?: Resolver<GqlResolversTypes['ApplicationMutationResponse'], ParentType, ContextType, RequireFields<GqlApplicationMutationsEvaluateEligibilityArgs, 'input'>>;
  submit?: Resolver<GqlResolversTypes['ApplicationMutationResponse'], ParentType, ContextType, RequireFields<GqlApplicationMutationsSubmitArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['ApplicationMutationResponse'], ParentType, ContextType, RequireFields<GqlApplicationMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationPage'] = GqlResolversParentTypes['ApplicationPage']> = {
  applications?: Resolver<Array<GqlResolversTypes['Application']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationQuestionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationQuestion'] = GqlResolversParentTypes['ApplicationQuestion']> = {
  copy?: Resolver<GqlResolversTypes['ApplicationQuestionCopy'], ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  fields?: Resolver<Array<GqlResolversTypes['ApplicationField']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  layout?: Resolver<Maybe<GqlResolversTypes['ApplicationQuestionLayout']>, ParentType, ContextType>;
  skippable?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationQuestionCopyResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationQuestionCopy'] = GqlResolversParentTypes['ApplicationQuestionCopy']> = {
  intro?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  title?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationQuestionGroupResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationQuestionGroup'] = GqlResolversParentTypes['ApplicationQuestionGroup']> = {
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  overview?: Resolver<Maybe<GqlResolversTypes['ApplicationQuestionGroupOverview']>, ParentType, ContextType>;
  questions?: Resolver<Array<GqlResolversTypes['ApplicationQuestion']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationQuestionGroupOverviewResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationQuestionGroupOverview'] = GqlResolversParentTypes['ApplicationQuestionGroupOverview']> = {
  description?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  title?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationScoreResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationScore'] = GqlResolversParentTypes['ApplicationScore']> = {
  algorithmVersion?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  applicationVersionId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  score?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationSectionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationSection'] = GqlResolversParentTypes['ApplicationSection']> = {
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  overview?: Resolver<GqlResolversTypes['ApplicationSectionOverview'], ParentType, ContextType>;
  questionGroups?: Resolver<Array<GqlResolversTypes['ApplicationQuestionGroup']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationSectionOverviewResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationSectionOverview'] = GqlResolversParentTypes['ApplicationSectionOverview']> = {
  description?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  title?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationSubmissionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationSubmission'] = GqlResolversParentTypes['ApplicationSubmission']> = {
  addresses?: Resolver<Maybe<Array<GqlResolversTypes['Address']>>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  currentVersion?: Resolver<GqlResolversTypes['ApplicationVersion'], ParentType, ContextType>;
  displayId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  documents?: Resolver<Array<GqlResolversTypes['Document']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  latestSubmission?: Resolver<GqlResolversTypes['ApplicationVersion'], ParentType, ContextType>;
  submitter?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  versions?: Resolver<Array<GqlResolversTypes['ApplicationVersion']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationVerificationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationVerification'] = GqlResolversParentTypes['ApplicationVerification']> = {
  confidence?: Resolver<GqlResolversTypes['Float'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  details?: Resolver<Maybe<Array<GqlResolversTypes['VerificationDetail']>>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  metadata?: Resolver<Maybe<Array<GqlResolversTypes['VerificationMetadata']>>, ParentType, ContextType>;
  score?: Resolver<Maybe<GqlResolversTypes['Float']>, ParentType, ContextType>;
  service?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlApplicationVersionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ApplicationVersion'] = GqlResolversParentTypes['ApplicationVersion']> = {
  answers?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  applicationId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  creator?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  creatorId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  eligibility?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  eligibilityReason?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  lastEvaluatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  mappedAnswers?: Resolver<Array<GqlResolversTypes['SectionResponse']>, ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  verification?: Resolver<Maybe<GqlResolversTypes['ApplicationVerification']>, ParentType, ContextType>;
  versionScore?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlAssigneeIdScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['AssigneeId'], any> {
  name: 'AssigneeId';
}

export type GqlAssignmentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Assignment'] = GqlResolversParentTypes['Assignment']> = {
  assigneeId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  case?: Resolver<Maybe<GqlResolversTypes['Case']>, ParentType, ContextType>;
  caseId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlBankAccountResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['BankAccount'] = GqlResolversParentTypes['BankAccount']> = {
  accountNumber?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  accountType?: Resolver<GqlResolversTypes['AccountType'], ParentType, ContextType>;
  routingNumber?: Resolver<GqlResolversTypes['RoutingNumber'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlBigIntScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['BigInt'], any> {
  name: 'BigInt';
}

export type GqlBulkCaseMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['BulkCaseMutationResponse'] = GqlResolversParentTypes['BulkCaseMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['BulkResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  records?: Resolver<Maybe<Array<GqlResolversTypes['Case']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlBulkFulfillmentResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['BulkFulfillmentResponse'] = GqlResolversParentTypes['BulkFulfillmentResponse']> = {
  metadata?: Resolver<GqlResolversTypes['BulkResponseMetadata'], ParentType, ContextType>;
  records?: Resolver<Maybe<Array<GqlResolversTypes['Fulfillment']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlBulkResponseErrorResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['BulkResponseError'] = GqlResolversParentTypes['BulkResponseError']> = {
  id?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  message?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlBulkResponseMetadataResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['BulkResponseMetadata'] = GqlResolversParentTypes['BulkResponseMetadata']> = {
  errors?: Resolver<Maybe<Array<GqlResolversTypes['BulkResponseError']>>, ParentType, ContextType>;
  ids?: Resolver<Maybe<Array<GqlResolversTypes['UUID']>>, ParentType, ContextType>;
  message?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlBulkUserMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['BulkUserMutationResponse'] = GqlResolversParentTypes['BulkUserMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['BulkResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  records?: Resolver<Maybe<Array<GqlResolversTypes['User']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlByteScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Byte'], any> {
  name: 'Byte';
}

export type GqlCalculatedFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CalculatedField'] = GqlResolversParentTypes['CalculatedField']> = {
  display?: Resolver<Maybe<GqlResolversTypes['CalculatedFieldDisplay']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  formula?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCalculatedFieldDisplayResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CalculatedFieldDisplay'] = GqlResolversParentTypes['CalculatedFieldDisplay']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  inputType?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Case'] = GqlResolversParentTypes['Case']> = {
  applications?: Resolver<Array<GqlResolversTypes['Application']>, ParentType, ContextType>;
  assignee?: Resolver<Maybe<GqlResolversTypes['Admin']>, ParentType, ContextType>;
  caseTags?: Resolver<Array<GqlResolversTypes['CaseTag']>, ParentType, ContextType>;
  comments?: Resolver<Maybe<Array<GqlResolversTypes['Comment']>>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  decisionReachedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  displayId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  documents?: Resolver<Array<GqlResolversTypes['Document']>, ParentType, ContextType>;
  fulfillments?: Resolver<Array<GqlResolversTypes['Fulfillment']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  metadata?: Resolver<GqlResolversTypes['CaseMetadata'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  notes?: Resolver<Array<GqlResolversTypes['Note']>, ParentType, ContextType>;
  participants?: Resolver<Array<GqlResolversTypes['CaseParticipant']>, ParentType, ContextType>;
  priority?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  program?: Resolver<GqlResolversTypes['Program'], ParentType, ContextType>;
  resolvedEntities?: Resolver<GqlResolversTypes['ResolvedEntities'], ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['CaseStatus'], ParentType, ContextType>;
  statusUpdatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  workflowEvents?: Resolver<Array<GqlResolversTypes['WorkflowEvent']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseApplicationAnswerResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseApplicationAnswer'] = GqlResolversParentTypes['CaseApplicationAnswer']> = {
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  value?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseApplicationsDocumentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseApplicationsDocument'] = GqlResolversParentTypes['CaseApplicationsDocument']> = {
  data?: Resolver<GqlResolversTypes['CasesApplicationsSource'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['ID'], ParentType, ContextType>;
  index?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseCountsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseCounts'] = GqlResolversParentTypes['CaseCounts']> = {
  All?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  Approved?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  Archived?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  Denied?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  FiscalReview?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  InProgress?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  InReview?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  Incomplete?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  PaymentSent?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  PendingCertification?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  ReadyForReview?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  Withdrawn?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseMetadataResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseMetadata'] = GqlResolversParentTypes['CaseMetadata']> = {
  denialReason?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  incompleteReason?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  intakeStaff?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  withdrawalReason?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseMutationResponse'] = GqlResolversParentTypes['CaseMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Case']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseMutations'] = GqlResolversParentTypes['CaseMutations']> = {
  addBulkComment?: Resolver<GqlResolversTypes['BulkCaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsAddBulkCommentArgs, 'input'>>;
  addComment?: Resolver<GqlResolversTypes['CaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsAddCommentArgs, 'input'>>;
  addParticipantComment?: Resolver<GqlResolversTypes['CaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsAddParticipantCommentArgs, 'input'>>;
  addTagToCase?: Resolver<GqlResolversTypes['CaseTagMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsAddTagToCaseArgs, 'input'>>;
  approvePayments?: Resolver<GqlResolversTypes['BulkCaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsApprovePaymentsArgs, 'input'>>;
  expedite?: Resolver<GqlResolversTypes['BulkCaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsExpediteArgs, 'input'>>;
  inviteParticipant?: Resolver<GqlResolversTypes['CaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsInviteParticipantArgs, 'input'>>;
  overridePayments?: Resolver<GqlResolversTypes['BulkCaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsOverridePaymentsArgs, 'input'>>;
  removeDocuments?: Resolver<GqlResolversTypes['CaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsRemoveDocumentsArgs, 'input'>>;
  removeTagFromCase?: Resolver<GqlResolversTypes['CaseTagMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsRemoveTagFromCaseArgs, 'input'>>;
  transitionStatuses?: Resolver<GqlResolversTypes['BulkCaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsTransitionStatusesArgs, 'input'>>;
  undoExpedite?: Resolver<GqlResolversTypes['BulkCaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsUndoExpediteArgs, 'input'>>;
  unlinkParticipant?: Resolver<GqlResolversTypes['CaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsUnlinkParticipantArgs, 'input'>>;
  uploadDocuments?: Resolver<GqlResolversTypes['CaseMutationResponse'], ParentType, ContextType, RequireFields<GqlCaseMutationsUploadDocumentsArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCasePageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CasePage'] = GqlResolversParentTypes['CasePage']> = {
  cases?: Resolver<Array<GqlResolversTypes['Case']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseParticipantResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseParticipant'] = GqlResolversParentTypes['CaseParticipant']> = {
  applicantType?: Resolver<GqlResolversTypes['ApplicantType'], ParentType, ContextType>;
  case?: Resolver<GqlResolversTypes['Case'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  email?: Resolver<GqlResolversTypes['EmailAddress'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  invitationCodes?: Resolver<Array<GqlResolversTypes['InvitationCode']>, ParentType, ContextType>;
  linkAttempts?: Resolver<Array<GqlResolversTypes['LinkAttempt']>, ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseTagResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseTag'] = GqlResolversParentTypes['CaseTag']> = {
  case?: Resolver<GqlResolversTypes['Case'], ParentType, ContextType>;
  caseId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  tag?: Resolver<GqlResolversTypes['Tag'], ParentType, ContextType>;
  tagId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCaseTagMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CaseTagMutationResponse'] = GqlResolversParentTypes['CaseTagMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['CaseTag']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCasesApplicationsSourceResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CasesApplicationsSource'] = GqlResolversParentTypes['CasesApplicationsSource']> = {
  applicationAnswers?: Resolver<Maybe<Array<GqlResolversTypes['ApplicationAnswer']>>, ParentType, ContextType>;
  applicationCreatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  applicationDeactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  applicationDisplayId?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  applicationEligibility?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  applicationEligibilityReason?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  applicationId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  applicationReferralId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  applicationRequestedAmount?: Resolver<Maybe<GqlResolversTypes['Float']>, ParentType, ContextType>;
  applicationSubmittedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  applicationSubmitterId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  applicationUpdatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  applicationVerificationScore?: Resolver<Maybe<GqlResolversTypes['Float']>, ParentType, ContextType>;
  assigneeId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  assigneeName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  caseApplicantTypeIds?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  caseApplicantTypes?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  caseCreatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  caseDeactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  caseDecisionReachedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  caseDisplayId?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  caseId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  caseLegacyId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  caseName?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  caseParticipantCount?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  casePartnerId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  casePriority?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  caseProgramId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  caseProgramName?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  caseProgramStatus?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  caseStatus?: Resolver<GqlResolversTypes['CaseStatus'], ParentType, ContextType>;
  caseStatusUpdatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  caseTagIds?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  caseTags?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  documentIds?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  documentTagIds?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  documentTags?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  hasFailedLink?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  hasMissingParticipant?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  hasPendingLink?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  location?: Resolver<Maybe<GqlResolversTypes['GeoPoint']>, ParentType, ContextType>;
  partnerName?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  paymentStatus?: Resolver<Maybe<Array<GqlResolversTypes['PaymentStatus']>>, ParentType, ContextType>;
  reviewStatus?: Resolver<Maybe<Array<GqlResolversTypes['String']>>, ParentType, ContextType>;
  submitterDisplayId?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  submitterEmail?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  submitterLegacyId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  submitterName?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  submitterPhone?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCasesMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CasesMutationResponse'] = GqlResolversParentTypes['CasesMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<Array<GqlResolversTypes['Case']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlChangelogResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Changelog'] = GqlResolversParentTypes['Changelog']> = {
  content?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlChannelConfigurationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ChannelConfiguration'] = GqlResolversParentTypes['ChannelConfiguration']> = {
  channel?: Resolver<GqlResolversTypes['NotificationChannel'], ParentType, ContextType>;
  enabled?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  template?: Resolver<GqlResolversTypes['NotificationTemplate'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCheckboxFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CheckboxField'] = GqlResolversParentTypes['CheckboxField']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCheckboxGroupFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CheckboxGroupField'] = GqlResolversParentTypes['CheckboxGroupField']> = {
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  options?: Resolver<Array<GqlResolversTypes['OptionLabelValue']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlClaimFulfillmentResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ClaimFulfillmentResponse'] = GqlResolversParentTypes['ClaimFulfillmentResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Fulfillment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCommentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Comment'] = GqlResolversParentTypes['Comment']> = {
  author?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  authorId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  case?: Resolver<Maybe<GqlResolversTypes['Case']>, ParentType, ContextType>;
  caseId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  children?: Resolver<Maybe<Array<GqlResolversTypes['Comment']>>, ParentType, ContextType>;
  content?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  parent?: Resolver<Maybe<GqlResolversTypes['Comment']>, ParentType, ContextType>;
  parentId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCommunicationChannelsConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CommunicationChannelsConfig'] = GqlResolversParentTypes['CommunicationChannelsConfig']> = {
  channels?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCommunicationChannelsConfigurationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CommunicationChannelsConfiguration'] = GqlResolversParentTypes['CommunicationChannelsConfiguration']> = {
  config?: Resolver<Maybe<GqlResolversTypes['CommunicationChannelsConfig']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlCommunicationPreferencesResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CommunicationPreferences'] = GqlResolversParentTypes['CommunicationPreferences']> = {
  email?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  sms?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlComplexFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ComplexField'] = GqlResolversParentTypes['ComplexField']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  subFields?: Resolver<Array<GqlResolversTypes['ApplicationField']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  useLabelPrefix?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlCountryCodeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['CountryCode'], any> {
  name: 'CountryCode';
}

export interface GqlCuidScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Cuid'], any> {
  name: 'Cuid';
}

export interface GqlCurrencyScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Currency'], any> {
  name: 'Currency';
}

export type GqlCursorPageInfoResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['CursorPageInfo'] = GqlResolversParentTypes['CursorPageInfo']> = {
  count?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  endCursor?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  hasNextPage?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  hasPreviousPage?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  startCursor?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlDidScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['DID'], any> {
  name: 'DID';
}

export type GqlDataLookupConfigurationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DataLookupConfiguration'] = GqlResolversParentTypes['DataLookupConfiguration']> = {
  fields?: Resolver<Array<GqlResolversTypes['DataLookupField']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDataLookupFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DataLookupField'] = GqlResolversParentTypes['DataLookupField']> = {
  details?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  metadata?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  sample?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  weight?: Resolver<Maybe<GqlResolversTypes['Float']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlDateScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Date'], any> {
  name: 'Date';
}

export type GqlDateFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DateField'] = GqlResolversParentTypes['DateField']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  props?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['DateFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDateFieldValidationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DateFieldValidation'] = GqlResolversParentTypes['DateFieldValidation']> = {
  allowedRange?: Resolver<Maybe<GqlResolversTypes['DateFieldRange']>, ParentType, ContextType>;
  condition?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  maximumDate?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  minimumDate?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  required?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  rules?: Resolver<Maybe<Array<GqlResolversTypes['ApplicationFieldValidationRule']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlDateTimeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['DateTime'], any> {
  name: 'DateTime';
}

export interface GqlDateTimeIsoScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['DateTimeISO'], any> {
  name: 'DateTimeISO';
}

export interface GqlDeweyDecimalScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['DeweyDecimal'], any> {
  name: 'DeweyDecimal';
}

export type GqlDoctopusTagResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DoctopusTag'] = GqlResolversParentTypes['DoctopusTag']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  visibility?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Document'] = GqlResolversParentTypes['Document']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  documentFields?: Resolver<Array<GqlResolversTypes['DocumentField']>, ParentType, ContextType>;
  documentKey?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  documentTags?: Resolver<Array<GqlResolversTypes['DocumentTag']>, ParentType, ContextType>;
  filename?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  mimetype?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  pinned?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  previewUrl?: Resolver<Maybe<GqlResolversTypes['URL']>, ParentType, ContextType>;
  summary?: Resolver<Maybe<GqlResolversTypes['DocumentSummary']>, ParentType, ContextType>;
  uploader?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  uploaderId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentConsistencyResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DocumentConsistency'] = GqlResolversParentTypes['DocumentConsistency']> = {
  bestMatchedName?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  documentId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  similarity?: Resolver<GqlResolversTypes['Similarity'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DocumentField'] = GqlResolversParentTypes['DocumentField']> = {
  confidence?: Resolver<Maybe<GqlResolversTypes['Float']>, ParentType, ContextType>;
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  docId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  fieldKey?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  fieldValue?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  normalizedValue?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  startIndex?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  stopIndex?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  valid?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentFieldCopyResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DocumentFieldCopy'] = GqlResolversParentTypes['DocumentFieldCopy']> = {
  description?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DocumentMutations'] = GqlResolversParentTypes['DocumentMutations']> = {
  pinDocument?: Resolver<GqlResolversTypes['DocumentsMutationResponse'], ParentType, ContextType, RequireFields<GqlDocumentMutationsPinDocumentArgs, 'input'>>;
  submitFeedback?: Resolver<GqlResolversTypes['DocumentsMutationResponse'], ParentType, ContextType, RequireFields<GqlDocumentMutationsSubmitFeedbackArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentSummaryResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DocumentSummary'] = GqlResolversParentTypes['DocumentSummary']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  modelVersion?: Resolver<GqlResolversTypes['ModelVersion'], ParentType, ContextType>;
  ocr?: Resolver<Maybe<GqlResolversTypes['OCRResult']>, ParentType, ContextType>;
  prediction?: Resolver<Maybe<GqlResolversTypes['Prediction']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentTagResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DocumentTag'] = GqlResolversParentTypes['DocumentTag']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  documentId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  tag?: Resolver<GqlResolversTypes['DoctopusTag'], ParentType, ContextType>;
  tagId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDocumentsMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DocumentsMutationResponse'] = GqlResolversParentTypes['DocumentsMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Document']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlDropdownFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['DropdownField'] = GqlResolversParentTypes['DropdownField']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  options?: Resolver<Array<GqlResolversTypes['OptionLabelValue']>, ParentType, ContextType>;
  props?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlDurationScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Duration'], any> {
  name: 'Duration';
}

export type GqlEligibilityConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['EligibilityConfig'] = GqlResolversParentTypes['EligibilityConfig']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  description?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  message?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  questions?: Resolver<Array<GqlResolversTypes['EligibilityQuestion']>, ParentType, ContextType>;
  title?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlEligibilityQuestionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['EligibilityQuestion'] = GqlResolversParentTypes['EligibilityQuestion']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  description?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  props?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['EligibilityQuestionType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlEmailAddressScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['EmailAddress'], any> {
  name: 'EmailAddress';
}

export type GqlEmailTemplateContentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['EmailTemplateContent'] = GqlResolversParentTypes['EmailTemplateContent']> = {
  button?: Resolver<Maybe<GqlResolversTypes['NotificationAction']>, ParentType, ContextType>;
  buttons?: Resolver<Maybe<Array<GqlResolversTypes['NotificationAction']>>, ParentType, ContextType>;
  postText?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  preText?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  subject?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlEnrollmentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Enrollment'] = GqlResolversParentTypes['Enrollment']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  endDate?: Resolver<Maybe<GqlResolversTypes['Date']>, ParentType, ContextType>;
  enrollmentOutcomes?: Resolver<Array<GqlResolversTypes['EnrollmentOutcome']>, ParentType, ContextType>;
  enrollmentServices?: Resolver<Array<GqlResolversTypes['EnrollmentService']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  program?: Resolver<GqlResolversTypes['Program'], ParentType, ContextType>;
  startDate?: Resolver<Maybe<GqlResolversTypes['Date']>, ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlEnrollmentMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['EnrollmentMutationResponse'] = GqlResolversParentTypes['EnrollmentMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Enrollment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlEnrollmentMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['EnrollmentMutations'] = GqlResolversParentTypes['EnrollmentMutations']> = {
  removeOutcome?: Resolver<GqlResolversTypes['EnrollmentMutationResponse'], ParentType, ContextType, RequireFields<GqlEnrollmentMutationsRemoveOutcomeArgs, 'input'>>;
  removeService?: Resolver<GqlResolversTypes['EnrollmentMutationResponse'], ParentType, ContextType, RequireFields<GqlEnrollmentMutationsRemoveServiceArgs, 'input'>>;
  upsert?: Resolver<GqlResolversTypes['EnrollmentMutationResponse'], ParentType, ContextType, RequireFields<GqlEnrollmentMutationsUpsertArgs, 'input'>>;
  upsertOutcome?: Resolver<GqlResolversTypes['EnrollmentMutationResponse'], ParentType, ContextType, RequireFields<GqlEnrollmentMutationsUpsertOutcomeArgs, 'input'>>;
  upsertService?: Resolver<GqlResolversTypes['EnrollmentMutationResponse'], ParentType, ContextType, RequireFields<GqlEnrollmentMutationsUpsertServiceArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlEnrollmentOutcomeResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['EnrollmentOutcome'] = GqlResolversParentTypes['EnrollmentOutcome']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  outcomeDate?: Resolver<GqlResolversTypes['Date'], ParentType, ContextType>;
  outcomes?: Resolver<Array<GqlResolversTypes['Outcome']>, ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlEnrollmentServiceResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['EnrollmentService'] = GqlResolversParentTypes['EnrollmentService']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  serviceDate?: Resolver<GqlResolversTypes['Date'], ParentType, ContextType>;
  services?: Resolver<Array<GqlResolversTypes['Service']>, ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFeatureResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Feature'] = GqlResolversParentTypes['Feature']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFeatureSettingResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FeatureSetting'] = GqlResolversParentTypes['FeatureSetting']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  enabled?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  feature?: Resolver<GqlResolversTypes['Feature'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFeedbackResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Feedback'] = GqlResolversParentTypes['Feedback']> = {
  accurate?: Resolver<GqlResolversTypes['YesNoUnsure'], ParentType, ContextType>;
  admin?: Resolver<GqlResolversTypes['Admin'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  preferredLabel?: Resolver<Maybe<GqlResolversTypes['ActiveLabel']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFieldResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FieldResponse'] = GqlResolversParentTypes['FieldResponse']> = {
  answer?: Resolver<Maybe<GqlResolversTypes['ApplicationAnswer']>, ParentType, ContextType>;
  field?: Resolver<GqlResolversTypes['ApplicationField'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFormDefinitionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FormDefinition'] = GqlResolversParentTypes['FormDefinition']> = {
  applicantTypeId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  formSchema?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  previousVersion?: Resolver<Maybe<GqlResolversTypes['FormDefinition']>, ParentType, ContextType>;
  previousVersionId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  programId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFulfillmentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Fulfillment'] = GqlResolversParentTypes['Fulfillment']> = {
  approvedAmount?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  case?: Resolver<GqlResolversTypes['Case'], ParentType, ContextType>;
  displayId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  fulfillmentMeta?: Resolver<Maybe<GqlResolversTypes['FulfillmentMeta']>, ParentType, ContextType>;
  fund?: Resolver<GqlResolversTypes['Fund'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  paymentPattern?: Resolver<Maybe<GqlResolversTypes['PaymentPattern']>, ParentType, ContextType>;
  payments?: Resolver<Maybe<Array<GqlResolversTypes['Payment']>>, ParentType, ContextType>;
  schedule?: Resolver<Maybe<Array<GqlResolversTypes['DateTime']>>, ParentType, ContextType>;
  scheduleType?: Resolver<GqlResolversTypes['ScheduleType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFulfillmentMetaResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FulfillmentMeta'] = GqlResolversParentTypes['FulfillmentMeta']> = {
  accountNumber?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  benefit?: Resolver<Maybe<GqlResolversTypes['BenefitType']>, ParentType, ContextType>;
  billingCode?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  checkNumber?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  endDate?: Resolver<Maybe<GqlResolversTypes['Date']>, ParentType, ContextType>;
  expenseType?: Resolver<Maybe<GqlResolversTypes['ExpenseType']>, ParentType, ContextType>;
  fulfillment?: Resolver<GqlResolversTypes['Fulfillment'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  months?: Resolver<Array<GqlResolversTypes['Month']>, ParentType, ContextType>;
  serviceDate?: Resolver<Maybe<GqlResolversTypes['Date']>, ParentType, ContextType>;
  startDate?: Resolver<Maybe<GqlResolversTypes['Date']>, ParentType, ContextType>;
  type?: Resolver<Maybe<GqlResolversTypes['SubBenefitType']>, ParentType, ContextType>;
  utilityType?: Resolver<Maybe<GqlResolversTypes['UtilityType']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFulfillmentMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FulfillmentMutations'] = GqlResolversParentTypes['FulfillmentMutations']> = {
  approveAmount?: Resolver<GqlResolversTypes['FulfillmentResponse'], ParentType, ContextType, Partial<GqlFulfillmentMutationsApproveAmountArgs>>;
  bulkIssueFunds?: Resolver<GqlResolversTypes['BulkFulfillmentResponse'], ParentType, ContextType, Partial<GqlFulfillmentMutationsBulkIssueFundsArgs>>;
  claimCheck?: Resolver<GqlResolversTypes['ClaimFulfillmentResponse'], ParentType, ContextType, RequireFields<GqlFulfillmentMutationsClaimCheckArgs, 'input'>>;
  claimDirectDeposit?: Resolver<GqlResolversTypes['ClaimFulfillmentResponse'], ParentType, ContextType, RequireFields<GqlFulfillmentMutationsClaimDirectDepositArgs, 'input'>>;
  claimPhysicalCard?: Resolver<GqlResolversTypes['ClaimFulfillmentResponse'], ParentType, ContextType, RequireFields<GqlFulfillmentMutationsClaimPhysicalCardArgs, 'input'>>;
  claimVirtualCard?: Resolver<GqlResolversTypes['ClaimFulfillmentResponse'], ParentType, ContextType, RequireFields<GqlFulfillmentMutationsClaimVirtualCardArgs, 'input'>>;
  claimZelle?: Resolver<GqlResolversTypes['ClaimFulfillmentResponse'], ParentType, ContextType, RequireFields<GqlFulfillmentMutationsClaimZelleArgs, 'input'>>;
  issueFunds?: Resolver<GqlResolversTypes['ClaimFulfillmentResponse'], ParentType, ContextType, RequireFields<GqlFulfillmentMutationsIssueFundsArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFulfillmentPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FulfillmentPage'] = GqlResolversParentTypes['FulfillmentPage']> = {
  fulfillments?: Resolver<Array<GqlResolversTypes['Fulfillment']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['VerifiedPageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFulfillmentResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FulfillmentResponse'] = GqlResolversParentTypes['FulfillmentResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Fulfillment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFundResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Fund'] = GqlResolversParentTypes['Fund']> = {
  awardAmountMax?: Resolver<Maybe<GqlResolversTypes['BigInt']>, ParentType, ContextType>;
  config?: Resolver<Maybe<GqlResolversTypes['FundConfig']>, ParentType, ContextType>;
  defaultPaymentFieldKey?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  programs?: Resolver<Array<GqlResolversTypes['Program']>, ParentType, ContextType>;
  startingBalance?: Resolver<GqlResolversTypes['BigInt'], ParentType, ContextType>;
  stats?: Resolver<Maybe<GqlResolversTypes['FundStats']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFundConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FundConfig'] = GqlResolversParentTypes['FundConfig']> = {
  accountNumber?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  checkCourierCode?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  checkFormatCode?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  includeCheckMemo?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  physicalDistributorId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  programId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  provider?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  skipACHTransfer?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  usioDesignId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  usioKey?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  virtualDistributorId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFundMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FundMutationResponse'] = GqlResolversParentTypes['FundMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Fund']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFundMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FundMutations'] = GqlResolversParentTypes['FundMutations']> = {
  create?: Resolver<GqlResolversTypes['FundMutationResponse'], ParentType, ContextType, RequireFields<GqlFundMutationsCreateArgs, 'input'>>;
  remove?: Resolver<GqlResolversTypes['FundMutationResponse'], ParentType, ContextType, RequireFields<GqlFundMutationsRemoveArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['FundMutationResponse'], ParentType, ContextType, RequireFields<GqlFundMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFundPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FundPage'] = GqlResolversParentTypes['FundPage']> = {
  nodes?: Resolver<Array<GqlResolversTypes['Fund']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlFundStatsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['FundStats'] = GqlResolversParentTypes['FundStats']> = {
  awardedBalance?: Resolver<GqlResolversTypes['BigInt'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  obligatedBalance?: Resolver<GqlResolversTypes['BigInt'], ParentType, ContextType>;
  remainingBalance?: Resolver<GqlResolversTypes['BigInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlGuidScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['GUID'], any> {
  name: 'GUID';
}

export type GqlGeoPointResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['GeoPoint'] = GqlResolversParentTypes['GeoPoint']> = {
  lat?: Resolver<GqlResolversTypes['Float'], ParentType, ContextType>;
  lon?: Resolver<GqlResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlHslScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['HSL'], any> {
  name: 'HSL';
}

export interface GqlHslaScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['HSLA'], any> {
  name: 'HSLA';
}

export type GqlHasAccessResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['HasAccessResponse'] = GqlResolversParentTypes['HasAccessResponse']> = {
  canAccess?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  hasAccess?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  message?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  token?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlHexColorCodeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['HexColorCode'], any> {
  name: 'HexColorCode';
}

export interface GqlHexadecimalScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Hexadecimal'], any> {
  name: 'Hexadecimal';
}

export type GqlHouseholdLimitResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['HouseholdLimit'] = GqlResolversParentTypes['HouseholdLimit']> = {
  householdSize?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  incomeLimit?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlIbanScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['IBAN'], any> {
  name: 'IBAN';
}

export interface GqlIpScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['IP'], any> {
  name: 'IP';
}

export interface GqlIpcPatentScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['IPCPatent'], any> {
  name: 'IPCPatent';
}

export interface GqlIPv4ScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['IPv4'], any> {
  name: 'IPv4';
}

export interface GqlIPv6ScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['IPv6'], any> {
  name: 'IPv6';
}

export interface GqlIsbnScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['ISBN'], any> {
  name: 'ISBN';
}

export interface GqlIso8601DurationScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['ISO8601Duration'], any> {
  name: 'ISO8601Duration';
}

export type GqlIdentityConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IdentityConfig'] = GqlResolversParentTypes['IdentityConfig']> = {
  advocate?: Resolver<Maybe<GqlResolversTypes['AdvocateIdentityConfig']>, ParentType, ContextType>;
  applicant?: Resolver<Maybe<GqlResolversTypes['ApplicantIdentityConfig']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIdentityMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IdentityMutations'] = GqlResolversParentTypes['IdentityMutations']> = {
  beginSession?: Resolver<GqlResolversTypes['IdentityResponse'], ParentType, ContextType, RequireFields<GqlIdentityMutationsBeginSessionArgs, 'input'>>;
  endSession?: Resolver<GqlResolversTypes['IdentityResponse'], ParentType, ContextType>;
  sendMagicLink?: Resolver<GqlResolversTypes['IdentityResponse'], ParentType, ContextType, RequireFields<GqlIdentityMutationsSendMagicLinkArgs, 'input'>>;
  sendMagicLinkV2?: Resolver<GqlResolversTypes['IdentityResponse'], ParentType, ContextType, RequireFields<GqlIdentityMutationsSendMagicLinkV2Args, 'input'>>;
  sendVerificationEmail?: Resolver<GqlResolversTypes['IdentityResponse'], ParentType, ContextType, RequireFields<GqlIdentityMutationsSendVerificationEmailArgs, 'input'>>;
  verifyEmail?: Resolver<GqlResolversTypes['IdentityResponse'], ParentType, ContextType, RequireFields<GqlIdentityMutationsVerifyEmailArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIdentityResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IdentityResponse'] = GqlResolversParentTypes['IdentityResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIdentityUserResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IdentityUser'] = GqlResolversParentTypes['IdentityUser']> = {
  email?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  phone?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  verifiedEmail?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIncidentMessageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IncidentMessage'] = GqlResolversParentTypes['IncidentMessage']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  isGlobal?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  message?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  partners?: Resolver<Maybe<Array<GqlResolversTypes['Partner']>>, ParentType, ContextType>;
  severity?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIncidentMessagePageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IncidentMessagePage'] = GqlResolversParentTypes['IncidentMessagePage']> = {
  incidentMessages?: Resolver<Array<GqlResolversTypes['IncidentMessage']>, ParentType, ContextType>;
  nodes?: Resolver<Array<GqlResolversTypes['IncidentMessage']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIncidentMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IncidentMutationResponse'] = GqlResolversParentTypes['IncidentMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['PartnerIncident']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIncomeLimitResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IncomeLimit'] = GqlResolversParentTypes['IncomeLimit']> = {
  fipsCode?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  limits?: Resolver<GqlResolversTypes['Limits'], ParentType, ContextType>;
  year?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIncomeLimitAreaResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IncomeLimitArea'] = GqlResolversParentTypes['IncomeLimitArea']> = {
  county?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  fipsCode?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  incomeLimit?: Resolver<GqlResolversTypes['IncomeLimit'], ParentType, ContextType>;
  state?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  town?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIncomeLimitAreaPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IncomeLimitAreaPage'] = GqlResolversParentTypes['IncomeLimitAreaPage']> = {
  incomeLimitAreas?: Resolver<Array<GqlResolversTypes['IncomeLimitArea']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIntroPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IntroPage'] = GqlResolversParentTypes['IntroPage']> = {
  body?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  copy?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  layout?: Resolver<GqlResolversTypes['IntroPageLayoutType'], ParentType, ContextType>;
  steps?: Resolver<Maybe<Array<GqlResolversTypes['IntroPageContentBlock']>>, ParentType, ContextType>;
  title?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlIntroPageContentBlockResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['IntroPageContentBlock'] = GqlResolversParentTypes['IntroPageContentBlock']> = {
  description?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  icon?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  title?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlInvitationCodeResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['InvitationCode'] = GqlResolversParentTypes['InvitationCode']> = {
  caseParticipant?: Resolver<GqlResolversTypes['CaseParticipant'], ParentType, ContextType>;
  code?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  usedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlJsonScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['JSON'], any> {
  name: 'JSON';
}

export interface GqlJsonObjectScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['JSONObject'], any> {
  name: 'JSONObject';
}

export interface GqlJwtScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['JWT'], any> {
  name: 'JWT';
}

export interface GqlLccSubclassScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['LCCSubclass'], any> {
  name: 'LCCSubclass';
}

export interface GqlLatitudeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Latitude'], any> {
  name: 'Latitude';
}

export type GqlLimitsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Limits'] = GqlResolversParentTypes['Limits']> = {
  extremelyLow?: Resolver<Array<GqlResolversTypes['HouseholdLimit']>, ParentType, ContextType>;
  low?: Resolver<Array<GqlResolversTypes['HouseholdLimit']>, ParentType, ContextType>;
  veryLow?: Resolver<Array<GqlResolversTypes['HouseholdLimit']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlLinkAttemptResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['LinkAttempt'] = GqlResolversParentTypes['LinkAttempt']> = {
  application?: Resolver<GqlResolversTypes['Application'], ParentType, ContextType>;
  caseParticipant?: Resolver<GqlResolversTypes['CaseParticipant'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  details?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  originalCase?: Resolver<GqlResolversTypes['Case'], ParentType, ContextType>;
  result?: Resolver<GqlResolversTypes['LinkAttemptResult'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlLocalDateScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['LocalDate'], any> {
  name: 'LocalDate';
}

export interface GqlLocalDateTimeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['LocalDateTime'], any> {
  name: 'LocalDateTime';
}

export interface GqlLocalEndTimeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['LocalEndTime'], any> {
  name: 'LocalEndTime';
}

export interface GqlLocalTimeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['LocalTime'], any> {
  name: 'LocalTime';
}

export interface GqlLocaleScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Locale'], any> {
  name: 'Locale';
}

export type GqlLoginDetailsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['LoginDetails'] = GqlResolversParentTypes['LoginDetails']> = {
  isVpn?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  location?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  timestamp?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlLongScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Long'], any> {
  name: 'Long';
}

export interface GqlLongitudeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Longitude'], any> {
  name: 'Longitude';
}

export interface GqlMacScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['MAC'], any> {
  name: 'MAC';
}

export type GqlModelVersionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ModelVersion'] = GqlResolversParentTypes['ModelVersion']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlMonthResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Month'] = GqlResolversParentTypes['Month']> = {
  month?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  year?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlMutationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Mutation'] = GqlResolversParentTypes['Mutation']> = {
  accessRequest?: Resolver<Maybe<GqlResolversTypes['AccessRequestMutations']>, ParentType, ContextType>;
  addNoteToApplicationAnswer?: Resolver<GqlResolversTypes['AddNoteToApplicationAnswerPayload'], ParentType, ContextType, RequireFields<GqlMutationAddNoteToApplicationAnswerArgs, 'input'>>;
  addReviewToApplicationAnswer?: Resolver<GqlResolversTypes['AddNoteToApplicationAnswerPayload'], ParentType, ContextType, RequireFields<GqlMutationAddReviewToApplicationAnswerArgs, 'input'>>;
  admin?: Resolver<Maybe<GqlResolversTypes['AdminMutations']>, ParentType, ContextType>;
  application?: Resolver<Maybe<GqlResolversTypes['ApplicationMutations']>, ParentType, ContextType>;
  case?: Resolver<Maybe<GqlResolversTypes['CaseMutations']>, ParentType, ContextType>;
  createFormDefinition?: Resolver<GqlResolversTypes['FormDefinition'], ParentType, ContextType, RequireFields<GqlMutationCreateFormDefinitionArgs, 'applicantTypeId' | 'formSchema' | 'programId'>>;
  document?: Resolver<Maybe<GqlResolversTypes['DocumentMutations']>, ParentType, ContextType>;
  enrollment?: Resolver<Maybe<GqlResolversTypes['EnrollmentMutations']>, ParentType, ContextType>;
  fulfillment?: Resolver<Maybe<GqlResolversTypes['FulfillmentMutations']>, ParentType, ContextType>;
  fund?: Resolver<Maybe<GqlResolversTypes['FundMutations']>, ParentType, ContextType>;
  identity?: Resolver<Maybe<GqlResolversTypes['IdentityMutations']>, ParentType, ContextType>;
  note?: Resolver<Maybe<GqlResolversTypes['NoteMutations']>, ParentType, ContextType>;
  partnerIncident?: Resolver<GqlResolversTypes['PartnerIncidentMutations'], ParentType, ContextType>;
  payment?: Resolver<Maybe<GqlResolversTypes['PaymentMutations']>, ParentType, ContextType>;
  preset?: Resolver<Maybe<GqlResolversTypes['PresetMutations']>, ParentType, ContextType>;
  program?: Resolver<Maybe<GqlResolversTypes['ProgramMutations']>, ParentType, ContextType>;
  programReferral?: Resolver<Maybe<GqlResolversTypes['ProgramReferralMutations']>, ParentType, ContextType>;
  saveRuleset?: Resolver<GqlResolversTypes['Ruleset'], ParentType, ContextType, RequireFields<GqlMutationSaveRulesetArgs, 'model' | 'programId'>>;
  savedView?: Resolver<Maybe<GqlResolversTypes['SavedViewMutations']>, ParentType, ContextType>;
  tag?: Resolver<Maybe<GqlResolversTypes['TagMutations']>, ParentType, ContextType>;
  tagAutomations?: Resolver<Maybe<GqlResolversTypes['TagAutomationsMutations']>, ParentType, ContextType>;
  updateFormDefinition?: Resolver<GqlResolversTypes['FormDefinition'], ParentType, ContextType, RequireFields<GqlMutationUpdateFormDefinitionArgs, 'formSchema' | 'id'>>;
  user?: Resolver<Maybe<GqlResolversTypes['UserMutations']>, ParentType, ContextType>;
  vendor?: Resolver<Maybe<GqlResolversTypes['VendorMutations']>, ParentType, ContextType>;
};

export interface GqlNegativeFloatScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['NegativeFloat'], any> {
  name: 'NegativeFloat';
}

export interface GqlNegativeIntScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['NegativeInt'], any> {
  name: 'NegativeInt';
}

export interface GqlNonEmptyStringScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['NonEmptyString'], any> {
  name: 'NonEmptyString';
}

export interface GqlNonNegativeFloatScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['NonNegativeFloat'], any> {
  name: 'NonNegativeFloat';
}

export interface GqlNonNegativeIntScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['NonNegativeInt'], any> {
  name: 'NonNegativeInt';
}

export interface GqlNonPositiveFloatScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['NonPositiveFloat'], any> {
  name: 'NonPositiveFloat';
}

export interface GqlNonPositiveIntScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['NonPositiveInt'], any> {
  name: 'NonPositiveInt';
}

export type GqlNoteResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Note'] = GqlResolversParentTypes['Note']> = {
  author?: Resolver<GqlResolversTypes['Admin'], ParentType, ContextType>;
  case?: Resolver<GqlResolversTypes['Case'], ParentType, ContextType>;
  content?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlNoteMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['NoteMutationResponse'] = GqlResolversParentTypes['NoteMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Note']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlNoteMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['NoteMutations'] = GqlResolversParentTypes['NoteMutations']> = {
  add?: Resolver<GqlResolversTypes['NoteMutationResponse'], ParentType, ContextType, RequireFields<GqlNoteMutationsAddArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['NoteMutationResponse'], ParentType, ContextType, RequireFields<GqlNoteMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlNotificationActionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['NotificationAction'] = GqlResolversParentTypes['NotificationAction']> = {
  domain?: Resolver<Maybe<GqlResolversTypes['Domain']>, ParentType, ContextType>;
  showFallbackText?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  text?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  url?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  variant?: Resolver<Maybe<GqlResolversTypes['ButtonVariant']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlNotificationConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['NotificationConfig'] = GqlResolversParentTypes['NotificationConfig']> = {
  channelConfigurations?: Resolver<Array<GqlResolversTypes['ChannelConfiguration']>, ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlNotificationTemplateResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['NotificationTemplate'] = GqlResolversParentTypes['NotificationTemplate']> = {
  channel?: Resolver<GqlResolversTypes['NotificationChannel'], ParentType, ContextType>;
  content?: Resolver<GqlResolversTypes['TemplateContent'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  partnerId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  programId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlOcrResultResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['OCRResult'] = GqlResolversParentTypes['OCRResult']> = {
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  modelVersion?: Resolver<GqlResolversTypes['ModelVersion'], ParentType, ContextType>;
  raw?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlObjectIdScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['ObjectID'], any> {
  name: 'ObjectID';
}

export type GqlOptionLabelValueResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['OptionLabelValue'] = GqlResolversParentTypes['OptionLabelValue']> = {
  children?: Resolver<Maybe<Array<GqlResolversTypes['OptionLabelValue']>>, ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  disabled?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  label?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  value?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlOutcomeResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Outcome'] = GqlResolversParentTypes['Outcome']> = {
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPageInfoResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PageInfo'] = GqlResolversParentTypes['PageInfo']> = {
  count?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  nextCursor?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPartnerResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Partner'] = GqlResolversParentTypes['Partner']> = {
  analyticsResources?: Resolver<Array<GqlResolversTypes['AnalyticsResource']>, ParentType, ContextType>;
  applicantProfileConfigs?: Resolver<Array<GqlResolversTypes['ApplicantProfileConfiguration']>, ParentType, ContextType>;
  applicantTypes?: Resolver<Array<GqlResolversTypes['ApplicantType']>, ParentType, ContextType>;
  communicationChannels?: Resolver<Maybe<GqlResolversTypes['CommunicationChannelsConfiguration']>, ParentType, ContextType>;
  config?: Resolver<Maybe<GqlResolversTypes['PartnerConfig']>, ParentType, ContextType>;
  eligibility?: Resolver<Maybe<GqlResolversTypes['EligibilityConfig']>, ParentType, ContextType>;
  email?: Resolver<GqlResolversTypes['EmailAddress'], ParentType, ContextType>;
  externalId?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  features?: Resolver<Array<GqlResolversTypes['FeatureSetting']>, ParentType, ContextType>;
  funds?: Resolver<Array<GqlResolversTypes['Fund']>, ParentType, ContextType>;
  gleanInviteToken?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  mailingAddress?: Resolver<Maybe<GqlResolversTypes['Address']>, ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  parent?: Resolver<Maybe<GqlResolversTypes['Partner']>, ParentType, ContextType>;
  phone?: Resolver<Maybe<GqlResolversTypes['PhoneNumber']>, ParentType, ContextType>;
  programs?: Resolver<Array<GqlResolversTypes['Program']>, ParentType, ContextType>;
  roles?: Resolver<Array<GqlResolversTypes['PortalRole']>, ParentType, ContextType>;
  savedViews?: Resolver<Maybe<Array<GqlResolversTypes['SavedView']>>, ParentType, ContextType>;
  tagAutomations?: Resolver<Array<GqlResolversTypes['TagAutomation']>, ParentType, ContextType>;
  tags?: Resolver<Array<GqlResolversTypes['Tag']>, ParentType, ContextType>;
  whitelabeling?: Resolver<Maybe<GqlResolversTypes['PartnerWhitelabeling']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPartnerConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PartnerConfig'] = GqlResolversParentTypes['PartnerConfig']> = {
  applicantTypeRoles?: Resolver<Maybe<GqlResolversTypes['ApplicantRolesConfig']>, ParentType, ContextType>;
  identity?: Resolver<Maybe<GqlResolversTypes['IdentityConfig']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPartnerIncidentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PartnerIncident'] = GqlResolversParentTypes['PartnerIncident']> = {
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  incidentId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  partnerId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPartnerIncidentMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PartnerIncidentMutations'] = GqlResolversParentTypes['PartnerIncidentMutations']> = {
  create?: Resolver<GqlResolversTypes['IncidentMutationResponse'], ParentType, ContextType, RequireFields<GqlPartnerIncidentMutationsCreateArgs, 'input'>>;
  remove?: Resolver<GqlResolversTypes['IncidentMutationResponse'], ParentType, ContextType, RequireFields<GqlPartnerIncidentMutationsRemoveArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPartnerPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PartnerPage'] = GqlResolversParentTypes['PartnerPage']> = {
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  partners?: Resolver<Array<GqlResolversTypes['Partner']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPartnerWhitelabelingResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PartnerWhitelabeling'] = GqlResolversParentTypes['PartnerWhitelabeling']> = {
  brandColor?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  favicon?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  logo?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  platformName?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlPasswordScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Password'], any> {
  name: 'Password';
}

export type GqlPayeeResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Payee'] = GqlResolversParentTypes['Payee']> = {
  __resolveType: TypeResolveFn<'User' | 'Vendor', ParentType, ContextType>;
};

export type GqlPaymentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Payment'] = GqlResolversParentTypes['Payment']> = {
  amount?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  completedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  displayId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  fulfillment?: Resolver<GqlResolversTypes['Fulfillment'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  initiatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  mailingAddress?: Resolver<Maybe<GqlResolversTypes['PaymentMailingAddress']>, ParentType, ContextType>;
  mailingAddressType?: Resolver<Maybe<GqlResolversTypes['MailingAddressType']>, ParentType, ContextType>;
  method?: Resolver<Maybe<GqlResolversTypes['PaymentMethod']>, ParentType, ContextType>;
  note?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  payee?: Resolver<Maybe<GqlResolversTypes['Payee']>, ParentType, ContextType>;
  payeeType?: Resolver<Maybe<GqlResolversTypes['PayeeType']>, ParentType, ContextType>;
  scheduledFor?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['PaymentStatus'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPaymentMailingAddressResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PaymentMailingAddress'] = GqlResolversParentTypes['PaymentMailingAddress']> = {
  addressLine1?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  addressLine2?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  careOf?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  city?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  state?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  zip?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPaymentMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PaymentMutationResponse'] = GqlResolversParentTypes['PaymentMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Payment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPaymentMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PaymentMutations'] = GqlResolversParentTypes['PaymentMutations']> = {
  create?: Resolver<GqlResolversTypes['PaymentMutationResponse'], ParentType, ContextType, RequireFields<GqlPaymentMutationsCreateArgs, 'input'>>;
  remove?: Resolver<GqlResolversTypes['PaymentMutationResponse'], ParentType, ContextType, RequireFields<GqlPaymentMutationsRemoveArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['PaymentMutationResponse'], ParentType, ContextType, RequireFields<GqlPaymentMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPaymentPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PaymentPage'] = GqlResolversParentTypes['PaymentPage']> = {
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  payments?: Resolver<Array<GqlResolversTypes['Payment']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPaymentPatternResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PaymentPattern'] = GqlResolversParentTypes['PaymentPattern']> = {
  amount?: Resolver<GqlResolversTypes['PositiveInt'], ParentType, ContextType>;
  count?: Resolver<GqlResolversTypes['PositiveInt'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  pattern?: Resolver<GqlResolversTypes['PaymentSchedule'], ParentType, ContextType>;
  start?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  totalAmount?: Resolver<GqlResolversTypes['PositiveInt'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPaymentsConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PaymentsConfig'] = GqlResolversParentTypes['PaymentsConfig']> = {
  recurring?: Resolver<Maybe<GqlResolversTypes['RecurringPaymentConfig']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlPhoneNumberScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['PhoneNumber'], any> {
  name: 'PhoneNumber';
}

export interface GqlPortScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Port'], any> {
  name: 'Port';
}

export interface GqlPositiveFloatScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['PositiveFloat'], any> {
  name: 'PositiveFloat';
}

export interface GqlPositiveIntScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['PositiveInt'], any> {
  name: 'PositiveInt';
}

export interface GqlPostalCodeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['PostalCode'], any> {
  name: 'PostalCode';
}

export type GqlPredictionResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Prediction'] = GqlResolversParentTypes['Prediction']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  feedback?: Resolver<Array<GqlResolversTypes['Feedback']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  label?: Resolver<GqlResolversTypes['ActiveLabel'], ParentType, ContextType>;
  probability?: Resolver<GqlResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlPresetMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['PresetMutations'] = GqlResolversParentTypes['PresetMutations']> = {
  exportTableToCSV?: Resolver<GqlResolversTypes['String'], ParentType, ContextType, RequireFields<GqlPresetMutationsExportTableToCsvArgs, 'input'>>;
  generatePresetGuestToken?: Resolver<GqlResolversTypes['String'], ParentType, ContextType, RequireFields<GqlPresetMutationsGeneratePresetGuestTokenArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlPrimitiveScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Primitive'], any> {
  name: 'Primitive';
}

export type GqlProfileAnswerResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProfileAnswer'] = GqlResolversParentTypes['ProfileAnswer']> = {
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  profile?: Resolver<GqlResolversTypes['ApplicantProfile'], ParentType, ContextType>;
  value?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProfileKeyResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProfileKey'] = GqlResolversParentTypes['ProfileKey']> = {
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  label?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Program'] = GqlResolversParentTypes['Program']> = {
  applicantTypes?: Resolver<Array<GqlResolversTypes['ProgramApplicantType']>, ParentType, ContextType>;
  applicationConfiguration?: Resolver<Maybe<GqlResolversTypes['ApplicationConfiguration']>, ParentType, ContextType>;
  applicationConfigurations?: Resolver<Array<GqlResolversTypes['ProgramApplicationConfiguration']>, ParentType, ContextType>;
  config?: Resolver<GqlResolversTypes['ProgramConfig'], ParentType, ContextType>;
  documentLabels?: Resolver<Array<GqlResolversTypes['ActiveLabel']>, ParentType, ContextType>;
  documents?: Resolver<Maybe<Array<GqlResolversTypes['ProgramDocument']>>, ParentType, ContextType>;
  features?: Resolver<Array<Maybe<GqlResolversTypes['FeatureSetting']>>, ParentType, ContextType>;
  funds?: Resolver<Array<GqlResolversTypes['Fund']>, ParentType, ContextType>;
  heroImage?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  notifications?: Resolver<Maybe<Array<GqlResolversTypes['NotificationConfig']>>, ParentType, ContextType>;
  outcomes?: Resolver<Array<GqlResolversTypes['Outcome']>, ParentType, ContextType>;
  services?: Resolver<Array<GqlResolversTypes['Service']>, ParentType, ContextType>;
  stats?: Resolver<GqlResolversTypes['ProgramStats'], ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['ProgramStatus'], ParentType, ContextType>;
  verificationConfigurations?: Resolver<Array<GqlResolversTypes['VerificationConfiguration']>, ParentType, ContextType>;
  workflow?: Resolver<GqlResolversTypes['Workflow'], ParentType, ContextType>;
  workflowSummary?: Resolver<GqlResolversTypes['WorkflowSummary'], ParentType, ContextType, Partial<GqlProgramWorkflowSummaryArgs>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramApplicantTypeResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramApplicantType'] = GqlResolversParentTypes['ProgramApplicantType']> = {
  applicantType?: Resolver<GqlResolversTypes['ApplicantType'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  nameOverride?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramApplicationConfigurationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramApplicationConfiguration'] = GqlResolversParentTypes['ProgramApplicationConfiguration']> = {
  applicantTypeId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  configuration?: Resolver<GqlResolversTypes['ApplicationConfiguration'], ParentType, ContextType>;
  configurationId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  programId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramConfig'] = GqlResolversParentTypes['ProgramConfig']> = {
  applicationConfiguration?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  applicationReviewFields?: Resolver<Maybe<Array<GqlResolversTypes['ApplicationConfigReviewFields']>>, ParentType, ContextType>;
  mailingAddressOverride?: Resolver<Maybe<GqlResolversTypes['Address']>, ParentType, ContextType>;
  maxFundingAmount?: Resolver<Maybe<GqlResolversTypes['NonNegativeInt']>, ParentType, ContextType>;
  nameMatchingEnabled?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  payments?: Resolver<Maybe<GqlResolversTypes['PaymentsConfig']>, ParentType, ContextType>;
  programContext?: Resolver<Maybe<GqlResolversTypes['ProgramContext']>, ParentType, ContextType>;
  reapplicationRules?: Resolver<Maybe<Array<GqlResolversTypes['ReapplicationRules']>>, ParentType, ContextType>;
  rulesEvaluationEnabled?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  sortOrder?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  verification?: Resolver<Maybe<GqlResolversTypes['VerificationConfig']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramContextResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramContext'] = GqlResolversParentTypes['ProgramContext']> = {
  description?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  link?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramDocumentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramDocument'] = GqlResolversParentTypes['ProgramDocument']> = {
  document?: Resolver<Maybe<GqlResolversTypes['Document']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  status?: Resolver<Maybe<GqlResolversTypes['ProgramDocumentStatus']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramFundStatsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramFundStats'] = GqlResolversParentTypes['ProgramFundStats']> = {
  awardedBalance?: Resolver<GqlResolversTypes['BigInt'], ParentType, ContextType>;
  fundId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  obligatedBalance?: Resolver<GqlResolversTypes['BigInt'], ParentType, ContextType>;
  paymentCount?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  programId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramMutationResponse'] = GqlResolversParentTypes['ProgramMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Program']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramMutations'] = GqlResolversParentTypes['ProgramMutations']> = {
  addProgramFund?: Resolver<GqlResolversTypes['ProgramMutationResponse'], ParentType, ContextType, Partial<GqlProgramMutationsAddProgramFundArgs>>;
  create?: Resolver<GqlResolversTypes['ProgramMutationResponse'], ParentType, ContextType, Partial<GqlProgramMutationsCreateArgs>>;
  removeProgramFund?: Resolver<GqlResolversTypes['ProgramMutationResponse'], ParentType, ContextType, Partial<GqlProgramMutationsRemoveProgramFundArgs>>;
  update?: Resolver<GqlResolversTypes['ProgramsMutationResponse'], ParentType, ContextType, RequireFields<GqlProgramMutationsUpdateArgs, 'input'>>;
  uploadVerificationFile?: Resolver<GqlResolversTypes['ProgramMutationResponse'], ParentType, ContextType, RequireFields<GqlProgramMutationsUploadVerificationFileArgs, 'input'>>;
  upsertLookupConfig?: Resolver<GqlResolversTypes['ProgramMutationResponse'], ParentType, ContextType, RequireFields<GqlProgramMutationsUpsertLookupConfigArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramPage'] = GqlResolversParentTypes['ProgramPage']> = {
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  programs?: Resolver<Array<GqlResolversTypes['Program']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramReferralResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramReferral'] = GqlResolversParentTypes['ProgramReferral']> = {
  admin?: Resolver<GqlResolversTypes['Admin'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  program?: Resolver<GqlResolversTypes['Program'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  user?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramReferralMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramReferralMutationResponse'] = GqlResolversParentTypes['ProgramReferralMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['ProgramReferral']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramReferralMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramReferralMutations'] = GqlResolversParentTypes['ProgramReferralMutations']> = {
  create?: Resolver<GqlResolversTypes['ProgramReferralMutationResponse'], ParentType, ContextType, RequireFields<GqlProgramReferralMutationsCreateArgs, 'input'>>;
  createBulkProgramReferral?: Resolver<GqlResolversTypes['BulkUserMutationResponse'], ParentType, ContextType, RequireFields<GqlProgramReferralMutationsCreateBulkProgramReferralArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramStatsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramStats'] = GqlResolversParentTypes['ProgramStats']> = {
  awardedBalance?: Resolver<GqlResolversTypes['BigInt'], ParentType, ContextType>;
  caseCounts?: Resolver<GqlResolversTypes['CaseCounts'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  programFundStats?: Resolver<Array<GqlResolversTypes['ProgramFundStats']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlProgramsMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ProgramsMutationResponse'] = GqlResolversParentTypes['ProgramsMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<Array<GqlResolversTypes['Program']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlQueryResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Query'] = GqlResolversParentTypes['Query']> = {
  accessRequests?: Resolver<GqlResolversTypes['AccessRequestPage'], ParentType, ContextType, Partial<GqlQueryAccessRequestsArgs>>;
  admins?: Resolver<GqlResolversTypes['AdminPage'], ParentType, ContextType, Partial<GqlQueryAdminsArgs>>;
  aggregateApplication?: Resolver<GqlResolversTypes['AggregateApplication'], ParentType, ContextType, Partial<GqlQueryAggregateApplicationArgs>>;
  aggregatePayment?: Resolver<GqlResolversTypes['AggregatePayment'], ParentType, ContextType, Partial<GqlQueryAggregatePaymentArgs>>;
  applicantsReferred?: Resolver<GqlResolversTypes['AggregateApplicantsReferred'], ParentType, ContextType>;
  applicationSubmission?: Resolver<Maybe<GqlResolversTypes['ApplicationSubmission']>, ParentType, ContextType, RequireFields<GqlQueryApplicationSubmissionArgs, 'id'>>;
  applications?: Resolver<GqlResolversTypes['ApplicationPage'], ParentType, ContextType, Partial<GqlQueryApplicationsArgs>>;
  case?: Resolver<GqlResolversTypes['Case'], ParentType, ContextType, RequireFields<GqlQueryCaseArgs, 'id'>>;
  cases?: Resolver<GqlResolversTypes['CasePage'], ParentType, ContextType, Partial<GqlQueryCasesArgs>>;
  changelogs?: Resolver<Array<GqlResolversTypes['Changelog']>, ParentType, ContextType>;
  currentUser?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  formDefinition?: Resolver<Maybe<GqlResolversTypes['FormDefinition']>, ParentType, ContextType, RequireFields<GqlQueryFormDefinitionArgs, 'id' | 'includeInactive'>>;
  formDefinitionByProgramAndApplicantType?: Resolver<Maybe<GqlResolversTypes['FormDefinition']>, ParentType, ContextType, RequireFields<GqlQueryFormDefinitionByProgramAndApplicantTypeArgs, 'applicantTypeId' | 'includeInactive' | 'programId'>>;
  formDefinitionsByProgram?: Resolver<Array<GqlResolversTypes['FormDefinition']>, ParentType, ContextType, RequireFields<GqlQueryFormDefinitionsByProgramArgs, 'includeInactive' | 'programId'>>;
  fulfillments?: Resolver<GqlResolversTypes['FulfillmentPage'], ParentType, ContextType, Partial<GqlQueryFulfillmentsArgs>>;
  funds?: Resolver<GqlResolversTypes['FundPage'], ParentType, ContextType, Partial<GqlQueryFundsArgs>>;
  hasAccess?: Resolver<GqlResolversTypes['HasAccessResponse'], ParentType, ContextType, RequireFields<GqlQueryHasAccessArgs, 'input'>>;
  incidentMessages?: Resolver<GqlResolversTypes['IncidentMessagePage'], ParentType, ContextType, Partial<GqlQueryIncidentMessagesArgs>>;
  incomeLimitAreas?: Resolver<GqlResolversTypes['IncomeLimitAreaPage'], ParentType, ContextType, Partial<GqlQueryIncomeLimitAreasArgs>>;
  partners?: Resolver<GqlResolversTypes['PartnerPage'], ParentType, ContextType, Partial<GqlQueryPartnersArgs>>;
  payments?: Resolver<GqlResolversTypes['PaymentPage'], ParentType, ContextType, RequireFields<GqlQueryPaymentsArgs, 'vendorId'>>;
  programs?: Resolver<GqlResolversTypes['ProgramPage'], ParentType, ContextType, Partial<GqlQueryProgramsArgs>>;
  ruleset?: Resolver<Maybe<GqlResolversTypes['Ruleset']>, ParentType, ContextType, RequireFields<GqlQueryRulesetArgs, 'programId'>>;
  rulesets?: Resolver<GqlResolversTypes['RulesetPage'], ParentType, ContextType, RequireFields<GqlQueryRulesetsArgs, 'programId'>>;
  search?: Resolver<GqlResolversTypes['SearchPage'], ParentType, ContextType, RequireFields<GqlQuerySearchArgs, 'input'>>;
  tagAutomations?: Resolver<Array<GqlResolversTypes['TagAutomation']>, ParentType, ContextType>;
  users?: Resolver<GqlResolversTypes['UserPage'], ParentType, ContextType, Partial<GqlQueryUsersArgs>>;
  vendorTypes?: Resolver<Array<GqlResolversTypes['VendorType']>, ParentType, ContextType>;
  vendors?: Resolver<GqlResolversTypes['VendorPage'], ParentType, ContextType, Partial<GqlQueryVendorsArgs>>;
};

export type GqlQuestionGroupResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['QuestionGroupResponse'] = GqlResolversParentTypes['QuestionGroupResponse']> = {
  key?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  questions?: Resolver<Array<GqlResolversTypes['QuestionResponse']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlQuestionResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['QuestionResponse'] = GqlResolversParentTypes['QuestionResponse']> = {
  copy?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  fields?: Resolver<Array<GqlResolversTypes['FieldResponse']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlRgbScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['RGB'], any> {
  name: 'RGB';
}

export interface GqlRgbaScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['RGBA'], any> {
  name: 'RGBA';
}

export type GqlRadioListFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['RadioListField'] = GqlResolversParentTypes['RadioListField']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  options?: Resolver<Array<GqlResolversTypes['OptionLabelValue']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlReapplicationRulesResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ReapplicationRules'] = GqlResolversParentTypes['ReapplicationRules']> = {
  type?: Resolver<GqlResolversTypes['ReapplicationValidationType'], ParentType, ContextType>;
  unit?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlRecurringPaymentConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['RecurringPaymentConfig'] = GqlResolversParentTypes['RecurringPaymentConfig']> = {
  amount?: Resolver<Maybe<GqlResolversTypes['PositiveInt']>, ParentType, ContextType>;
  count?: Resolver<Maybe<GqlResolversTypes['PositiveInt']>, ParentType, ContextType>;
  frequency?: Resolver<GqlResolversTypes['PaymentSchedule'], ParentType, ContextType>;
  pattern?: Resolver<Maybe<GqlResolversTypes['PaymentSchedule']>, ParentType, ContextType>;
  start?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  startDate?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  totalAmount?: Resolver<Maybe<GqlResolversTypes['PositiveInt']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlResolvedEntitiesResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ResolvedEntities'] = GqlResolversParentTypes['ResolvedEntities']> = {
  applicantNameConsistency?: Resolver<GqlResolversTypes['ApplicantNameConsistency'], ParentType, ContextType>;
  caseId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlResponseMetadataResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['ResponseMetadata'] = GqlResolversParentTypes['ResponseMetadata']> = {
  errors?: Resolver<Maybe<Array<Maybe<GqlResolversTypes['String']>>>, ParentType, ContextType>;
  id?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  message?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlRoleConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['RoleConfig'] = GqlResolversParentTypes['RoleConfig']> = {
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlRoutingNumberScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['RoutingNumber'], any> {
  name: 'RoutingNumber';
}

export type GqlRulesetResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Ruleset'] = GqlResolversParentTypes['Ruleset']> = {
  author?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  model?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  partnerId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  programId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  version?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlRulesetPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['RulesetPage'] = GqlResolversParentTypes['RulesetPage']> = {
  nodes?: Resolver<Array<GqlResolversTypes['Ruleset']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSamlConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SAMLConfig'] = GqlResolversParentTypes['SAMLConfig']> = {
  callbackURL?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  copy?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  method?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  providerId?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  ssoURL?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlSessnScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['SESSN'], any> {
  name: 'SESSN';
}

export interface GqlSafeIntScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['SafeInt'], any> {
  name: 'SafeInt';
}

export type GqlSavedViewResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SavedView'] = GqlResolversParentTypes['SavedView']> = {
  author?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  filters?: Resolver<GqlResolversTypes['JSON'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  isPublic?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  partner?: Resolver<GqlResolversTypes['Partner'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSavedViewMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SavedViewMutationResponse'] = GqlResolversParentTypes['SavedViewMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['SavedView']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSavedViewMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SavedViewMutations'] = GqlResolversParentTypes['SavedViewMutations']> = {
  create?: Resolver<GqlResolversTypes['SavedViewMutationResponse'], ParentType, ContextType, RequireFields<GqlSavedViewMutationsCreateArgs, 'input'>>;
  delete?: Resolver<GqlResolversTypes['SavedViewMutationResponse'], ParentType, ContextType, RequireFields<GqlSavedViewMutationsDeleteArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['SavedViewMutationResponse'], ParentType, ContextType, RequireFields<GqlSavedViewMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSearchPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SearchPage'] = GqlResolversParentTypes['SearchPage']> = {
  nodes?: Resolver<Array<GqlResolversTypes['CaseApplicationsDocument']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['CursorPageInfo'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSectionResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SectionResponse'] = GqlResolversParentTypes['SectionResponse']> = {
  key?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  questionGroups?: Resolver<Array<GqlResolversTypes['QuestionGroupResponse']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlSemVerScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['SemVer'], any> {
  name: 'SemVer';
}

export type GqlServiceResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Service'] = GqlResolversParentTypes['Service']> = {
  children?: Resolver<Array<GqlResolversTypes['Service']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  parent?: Resolver<Maybe<GqlResolversTypes['Service']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSmsTemplateContentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SmsTemplateContent'] = GqlResolversParentTypes['SmsTemplateContent']> = {
  link?: Resolver<Maybe<GqlResolversTypes['NotificationAction']>, ParentType, ContextType>;
  postText?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  preText?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlStatusOverrideResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['StatusOverride'] = GqlResolversParentTypes['StatusOverride']> = {
  action?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  label?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  message?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  referralMessage?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['ApplicationStatus'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSubmissionBulletResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SubmissionBullet'] = GqlResolversParentTypes['SubmissionBullet']> = {
  description?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  icon?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  title?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlSubmissionOverridesResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['SubmissionOverrides'] = GqlResolversParentTypes['SubmissionOverrides']> = {
  bullets?: Resolver<Array<GqlResolversTypes['SubmissionBullet']>, ParentType, ContextType>;
  title?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTagResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Tag'] = GqlResolversParentTypes['Tag']> = {
  count?: Resolver<Maybe<GqlResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  partner?: Resolver<GqlResolversTypes['Partner'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTagAutomationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TagAutomation'] = GqlResolversParentTypes['TagAutomation']> = {
  actionType?: Resolver<GqlResolversTypes['TagAutomationActionType'], ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  criteria?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  deactivatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  partner?: Resolver<Maybe<GqlResolversTypes['Partner']>, ParentType, ContextType>;
  partnerId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  program?: Resolver<Maybe<GqlResolversTypes['Program']>, ParentType, ContextType>;
  programId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  tags?: Resolver<Array<GqlResolversTypes['Tag']>, ParentType, ContextType>;
  triggerType?: Resolver<GqlResolversTypes['TagAutomationTriggerType'], ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTagAutomationMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TagAutomationMutationResponse'] = GqlResolversParentTypes['TagAutomationMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['TagAutomation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTagAutomationsMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TagAutomationsMutations'] = GqlResolversParentTypes['TagAutomationsMutations']> = {
  create?: Resolver<GqlResolversTypes['TagAutomationMutationResponse'], ParentType, ContextType, RequireFields<GqlTagAutomationsMutationsCreateArgs, 'input'>>;
  delete?: Resolver<GqlResolversTypes['TagAutomationMutationResponse'], ParentType, ContextType, RequireFields<GqlTagAutomationsMutationsDeleteArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['TagAutomationMutationResponse'], ParentType, ContextType, RequireFields<GqlTagAutomationsMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTagMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TagMutationResponse'] = GqlResolversParentTypes['TagMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<GqlResolversTypes['Tag'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTagMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TagMutations'] = GqlResolversParentTypes['TagMutations']> = {
  create?: Resolver<GqlResolversTypes['TagsMutationResponse'], ParentType, ContextType, RequireFields<GqlTagMutationsCreateArgs, 'input'>>;
  delete?: Resolver<GqlResolversTypes['TagsMutationResponse'], ParentType, ContextType, RequireFields<GqlTagMutationsDeleteArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['TagMutationResponse'], ParentType, ContextType, RequireFields<GqlTagMutationsUpdateArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTagsMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TagsMutationResponse'] = GqlResolversParentTypes['TagsMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<Array<GqlResolversTypes['Tag']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTaxFormResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TaxForm'] = GqlResolversParentTypes['TaxForm']> = {
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['TaxFormType'], ParentType, ContextType>;
  user?: Resolver<GqlResolversTypes['User'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTaxFormMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TaxFormMutationResponse'] = GqlResolversParentTypes['TaxFormMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  query?: Resolver<GqlResolversTypes['Query'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlTemplateContentResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TemplateContent'] = GqlResolversParentTypes['TemplateContent']> = {
  __resolveType: TypeResolveFn<'EmailTemplateContent' | 'SmsTemplateContent', ParentType, ContextType>;
};

export type GqlTextFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TextField'] = GqlResolversParentTypes['TextField']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  displayName?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  inputType?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  isSensitive?: Resolver<Maybe<GqlResolversTypes['Boolean']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  props?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  validation?: Resolver<Maybe<GqlResolversTypes['ApplicationFieldValidation']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlTimeScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Time'], any> {
  name: 'Time';
}

export interface GqlTimeZoneScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['TimeZone'], any> {
  name: 'TimeZone';
}

export interface GqlTimestampScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Timestamp'], any> {
  name: 'Timestamp';
}

export type GqlTypographyFieldResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['TypographyField'] = GqlResolversParentTypes['TypographyField']> = {
  copy?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  dynamicLogic?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  props?: Resolver<Maybe<GqlResolversTypes['JSON']>, ParentType, ContextType>;
  type?: Resolver<GqlResolversTypes['ApplicationFieldType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlUrlScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['URL'], any> {
  name: 'URL';
}

export interface GqlUsCurrencyScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['USCurrency'], any> {
  name: 'USCurrency';
}

export interface GqlUuidScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['UUID'], any> {
  name: 'UUID';
}

export interface GqlUnsignedFloatScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['UnsignedFloat'], any> {
  name: 'UnsignedFloat';
}

export interface GqlUnsignedIntScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['UnsignedInt'], any> {
  name: 'UnsignedInt';
}

export interface GqlUploadScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Upload'], any> {
  name: 'Upload';
}

export type GqlUserResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['User'] = GqlResolversParentTypes['User']> = {
  admin?: Resolver<Maybe<GqlResolversTypes['Admin']>, ParentType, ContextType>;
  aggregatePayments?: Resolver<GqlResolversTypes['AggregatePayment'], ParentType, ContextType>;
  applicantProfile?: Resolver<Maybe<GqlResolversTypes['ApplicantProfile']>, ParentType, ContextType>;
  applications?: Resolver<Array<GqlResolversTypes['Application']>, ParentType, ContextType>;
  bankAccount?: Resolver<Maybe<GqlResolversTypes['BankAccount']>, ParentType, ContextType>;
  communicationPreferences?: Resolver<Maybe<GqlResolversTypes['CommunicationPreferences']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  displayId?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  documents?: Resolver<Array<GqlResolversTypes['Document']>, ParentType, ContextType>;
  email?: Resolver<Maybe<GqlResolversTypes['EmailAddress']>, ParentType, ContextType>;
  enrollments?: Resolver<Array<GqlResolversTypes['Enrollment']>, ParentType, ContextType>;
  hasPotentialDuplicates?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  legacyId?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  mailingAddress?: Resolver<Maybe<GqlResolversTypes['Address']>, ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  newEmail?: Resolver<Maybe<GqlResolversTypes['EmailAddress']>, ParentType, ContextType>;
  partner?: Resolver<GqlResolversTypes['Partner'], ParentType, ContextType>;
  partnerId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  payeeType?: Resolver<GqlResolversTypes['PayeeType'], ParentType, ContextType>;
  phone?: Resolver<Maybe<GqlResolversTypes['PhoneNumber']>, ParentType, ContextType>;
  recentLogin?: Resolver<Maybe<GqlResolversTypes['LoginDetails']>, ParentType, ContextType>;
  referrals?: Resolver<Array<GqlResolversTypes['ProgramReferral']>, ParentType, ContextType>;
  savedViews?: Resolver<Maybe<Array<GqlResolversTypes['SavedView']>>, ParentType, ContextType>;
  taxForms?: Resolver<Array<GqlResolversTypes['TaxForm']>, ParentType, ContextType>;
  taxId?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  validatedEmail?: Resolver<GqlResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlUserMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['UserMutations'] = GqlResolversParentTypes['UserMutations']> = {
  create?: Resolver<GqlResolversTypes['UserResponse'], ParentType, ContextType, RequireFields<GqlUserMutationsCreateArgs, 'input'>>;
  createW9TaxForm?: Resolver<GqlResolversTypes['TaxFormMutationResponse'], ParentType, ContextType, RequireFields<GqlUserMutationsCreateW9TaxFormArgs, 'input'>>;
  linkLegacyUser?: Resolver<GqlResolversTypes['UserResponse'], ParentType, ContextType, RequireFields<GqlUserMutationsLinkLegacyUserArgs, 'input'>>;
  saveBankAccount?: Resolver<GqlResolversTypes['UserResponse'], ParentType, ContextType, RequireFields<GqlUserMutationsSaveBankAccountArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['UserResponse'], ParentType, ContextType, RequireFields<GqlUserMutationsUpdateArgs, 'input'>>;
  updateProfile?: Resolver<GqlResolversTypes['UserResponse'], ParentType, ContextType, RequireFields<GqlUserMutationsUpdateProfileArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlUserPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['UserPage'] = GqlResolversParentTypes['UserPage']> = {
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  users?: Resolver<Array<GqlResolversTypes['User']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlUserResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['UserResponse'] = GqlResolversParentTypes['UserResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlUtcOffsetScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['UtcOffset'], any> {
  name: 'UtcOffset';
}

export type GqlVendorResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Vendor'] = GqlResolversParentTypes['Vendor']> = {
  aggregatePayments?: Resolver<GqlResolversTypes['AggregatePayment'], ParentType, ContextType>;
  bankAccount?: Resolver<Maybe<GqlResolversTypes['BankAccount']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  documents?: Resolver<Array<GqlResolversTypes['Document']>, ParentType, ContextType>;
  email?: Resolver<Maybe<GqlResolversTypes['EmailAddress']>, ParentType, ContextType>;
  externalId?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  mailingAddress?: Resolver<Maybe<GqlResolversTypes['Address']>, ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  notes?: Resolver<Array<GqlResolversTypes['Note']>, ParentType, ContextType>;
  payeeType?: Resolver<GqlResolversTypes['PayeeType'], ParentType, ContextType>;
  payments?: Resolver<Array<GqlResolversTypes['Payment']>, ParentType, ContextType>;
  phone?: Resolver<Maybe<GqlResolversTypes['PhoneNumber']>, ParentType, ContextType>;
  taxId?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  types?: Resolver<Array<GqlResolversTypes['VendorType']>, ParentType, ContextType>;
  updatedAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVendorMutationResponseResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VendorMutationResponse'] = GqlResolversParentTypes['VendorMutationResponse']> = {
  metadata?: Resolver<GqlResolversTypes['ResponseMetadata'], ParentType, ContextType>;
  record?: Resolver<Maybe<GqlResolversTypes['Vendor']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVendorMutationsResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VendorMutations'] = GqlResolversParentTypes['VendorMutations']> = {
  create?: Resolver<GqlResolversTypes['VendorMutationResponse'], ParentType, ContextType, RequireFields<GqlVendorMutationsCreateArgs, 'input'>>;
  delete?: Resolver<GqlResolversTypes['VendorMutationResponse'], ParentType, ContextType, RequireFields<GqlVendorMutationsDeleteArgs, 'input'>>;
  removeDocuments?: Resolver<GqlResolversTypes['VendorMutationResponse'], ParentType, ContextType, RequireFields<GqlVendorMutationsRemoveDocumentsArgs, 'input'>>;
  update?: Resolver<GqlResolversTypes['VendorMutationResponse'], ParentType, ContextType, RequireFields<GqlVendorMutationsUpdateArgs, 'input'>>;
  uploadDocuments?: Resolver<GqlResolversTypes['VendorMutationResponse'], ParentType, ContextType, RequireFields<GqlVendorMutationsUploadDocumentsArgs, 'input'>>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVendorPageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VendorPage'] = GqlResolversParentTypes['VendorPage']> = {
  nodes?: Resolver<Array<GqlResolversTypes['Vendor']>, ParentType, ContextType>;
  pageInfo?: Resolver<GqlResolversTypes['PageInfo'], ParentType, ContextType>;
  vendors?: Resolver<Array<GqlResolversTypes['Vendor']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVendorTypeResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VendorType'] = GqlResolversParentTypes['VendorType']> = {
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  name?: Resolver<GqlResolversTypes['NonEmptyString'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVerificationConfigResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VerificationConfig'] = GqlResolversParentTypes['VerificationConfig']> = {
  dataLookup?: Resolver<Maybe<Array<GqlResolversTypes['VerificationFileKey']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVerificationConfigurationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VerificationConfiguration'] = GqlResolversParentTypes['VerificationConfiguration']> = {
  dataLookup?: Resolver<Maybe<GqlResolversTypes['DataLookupConfiguration']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  service?: Resolver<GqlResolversTypes['VerificationServiceType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVerificationDetailResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VerificationDetail'] = GqlResolversParentTypes['VerificationDetail']> = {
  confidence?: Resolver<GqlResolversTypes['Float'], ParentType, ContextType>;
  description?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  diff?: Resolver<Maybe<GqlResolversTypes['VerificationDiff']>, ParentType, ContextType>;
  key?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  weight?: Resolver<GqlResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVerificationDiffResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VerificationDiff'] = GqlResolversParentTypes['VerificationDiff']> = {
  provided?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  verified?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVerificationFileKeyResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VerificationFileKey'] = GqlResolversParentTypes['VerificationFileKey']> = {
  details?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  key?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  sample?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVerificationMetadataResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VerificationMetadata'] = GqlResolversParentTypes['VerificationMetadata']> = {
  key?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  value?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlVerifiedPageInfoResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['VerifiedPageInfo'] = GqlResolversParentTypes['VerifiedPageInfo']> = {
  count?: Resolver<GqlResolversTypes['NonNegativeInt'], ParentType, ContextType>;
  nextCursor?: Resolver<Maybe<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  verifiedIds?: Resolver<Array<GqlResolversTypes['NonEmptyString']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface GqlVoidScalarConfig extends GraphQLScalarTypeConfig<GqlResolversTypes['Void'], any> {
  name: 'Void';
}

export type GqlWorkflowResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['Workflow'] = GqlResolversParentTypes['Workflow']> = {
  workflowStages?: Resolver<Array<GqlResolversTypes['WorkflowStage']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlWorkflowEventResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['WorkflowEvent'] = GqlResolversParentTypes['WorkflowEvent']> = {
  action?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  author?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  createdAt?: Resolver<GqlResolversTypes['DateTime'], ParentType, ContextType>;
  details?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  entityId?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  entityType?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<GqlResolversTypes['UUID'], ParentType, ContextType>;
  newAssignee?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  newValue?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  previousAssignee?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  previousValue?: Resolver<Maybe<GqlResolversTypes['String']>, ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<GqlResolversTypes['DateTime']>, ParentType, ContextType>;
  user?: Resolver<Maybe<GqlResolversTypes['User']>, ParentType, ContextType>;
  userId?: Resolver<Maybe<GqlResolversTypes['UUID']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlWorkflowNotificationResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['WorkflowNotification'] = GqlResolversParentTypes['WorkflowNotification']> = {
  template?: Resolver<GqlResolversTypes['NotificationTemplate'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlWorkflowStageResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['WorkflowStage'] = GqlResolversParentTypes['WorkflowStage']> = {
  description?: Resolver<GqlResolversTypes['String'], ParentType, ContextType>;
  order?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  status?: Resolver<GqlResolversTypes['CaseStatus'], ParentType, ContextType>;
  workflowNotifications?: Resolver<Array<GqlResolversTypes['WorkflowNotification']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlWorkflowSummaryResolvers<ContextType = AuthenticatedContext, ParentType extends GqlResolversParentTypes['WorkflowSummary'] = GqlResolversParentTypes['WorkflowSummary']> = {
  casesApproved?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  casesCertified?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  casesDenied?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  casesReviewed?: Resolver<GqlResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GqlResolvers<ContextType = AuthenticatedContext> = {
  AccessRequest?: GqlAccessRequestResolvers<ContextType>;
  AccessRequestMutationResponse?: GqlAccessRequestMutationResponseResolvers<ContextType>;
  AccessRequestMutations?: GqlAccessRequestMutationsResolvers<ContextType>;
  AccessRequestPage?: GqlAccessRequestPageResolvers<ContextType>;
  AccountNumber?: GraphQLScalarType;
  ActiveLabel?: GqlActiveLabelResolvers<ContextType>;
  AddNoteToApplicationAnswerPayload?: GqlAddNoteToApplicationAnswerPayloadResolvers<ContextType>;
  AddReviewToApplicationAnswerPayload?: GqlAddReviewToApplicationAnswerPayloadResolvers<ContextType>;
  Address?: GqlAddressResolvers<ContextType>;
  AddressField?: GqlAddressFieldResolvers<ContextType>;
  AddressFieldFilter?: GqlAddressFieldFilterResolvers<ContextType>;
  AddressPage?: GqlAddressPageResolvers<ContextType>;
  Admin?: GqlAdminResolvers<ContextType>;
  AdminMutations?: GqlAdminMutationsResolvers<ContextType>;
  AdminPage?: GqlAdminPageResolvers<ContextType>;
  AdminResponse?: GqlAdminResponseResolvers<ContextType>;
  AdvocateIdentityConfig?: GqlAdvocateIdentityConfigResolvers<ContextType>;
  AggregateApplicantsReferred?: GqlAggregateApplicantsReferredResolvers<ContextType>;
  AggregateApplication?: GqlAggregateApplicationResolvers<ContextType>;
  AggregatePayment?: GqlAggregatePaymentResolvers<ContextType>;
  AnalyticsResource?: GqlAnalyticsResourceResolvers<ContextType>;
  ApplicantIdentityConfig?: GqlApplicantIdentityConfigResolvers<ContextType>;
  ApplicantNameConsistency?: GqlApplicantNameConsistencyResolvers<ContextType>;
  ApplicantNameConsistencyResult?: GqlApplicantNameConsistencyResultResolvers<ContextType>;
  ApplicantProfile?: GqlApplicantProfileResolvers<ContextType>;
  ApplicantProfileConfig?: GqlApplicantProfileConfigResolvers<ContextType>;
  ApplicantProfileConfiguration?: GqlApplicantProfileConfigurationResolvers<ContextType>;
  ApplicantRolesConfig?: GqlApplicantRolesConfigResolvers<ContextType>;
  ApplicantType?: GqlApplicantTypeResolvers<ContextType>;
  Application?: GqlApplicationResolvers<ContextType>;
  ApplicationAnswer?: GqlApplicationAnswerResolvers<ContextType>;
  ApplicationAnswerNote?: GqlApplicationAnswerNoteResolvers<ContextType>;
  ApplicationAnswerReview?: GqlApplicationAnswerReviewResolvers<ContextType>;
  ApplicationConfigOverrides?: GqlApplicationConfigOverridesResolvers<ContextType>;
  ApplicationConfigReviewFields?: GqlApplicationConfigReviewFieldsResolvers<ContextType>;
  ApplicationConfiguration?: GqlApplicationConfigurationResolvers<ContextType>;
  ApplicationField?: GqlApplicationFieldResolvers<ContextType>;
  ApplicationFieldValidation?: GqlApplicationFieldValidationResolvers<ContextType>;
  ApplicationFieldValidationRule?: GqlApplicationFieldValidationRuleResolvers<ContextType>;
  ApplicationMutationResponse?: GqlApplicationMutationResponseResolvers<ContextType>;
  ApplicationMutations?: GqlApplicationMutationsResolvers<ContextType>;
  ApplicationPage?: GqlApplicationPageResolvers<ContextType>;
  ApplicationQuestion?: GqlApplicationQuestionResolvers<ContextType>;
  ApplicationQuestionCopy?: GqlApplicationQuestionCopyResolvers<ContextType>;
  ApplicationQuestionGroup?: GqlApplicationQuestionGroupResolvers<ContextType>;
  ApplicationQuestionGroupOverview?: GqlApplicationQuestionGroupOverviewResolvers<ContextType>;
  ApplicationScore?: GqlApplicationScoreResolvers<ContextType>;
  ApplicationSection?: GqlApplicationSectionResolvers<ContextType>;
  ApplicationSectionOverview?: GqlApplicationSectionOverviewResolvers<ContextType>;
  ApplicationSubmission?: GqlApplicationSubmissionResolvers<ContextType>;
  ApplicationVerification?: GqlApplicationVerificationResolvers<ContextType>;
  ApplicationVersion?: GqlApplicationVersionResolvers<ContextType>;
  AssigneeId?: GraphQLScalarType;
  Assignment?: GqlAssignmentResolvers<ContextType>;
  BankAccount?: GqlBankAccountResolvers<ContextType>;
  BigInt?: GraphQLScalarType;
  BulkCaseMutationResponse?: GqlBulkCaseMutationResponseResolvers<ContextType>;
  BulkFulfillmentResponse?: GqlBulkFulfillmentResponseResolvers<ContextType>;
  BulkResponseError?: GqlBulkResponseErrorResolvers<ContextType>;
  BulkResponseMetadata?: GqlBulkResponseMetadataResolvers<ContextType>;
  BulkUserMutationResponse?: GqlBulkUserMutationResponseResolvers<ContextType>;
  Byte?: GraphQLScalarType;
  CalculatedField?: GqlCalculatedFieldResolvers<ContextType>;
  CalculatedFieldDisplay?: GqlCalculatedFieldDisplayResolvers<ContextType>;
  Case?: GqlCaseResolvers<ContextType>;
  CaseApplicationAnswer?: GqlCaseApplicationAnswerResolvers<ContextType>;
  CaseApplicationsDocument?: GqlCaseApplicationsDocumentResolvers<ContextType>;
  CaseCounts?: GqlCaseCountsResolvers<ContextType>;
  CaseMetadata?: GqlCaseMetadataResolvers<ContextType>;
  CaseMutationResponse?: GqlCaseMutationResponseResolvers<ContextType>;
  CaseMutations?: GqlCaseMutationsResolvers<ContextType>;
  CasePage?: GqlCasePageResolvers<ContextType>;
  CaseParticipant?: GqlCaseParticipantResolvers<ContextType>;
  CaseTag?: GqlCaseTagResolvers<ContextType>;
  CaseTagMutationResponse?: GqlCaseTagMutationResponseResolvers<ContextType>;
  CasesApplicationsSource?: GqlCasesApplicationsSourceResolvers<ContextType>;
  CasesMutationResponse?: GqlCasesMutationResponseResolvers<ContextType>;
  Changelog?: GqlChangelogResolvers<ContextType>;
  ChannelConfiguration?: GqlChannelConfigurationResolvers<ContextType>;
  CheckboxField?: GqlCheckboxFieldResolvers<ContextType>;
  CheckboxGroupField?: GqlCheckboxGroupFieldResolvers<ContextType>;
  ClaimFulfillmentResponse?: GqlClaimFulfillmentResponseResolvers<ContextType>;
  Comment?: GqlCommentResolvers<ContextType>;
  CommunicationChannelsConfig?: GqlCommunicationChannelsConfigResolvers<ContextType>;
  CommunicationChannelsConfiguration?: GqlCommunicationChannelsConfigurationResolvers<ContextType>;
  CommunicationPreferences?: GqlCommunicationPreferencesResolvers<ContextType>;
  ComplexField?: GqlComplexFieldResolvers<ContextType>;
  CountryCode?: GraphQLScalarType;
  Cuid?: GraphQLScalarType;
  Currency?: GraphQLScalarType;
  CursorPageInfo?: GqlCursorPageInfoResolvers<ContextType>;
  DID?: GraphQLScalarType;
  DataLookupConfiguration?: GqlDataLookupConfigurationResolvers<ContextType>;
  DataLookupField?: GqlDataLookupFieldResolvers<ContextType>;
  Date?: GraphQLScalarType;
  DateField?: GqlDateFieldResolvers<ContextType>;
  DateFieldValidation?: GqlDateFieldValidationResolvers<ContextType>;
  DateTime?: GraphQLScalarType;
  DateTimeISO?: GraphQLScalarType;
  DeweyDecimal?: GraphQLScalarType;
  DoctopusTag?: GqlDoctopusTagResolvers<ContextType>;
  Document?: GqlDocumentResolvers<ContextType>;
  DocumentConsistency?: GqlDocumentConsistencyResolvers<ContextType>;
  DocumentField?: GqlDocumentFieldResolvers<ContextType>;
  DocumentFieldCopy?: GqlDocumentFieldCopyResolvers<ContextType>;
  DocumentMutations?: GqlDocumentMutationsResolvers<ContextType>;
  DocumentSummary?: GqlDocumentSummaryResolvers<ContextType>;
  DocumentTag?: GqlDocumentTagResolvers<ContextType>;
  DocumentsMutationResponse?: GqlDocumentsMutationResponseResolvers<ContextType>;
  DropdownField?: GqlDropdownFieldResolvers<ContextType>;
  Duration?: GraphQLScalarType;
  EligibilityConfig?: GqlEligibilityConfigResolvers<ContextType>;
  EligibilityQuestion?: GqlEligibilityQuestionResolvers<ContextType>;
  EmailAddress?: GraphQLScalarType;
  EmailTemplateContent?: GqlEmailTemplateContentResolvers<ContextType>;
  Enrollment?: GqlEnrollmentResolvers<ContextType>;
  EnrollmentMutationResponse?: GqlEnrollmentMutationResponseResolvers<ContextType>;
  EnrollmentMutations?: GqlEnrollmentMutationsResolvers<ContextType>;
  EnrollmentOutcome?: GqlEnrollmentOutcomeResolvers<ContextType>;
  EnrollmentService?: GqlEnrollmentServiceResolvers<ContextType>;
  Feature?: GqlFeatureResolvers<ContextType>;
  FeatureSetting?: GqlFeatureSettingResolvers<ContextType>;
  Feedback?: GqlFeedbackResolvers<ContextType>;
  FieldResponse?: GqlFieldResponseResolvers<ContextType>;
  FormDefinition?: GqlFormDefinitionResolvers<ContextType>;
  Fulfillment?: GqlFulfillmentResolvers<ContextType>;
  FulfillmentMeta?: GqlFulfillmentMetaResolvers<ContextType>;
  FulfillmentMutations?: GqlFulfillmentMutationsResolvers<ContextType>;
  FulfillmentPage?: GqlFulfillmentPageResolvers<ContextType>;
  FulfillmentResponse?: GqlFulfillmentResponseResolvers<ContextType>;
  Fund?: GqlFundResolvers<ContextType>;
  FundConfig?: GqlFundConfigResolvers<ContextType>;
  FundMutationResponse?: GqlFundMutationResponseResolvers<ContextType>;
  FundMutations?: GqlFundMutationsResolvers<ContextType>;
  FundPage?: GqlFundPageResolvers<ContextType>;
  FundStats?: GqlFundStatsResolvers<ContextType>;
  GUID?: GraphQLScalarType;
  GeoPoint?: GqlGeoPointResolvers<ContextType>;
  HSL?: GraphQLScalarType;
  HSLA?: GraphQLScalarType;
  HasAccessResponse?: GqlHasAccessResponseResolvers<ContextType>;
  HexColorCode?: GraphQLScalarType;
  Hexadecimal?: GraphQLScalarType;
  HouseholdLimit?: GqlHouseholdLimitResolvers<ContextType>;
  IBAN?: GraphQLScalarType;
  IP?: GraphQLScalarType;
  IPCPatent?: GraphQLScalarType;
  IPv4?: GraphQLScalarType;
  IPv6?: GraphQLScalarType;
  ISBN?: GraphQLScalarType;
  ISO8601Duration?: GraphQLScalarType;
  IdentityConfig?: GqlIdentityConfigResolvers<ContextType>;
  IdentityMutations?: GqlIdentityMutationsResolvers<ContextType>;
  IdentityResponse?: GqlIdentityResponseResolvers<ContextType>;
  IdentityUser?: GqlIdentityUserResolvers<ContextType>;
  IncidentMessage?: GqlIncidentMessageResolvers<ContextType>;
  IncidentMessagePage?: GqlIncidentMessagePageResolvers<ContextType>;
  IncidentMutationResponse?: GqlIncidentMutationResponseResolvers<ContextType>;
  IncomeLimit?: GqlIncomeLimitResolvers<ContextType>;
  IncomeLimitArea?: GqlIncomeLimitAreaResolvers<ContextType>;
  IncomeLimitAreaPage?: GqlIncomeLimitAreaPageResolvers<ContextType>;
  IntroPage?: GqlIntroPageResolvers<ContextType>;
  IntroPageContentBlock?: GqlIntroPageContentBlockResolvers<ContextType>;
  InvitationCode?: GqlInvitationCodeResolvers<ContextType>;
  JSON?: GraphQLScalarType;
  JSONObject?: GraphQLScalarType;
  JWT?: GraphQLScalarType;
  LCCSubclass?: GraphQLScalarType;
  Latitude?: GraphQLScalarType;
  Limits?: GqlLimitsResolvers<ContextType>;
  LinkAttempt?: GqlLinkAttemptResolvers<ContextType>;
  LocalDate?: GraphQLScalarType;
  LocalDateTime?: GraphQLScalarType;
  LocalEndTime?: GraphQLScalarType;
  LocalTime?: GraphQLScalarType;
  Locale?: GraphQLScalarType;
  LoginDetails?: GqlLoginDetailsResolvers<ContextType>;
  Long?: GraphQLScalarType;
  Longitude?: GraphQLScalarType;
  MAC?: GraphQLScalarType;
  ModelVersion?: GqlModelVersionResolvers<ContextType>;
  Month?: GqlMonthResolvers<ContextType>;
  Mutation?: GqlMutationResolvers<ContextType>;
  NegativeFloat?: GraphQLScalarType;
  NegativeInt?: GraphQLScalarType;
  NonEmptyString?: GraphQLScalarType;
  NonNegativeFloat?: GraphQLScalarType;
  NonNegativeInt?: GraphQLScalarType;
  NonPositiveFloat?: GraphQLScalarType;
  NonPositiveInt?: GraphQLScalarType;
  Note?: GqlNoteResolvers<ContextType>;
  NoteMutationResponse?: GqlNoteMutationResponseResolvers<ContextType>;
  NoteMutations?: GqlNoteMutationsResolvers<ContextType>;
  NotificationAction?: GqlNotificationActionResolvers<ContextType>;
  NotificationConfig?: GqlNotificationConfigResolvers<ContextType>;
  NotificationTemplate?: GqlNotificationTemplateResolvers<ContextType>;
  OCRResult?: GqlOcrResultResolvers<ContextType>;
  ObjectID?: GraphQLScalarType;
  OptionLabelValue?: GqlOptionLabelValueResolvers<ContextType>;
  Outcome?: GqlOutcomeResolvers<ContextType>;
  PageInfo?: GqlPageInfoResolvers<ContextType>;
  Partner?: GqlPartnerResolvers<ContextType>;
  PartnerConfig?: GqlPartnerConfigResolvers<ContextType>;
  PartnerIncident?: GqlPartnerIncidentResolvers<ContextType>;
  PartnerIncidentMutations?: GqlPartnerIncidentMutationsResolvers<ContextType>;
  PartnerPage?: GqlPartnerPageResolvers<ContextType>;
  PartnerWhitelabeling?: GqlPartnerWhitelabelingResolvers<ContextType>;
  Password?: GraphQLScalarType;
  Payee?: GqlPayeeResolvers<ContextType>;
  Payment?: GqlPaymentResolvers<ContextType>;
  PaymentMailingAddress?: GqlPaymentMailingAddressResolvers<ContextType>;
  PaymentMutationResponse?: GqlPaymentMutationResponseResolvers<ContextType>;
  PaymentMutations?: GqlPaymentMutationsResolvers<ContextType>;
  PaymentPage?: GqlPaymentPageResolvers<ContextType>;
  PaymentPattern?: GqlPaymentPatternResolvers<ContextType>;
  PaymentsConfig?: GqlPaymentsConfigResolvers<ContextType>;
  PhoneNumber?: GraphQLScalarType;
  Port?: GraphQLScalarType;
  PositiveFloat?: GraphQLScalarType;
  PositiveInt?: GraphQLScalarType;
  PostalCode?: GraphQLScalarType;
  Prediction?: GqlPredictionResolvers<ContextType>;
  PresetMutations?: GqlPresetMutationsResolvers<ContextType>;
  Primitive?: GraphQLScalarType;
  ProfileAnswer?: GqlProfileAnswerResolvers<ContextType>;
  ProfileKey?: GqlProfileKeyResolvers<ContextType>;
  Program?: GqlProgramResolvers<ContextType>;
  ProgramApplicantType?: GqlProgramApplicantTypeResolvers<ContextType>;
  ProgramApplicationConfiguration?: GqlProgramApplicationConfigurationResolvers<ContextType>;
  ProgramConfig?: GqlProgramConfigResolvers<ContextType>;
  ProgramContext?: GqlProgramContextResolvers<ContextType>;
  ProgramDocument?: GqlProgramDocumentResolvers<ContextType>;
  ProgramFundStats?: GqlProgramFundStatsResolvers<ContextType>;
  ProgramMutationResponse?: GqlProgramMutationResponseResolvers<ContextType>;
  ProgramMutations?: GqlProgramMutationsResolvers<ContextType>;
  ProgramPage?: GqlProgramPageResolvers<ContextType>;
  ProgramReferral?: GqlProgramReferralResolvers<ContextType>;
  ProgramReferralMutationResponse?: GqlProgramReferralMutationResponseResolvers<ContextType>;
  ProgramReferralMutations?: GqlProgramReferralMutationsResolvers<ContextType>;
  ProgramStats?: GqlProgramStatsResolvers<ContextType>;
  ProgramsMutationResponse?: GqlProgramsMutationResponseResolvers<ContextType>;
  Query?: GqlQueryResolvers<ContextType>;
  QuestionGroupResponse?: GqlQuestionGroupResponseResolvers<ContextType>;
  QuestionResponse?: GqlQuestionResponseResolvers<ContextType>;
  RGB?: GraphQLScalarType;
  RGBA?: GraphQLScalarType;
  RadioListField?: GqlRadioListFieldResolvers<ContextType>;
  ReapplicationRules?: GqlReapplicationRulesResolvers<ContextType>;
  RecurringPaymentConfig?: GqlRecurringPaymentConfigResolvers<ContextType>;
  ResolvedEntities?: GqlResolvedEntitiesResolvers<ContextType>;
  ResponseMetadata?: GqlResponseMetadataResolvers<ContextType>;
  RoleConfig?: GqlRoleConfigResolvers<ContextType>;
  RoutingNumber?: GraphQLScalarType;
  Ruleset?: GqlRulesetResolvers<ContextType>;
  RulesetPage?: GqlRulesetPageResolvers<ContextType>;
  SAMLConfig?: GqlSamlConfigResolvers<ContextType>;
  SESSN?: GraphQLScalarType;
  SafeInt?: GraphQLScalarType;
  SavedView?: GqlSavedViewResolvers<ContextType>;
  SavedViewMutationResponse?: GqlSavedViewMutationResponseResolvers<ContextType>;
  SavedViewMutations?: GqlSavedViewMutationsResolvers<ContextType>;
  SearchPage?: GqlSearchPageResolvers<ContextType>;
  SectionResponse?: GqlSectionResponseResolvers<ContextType>;
  SemVer?: GraphQLScalarType;
  Service?: GqlServiceResolvers<ContextType>;
  SmsTemplateContent?: GqlSmsTemplateContentResolvers<ContextType>;
  StatusOverride?: GqlStatusOverrideResolvers<ContextType>;
  SubmissionBullet?: GqlSubmissionBulletResolvers<ContextType>;
  SubmissionOverrides?: GqlSubmissionOverridesResolvers<ContextType>;
  Tag?: GqlTagResolvers<ContextType>;
  TagAutomation?: GqlTagAutomationResolvers<ContextType>;
  TagAutomationMutationResponse?: GqlTagAutomationMutationResponseResolvers<ContextType>;
  TagAutomationsMutations?: GqlTagAutomationsMutationsResolvers<ContextType>;
  TagMutationResponse?: GqlTagMutationResponseResolvers<ContextType>;
  TagMutations?: GqlTagMutationsResolvers<ContextType>;
  TagsMutationResponse?: GqlTagsMutationResponseResolvers<ContextType>;
  TaxForm?: GqlTaxFormResolvers<ContextType>;
  TaxFormMutationResponse?: GqlTaxFormMutationResponseResolvers<ContextType>;
  TemplateContent?: GqlTemplateContentResolvers<ContextType>;
  TextField?: GqlTextFieldResolvers<ContextType>;
  Time?: GraphQLScalarType;
  TimeZone?: GraphQLScalarType;
  Timestamp?: GraphQLScalarType;
  TypographyField?: GqlTypographyFieldResolvers<ContextType>;
  URL?: GraphQLScalarType;
  USCurrency?: GraphQLScalarType;
  UUID?: GraphQLScalarType;
  UnsignedFloat?: GraphQLScalarType;
  UnsignedInt?: GraphQLScalarType;
  Upload?: GraphQLScalarType;
  User?: GqlUserResolvers<ContextType>;
  UserMutations?: GqlUserMutationsResolvers<ContextType>;
  UserPage?: GqlUserPageResolvers<ContextType>;
  UserResponse?: GqlUserResponseResolvers<ContextType>;
  UtcOffset?: GraphQLScalarType;
  Vendor?: GqlVendorResolvers<ContextType>;
  VendorMutationResponse?: GqlVendorMutationResponseResolvers<ContextType>;
  VendorMutations?: GqlVendorMutationsResolvers<ContextType>;
  VendorPage?: GqlVendorPageResolvers<ContextType>;
  VendorType?: GqlVendorTypeResolvers<ContextType>;
  VerificationConfig?: GqlVerificationConfigResolvers<ContextType>;
  VerificationConfiguration?: GqlVerificationConfigurationResolvers<ContextType>;
  VerificationDetail?: GqlVerificationDetailResolvers<ContextType>;
  VerificationDiff?: GqlVerificationDiffResolvers<ContextType>;
  VerificationFileKey?: GqlVerificationFileKeyResolvers<ContextType>;
  VerificationMetadata?: GqlVerificationMetadataResolvers<ContextType>;
  VerifiedPageInfo?: GqlVerifiedPageInfoResolvers<ContextType>;
  Void?: GraphQLScalarType;
  Workflow?: GqlWorkflowResolvers<ContextType>;
  WorkflowEvent?: GqlWorkflowEventResolvers<ContextType>;
  WorkflowNotification?: GqlWorkflowNotificationResolvers<ContextType>;
  WorkflowStage?: GqlWorkflowStageResolvers<ContextType>;
  WorkflowSummary?: GqlWorkflowSummaryResolvers<ContextType>;
};

export type GqlDirectiveResolvers<ContextType = AuthenticatedContext> = {
  constraint?: GqlConstraintDirectiveResolver<any, any, ContextType>;
  experimental?: GqlExperimentalDirectiveResolver<any, any, ContextType>;
  masked?: GqlMaskedDirectiveResolver<any, any, ContextType>;
};
