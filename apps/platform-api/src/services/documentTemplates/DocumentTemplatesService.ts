import QueryServiceImplementation from '../QueryService.js';
import { DocumentationTemplateRepository } from '@platform-api/@types/repositories/index.js';
import {
  DocumentationTemplateService as IDocumentationTemplateService,
  PartnerService,
} from '../../@types/services.js';
import {
  DocumentationTemplate,
  Application,
  CreateDocumentationTemplateInput,
} from '@bybeam/platform-types';
import sanitize from 'sanitize-filename';
import { UploadRepository } from '@platform-api/@types/upload.js';
import { LoginToken } from '@platform-api/@types/authentication.js';
import { FileUpload } from 'graphql-upload/Upload.mjs';
import { logger } from '../../utils/logger.js';

export class DocumentTemplatesService
  extends QueryServiceImplementation<DocumentationTemplate, DocumentationTemplateRepository>
  implements IDocumentationTemplateService
{
  private uploadRepository: UploadRepository;
  private partnerService: PartnerService;

  constructor({
    partnerService,
    uploadRepository,
    documentationTemplateRepository,
  }: {
    partnerService: PartnerService;
    uploadRepository: UploadRepository;
    documentationTemplateRepository: DocumentationTemplateRepository;
  }) {
    super(documentationTemplateRepository);
    this.uploadRepository = uploadRepository;
    this.partnerService = partnerService;
  }

  public async findByPartnerId(partnerId: string): Promise<DocumentationTemplate[]> {
    return this.repository.findBy({ partnerId });
  }

  public async create(
    { partnerId }: LoginToken,
    input: CreateDocumentationTemplateInput,
  ): Promise<DocumentationTemplate> {
    try {
      const { file: filePromise, ...templateData } = input;
      const file = await filePromise;
      if (!this.validateFileType(file)) {
        throw new Error('Invalid file type');
      }
      const sanitizedFile = {
        ...file,
        filename: sanitize(file.filename),
      };

      const partner = await this.partnerService.findById(partnerId);
      const upload = await this.uploadRepository.upload({
        partner,
        prefix: 'documentation_templates',
        files: [sanitizedFile],
      });

      return await this.repository.save({
        ...templateData,
        documentKey: upload[0].documentKey,
        partnerId: partner.id,
      });
    } catch (error) {
      logger.error({ error }, 'DocumentTemplatesService.create: failed');
      throw new Error('Failed to create documentation template');
    }
  }
  public async generateDocumentation(
    template: DocumentationTemplate,
    application: Application,
  ): Promise<Uint8Array | undefined> {
    return undefined;
  }

  private validateFileType(file: FileUpload): boolean {
    return file.mimetype === 'application/pdf';
  }
}
