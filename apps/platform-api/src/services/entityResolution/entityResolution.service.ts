import { Match, NameConsistencyResponse } from '@bybeam/entity-resolution';
import { ApplicantTypeRole } from '@bybeam/platform-types';
import { CaseRepository } from '@platform-api/@types/repositories/core.js';
import type {
  ApplicantNameConsistency,
  ApplicantNameConsistencyResult,
} from '@platform-api/graphql/modules/Case/ResolvedEntities/resolvedEntities.types.js';
import EntityResolutionRepository from '@platform-api/repositories/external/EntityResolutionRepository.js';
import { ResolverStatus } from '../../graphql/modules/Case/ResolvedEntities/resolvedEntities.types.js';
import { logger } from '../../utils/logger.js';

interface Dependencies {
  readonly repository: EntityResolutionRepository;
  readonly caseRepository: CaseRepository;
}

export default class EntityResolutionService {
  private readonly repository: EntityResolutionRepository;
  private readonly caseRepository: CaseRepository;
  // Map used as a simple dataloader: caches in-flight promises keyed by caseId
  private readonly nameConsistencyLoader: Map<string, Promise<ApplicantNameConsistency>> =
    new Map();

  constructor({ repository, caseRepository }: Dependencies) {
    this.repository = repository;
    this.caseRepository = caseRepository;
  }

  /**
   * Triggers duplicate detection for an application version.
   * This method calls the entity resolution service to detect potential duplicates
   * and logs the results. It never throws errors to avoid blocking the calling operation.
   *
   * @param applicationVersionId - The ID of the application version to check for duplicates
   * @param programId - The ID of the program the application belongs to
   * @param recalculate - Whether to recalculate duplicates even if already cached
   * @returns Promise that resolves when duplicate detection is complete
   */
  public async detectDuplicateApplicationVersion(
    applicationVersionId: string,
    programId: string,
    recalculate = false,
  ): Promise<void> {
    if (!this.repository) {
      logger.error('EntityResolutionRepository not available', {
        applicationVersionId,
        programId,
      });
      return;
    }

    try {
      await this.repository.detectDuplicateApplicationVersion(
        applicationVersionId,
        programId,
        recalculate,
      );
      // Only log success at debug level to avoid noise
      logger.debug('Duplicate detection completed successfully', {
        applicationVersionId,
        programId,
      });
    } catch (error) {
      logger.error('Duplicate detection failed', {
        applicationVersionId,
        programId,
        error:
          error instanceof Error
            ? {
                message: error.message,
                name: error.name,
              }
            : error,
      });
      // Don't throw - we never want to block on duplicate detection
    }
  }
  public async applicantNameConsistency(caseId: string): Promise<ApplicantNameConsistency> {
    // If there's an in-flight request for this caseId, return that promise
    const existing = this.nameConsistencyLoader.get(caseId);
    if (existing) return existing;

    // Otherwise create, store, and return a new promise. Remove the cache entry when finished
    const promise = this.computeApplicantNameConsistency(caseId).finally(() =>
      this.nameConsistencyLoader.delete(caseId),
    );

    this.nameConsistencyLoader.set(caseId, promise);
    return promise;
  }

  private async computeApplicantNameConsistency(caseId: string): Promise<ApplicantNameConsistency> {
    try {
      const caseEntity = await this.caseRepository.findOne({
        where: { id: caseId },
        relations: [
          'applications',
          'applications.documents',
          'applications.submitter',
          'applications.submitter.applicantProfile',
          'applications.submitter.applicantProfile.user',
          'documents',
        ],
      });
      const caseDocumentIds = caseEntity?.documents?.map((doc) => doc.id) || [];

      const primaryApplication =
        caseEntity?.applications?.find(
          (app) =>
            app.submitter?.applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty,
        ) ?? caseEntity?.applications?.[0];

      if (!primaryApplication) {
        logger.error('No applications found for case', { caseId });
        return { status: ResolverStatus.FAILED };
      }

      const appDocumentIds = primaryApplication?.documents?.map((doc) => doc.id) || [];

      const primaryApplicantProfile = primaryApplication?.submitter?.applicantProfile;
      const applicantUserId = primaryApplicantProfile?.userId;
      const canonicalName = primaryApplicantProfile?.user.name;
      const documentIds = Array.from(new Set([...caseDocumentIds, ...appDocumentIds]));

      if (!canonicalName || !applicantUserId || documentIds.length === 0) {
        logger.error('Insufficient data to perform name consistency check', { caseId });
        return { status: ResolverStatus.FAILED };
      }

      const result = await this.repository.nameConsistency(
        canonicalName,
        applicantUserId,
        documentIds,
      );

      return {
        status: ResolverStatus.COMPLETED,
        result: this.mapToApplicantNameConsistencyResult(result),
      };
    } catch (err) {
      logger.error('Error computing applicantNameConsistency', { caseId, err });
      return { status: ResolverStatus.FAILED };
    }
  }

  private mapToApplicantNameConsistencyResult(
    resp: NameConsistencyResponse,
  ): ApplicantNameConsistencyResult {
    // Map fields explicitly from the ER response to our GraphQL contract
    // Adjust property names below to match the actual NameConsistencyResponse shape
    return {
      canonicalName: resp.canonicalName,
      overallSimilarity:
        resp.overallSimilarity as ApplicantNameConsistencyResult['overallSimilarity'],
      documents: (resp.documents ?? []).map((d) => ({
        documentId: d.documentId,
        bestMatchedName: getNameFromBestMatch(d.bestMatch),
        similarity: d.similarity as ApplicantNameConsistencyResult['overallSimilarity'],
      })),
    };
  }
}

/**
 * Constructs a complete name string from the best match details.
 * Analyzes the field types in the match and assembles them in typical name order.
 * Based on the FieldType enum from ers.proto, the possible name field types are:
 * - FULL_NAME (1)
 * - GIVEN_NAME (2)
 * - MIDDLE_NAME (3)
 * - FAMILY_NAME (4)
 * - NICKNAME (5)
 * - PREFIX (6)
 * - SUFFIX (7)
 *
 * @param bestMatch - The match object containing name field details
 * @returns A formatted complete name string, or empty string if no match
 */
function getNameFromBestMatch(bestMatch: Match | null): string {
  if (!bestMatch || !bestMatch.details || bestMatch.details.length === 0) {
    return '';
  }

  // Define name component field types and their display order
  const NAME_FIELD_ORDER: Record<string, { order: number; displayName: string }> = {
    // Full name - highest priority, return immediately if present
    FULL_NAME: { order: 0, displayName: 'full name' },
    FULLNAME: { order: 0, displayName: 'full name' },

    // Prefix/Title
    PREFIX: { order: 1, displayName: 'prefix' },

    // Given/First name
    GIVEN_NAME: { order: 2, displayName: 'given name' },
    FIRST_NAME: { order: 2, displayName: 'first name' },
    FIRSTNAME: { order: 2, displayName: 'first name' },
    NICKNAME: { order: 2, displayName: 'nickname' },

    // Middle name
    MIDDLE_NAME: { order: 3, displayName: 'middle name' },
    MIDDLENAME: { order: 3, displayName: 'middle name' },

    // Family/Last name
    FAMILY_NAME: { order: 4, displayName: 'family name' },
    LAST_NAME: { order: 4, displayName: 'last name' },
    LASTNAME: { order: 4, displayName: 'last name' },

    // Suffix
    SUFFIX: { order: 5, displayName: 'suffix' },
  } as const;

  // Use a Map to deduplicate by order (field type), keeping only the first occurrence
  // This prevents duplicate fields when entity-resolution returns multiple metrics for the same field
  const nameComponentsMap = new Map<number, { value: string; fieldName: string }>();

  for (const detail of bestMatch.details) {
    const fieldName = detail.fieldName?.toUpperCase().trim() || '';
    const matchedValue = detail.matched?.trim() || '';

    if (!matchedValue) continue;

    // Check if this field is a recognized name component
    const fieldConfig = NAME_FIELD_ORDER[fieldName];

    if (fieldConfig) {
      // If it's FULL_NAME (order 0), use it directly and skip other components
      if (fieldConfig.order === 0) {
        return matchedValue;
      }

      // Only add if we haven't seen this field type (order) yet
      // This deduplicates multiple entries for the same field with different metrics
      if (!nameComponentsMap.has(fieldConfig.order)) {
        nameComponentsMap.set(fieldConfig.order, {
          value: matchedValue,
          fieldName: fieldConfig.displayName,
        });
      }
    }
  }

  // Convert map to array and sort by order (the map keys)
  const nameComponents = Array.from(nameComponentsMap.entries())
    .sort(([orderA], [orderB]) => orderA - orderB)
    .map(([, component]) => component);

  return nameComponents
    .map((component) => component.value)
    .filter((value) => value.length > 0)
    .join(' ')
    .trim();
}
