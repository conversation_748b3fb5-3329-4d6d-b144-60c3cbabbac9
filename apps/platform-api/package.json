{"name": "@bybeam/platform-api", "version": "0.0.1", "private": true, "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "main": "dist/index.js", "type": "module", "scripts": {"build": "pnpm run codegen && tsc --build --force tsconfig.src.json && pnpm copy", "codegen": "pnpm run codegen:emit-ide-helpers && graphql-codegen --config codegen.yml", "codegen:emit-ide-helpers": "node ./scripts/emit-scalars-sdl.cjs && node ./scripts/emit-constraint-directive-sdl.mjs", "codegen:watch": "graphql-codegen --config codegen.yml --watch", "codegen:ci-check": "bash ./scripts/codegen-ci-check.sh", "clean": "rm -rf dist", "copy": "copyfiles -u 1 src/**/*.graphql src/**/*.pdf  src/**/*.otf dist/", "debug": "tsc-watch --build tsconfig.src.json --onSuccess 'node --inspect -r ts-node dist/index.js'", "start": "node ./dist/index.js", "start:serve": "pnpm copy && node ./dist/index.js", "start:dev": "pnpm run codegen:watch & tsc-watch --build tsconfig.src.json --onSuccess 'pnpm run start:serve'", "test": "vitest"}, "dependencies": {"@apollo/server": "^4.11.0", "@bybeam/config-client": "workspace:^", "@bybeam/doctopus-entities": "workspace:*", "@bybeam/doctopus-types": "workspace:*", "@bybeam/entity-resolution": "workspace:*", "@bybeam/formatting": "workspace:^", "@bybeam/identity-client": "workspace:^", "@bybeam/infrastructure-lib": "workspace:*", "@bybeam/linking-client": "workspace:^", "@bybeam/math-lib": "workspace:*", "@bybeam/notification-client": "workspace:^", "@bybeam/platform-entities": "workspace:*", "@bybeam/platform-lib": "workspace:*", "@bybeam/platform-types": "workspace:*", "@bybeam/scoring": "workspace:*", "@bybeam/verification-client": "workspace:*", "@bybeam/verification-types": "workspace:*", "@elastic/elasticsearch": "^8.17.1", "@google-cloud/bigquery": "^8.1.1", "@google-cloud/modelarmor": "^0.4.1", "@google-cloud/pubsub": "4.3.3", "@google-cloud/recaptcha-enterprise": "5.1.0", "@google-cloud/storage": "7.17.3", "@graphql-tools/load-files": "7.0.1", "@graphql-tools/merge": "9.1.1", "@graphql-tools/resolvers-composition": "7.0.20", "@graphql-tools/schema": "10.0.25", "@graphql-tools/utils": "10.9.1", "@grpc/grpc-js": "1.10.10", "@pdf-lib/fontkit": "1.1.1", "argon2": "0.44.0", "axios": "1.12.2", "bcrypt": "5.1.1", "cors": "2.8.5", "csv-parse": "5.5.3", "dataloader": "2.2.3", "dayjs": "1.11.19", "dotenv": "16.4.5", "express": "4.21.2", "express-winston": "4.2.0", "flat": "5.0.2", "graphql": "16.11.0", "graphql-constraint-directive": "5.4.3", "graphql-depth-limit": "1.1.0", "graphql-scalars": "1.23.0", "graphql-upload": "16.0.2", "heic-convert": "^2.1.0", "http-status-codes": "2.3.0", "isomorphic-dompurify": "^2.10.0", "jsonwebtoken": "9.0.2", "mnemonist": "0.39.6", "multer": "2.0.2", "pdf-lib": "1.17.1", "pg": "8.13.1", "posthog-node": "5.9.1", "reflect-metadata": "0.1.14", "sanitize-filename": "^1.6.3", "source-map-support": "0.5.21", "temp": "0.9.4", "typeorm": "catalog:default", "uuid": "9.0.1"}, "devDependencies": {"@bybeam/common-proto": "workspace:^", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/typescript": "^4.1.0", "@graphql-codegen/typescript-resolvers": "^4.3.1", "@parcel/watcher": "^2.5.1", "@tsconfig/node20": "20.1.6", "@types/bcrypt": "5.0.2", "@types/cors": "2.8.19", "@types/express": "4.17.21", "@types/flat": "5.0.5", "@types/graphql-depth-limit": "1.1.6", "@types/graphql-upload": "16.0.5", "@types/heic-convert": "^1.2.3", "@types/jsonwebtoken": "9.0.10", "@types/multer": "^1.4.12", "@types/node": "catalog:default", "@types/pg": "^8.15.5", "@types/temp": "0.9.4", "@types/uuid": "catalog:default", "@vitest/coverage-v8": "catalog:default", "copyfiles": "2.4.1", "mockdate": "catalog:default", "ts-node": "catalog:default", "tsc-watch": "7.2.0", "typescript": "catalog:default", "vite-tsconfig-paths": "5.0.1", "vitest": "catalog:default"}}