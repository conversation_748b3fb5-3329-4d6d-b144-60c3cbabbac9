{"name": "@bybeam/scheduler-server", "version": "0.0.1", "description": "nestjs gRPC server for scheduler", "private": true, "type": "module", "imports": {"#generated": "./prisma/generated/client/index.js", "#generated/*": "./prisma/generated/client/*"}, "scripts": {"build:prod": "rm -rf dist && tsc --project tsconfig.build.json", "nest": "nest", "dbpull": "prisma db pull && prisma-case-format -f ./prisma/schema.prisma -c ./prisma/prisma-case-format.yaml", "generate": "prisma generate", "refresh": "pnpm dbpull && pnpm generate", "start": "nest start", "start:dev": "pnpm refresh && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "vitest"}, "devDependencies": {"@bybeam/typescript-config": "workspace:^", "@nestjs/cli": "^10.3.2", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@types/node": "catalog:default", "@vitest/coverage-v8": "catalog:default", "mockdate": "catalog:default", "source-map-support": "0.5.21", "ts-loader": "^9.5.1", "ts-node": "catalog:default", "tsconfig-paths": "catalog:default", "typescript": "catalog:default", "unplugin-swc": "^1.5.8", "vitest": "catalog:default"}, "dependencies": {"@bybeam/infrastructure-lib": "workspace:*", "@bybeam/math-lib": "workspace:*", "@bybeam/platform-types": "workspace:*", "@bybeam/scheduler-client": "workspace:*", "@google-cloud/pubsub": "4.3.3", "@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "@nestjs/common": "^11.0.12", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.5", "@nestjs/microservices": "^10.4.6", "@nestjs/terminus": "^11.0.0", "@prisma/client": "5.17.0", "dayjs": "1.11.19", "grpc-health-check": "^2.0.0", "nestjs-grpc-reflection": "^0.2.2", "nestjs-pino": "4.0.0", "prisma": "5.17.0", "prisma-case-format": "^2.2.1"}}