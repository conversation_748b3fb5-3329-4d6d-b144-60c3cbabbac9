// This file has been automatically migrated to valid ESM format by Storybook.
import { createRequire } from 'node:module';
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import type { StorybookConfig } from '@storybook/nextjs-vite';
import graphqlLoader from 'vite-plugin-graphql-loader';

const require = createRequire(import.meta.url);
const __dirname = dirname(fileURLToPath(import.meta.url));

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string) {
  return dirname(require.resolve(join(value, 'package.json')));
}

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],

  addons: [
    getAbsolutePath('@storybook/addon-onboarding'),
    getAbsolutePath('@storybook/addon-links'),
    getAbsolutePath('@chromatic-com/storybook'),
    getAbsolutePath('storybook-addon-apollo-client'),
    getAbsolutePath('@storybook/addon-docs'),
    getAbsolutePath('@storybook/addon-vitest'),
  ],

  framework: {
    name: getAbsolutePath('@storybook/nextjs-vite'),
    options: {},
  },

  staticDirs: [
    '../public',
    {
      from: '../public/assets/fonts',
      to: '/partner/assets/fonts', // not sure how/why we're serving these under the `partner` path, but it's consistent with how they're being served in the app (in local dev at least)
    },
  ],

  // biome-ignore lint/suspicious/noExplicitAny: Storybook passes a Vite InlineConfig here
  viteFinal: async (config: any) => {
    config.resolve = config.resolve || {};
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      '@/spa-legacy': join(__dirname, '../src/spa-legacy'),
      buffer: 'buffer',
    };

    // Ensure GraphQL loader runs first so #import is resolved in Storybook builds
    config.plugins = [graphqlLoader(), ...(config.plugins || [])];

    // Exclude Next.js specific modules from Vite optimization
    config.optimizeDeps = config.optimizeDeps || {};
    config.optimizeDeps.exclude = [...(config.optimizeDeps.exclude || []), 'next/config'];

    return config;
  },

  typescript: {
    reactDocgen: 'react-docgen-typescript',
  },
};
export default config;
