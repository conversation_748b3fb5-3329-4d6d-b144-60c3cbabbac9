import isAccountNumberValid from '@/app/_utils/accountNumber';
import type { BankAccount, Nullable } from '@bybeam/platform-types';

export const hasBankInfoChanged = (form: BankAccount, bankAccount: BankAccount): boolean =>
  form.accountType !== bankAccount?.accountType ||
  form.routingNumber !== (bankAccount?.routingNumber ?? '') ||
  form.accountNumber !== (bankAccount?.accountNumber ?? '');

export const doesAccountNumberNeedUpdate = (form: BankAccount, bankAccount: BankAccount): boolean =>
  (form.accountType !== bankAccount?.accountType ||
    form.routingNumber !== (bankAccount?.routingNumber ?? '')) &&
  form.accountNumber === (bankAccount?.accountNumber ?? '');

export const validateAccountNumber = (
  // biome-ignore lint/suspicious/noExplicitAny: difficult casting on enum
  form: Record<keyof BankAccount, any>,
  bankAccount: BankAccount,
): Nullable<string> => {
  if (doesAccountNumberNeedUpdate(form, bankAccount)) return 'Please re-enter the account number';
  return form.accountNumber === bankAccount?.accountNumber
    ? undefined
    : isAccountNumberValid(form.accountNumber);
};
