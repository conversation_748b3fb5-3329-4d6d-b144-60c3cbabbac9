import { hasApplicantType } from '@/app/_utils/programs';
import AnswerReview from '@/spa-legacy/common/components/AnswerReview';
import type { FieldAnswer } from '@/spa-legacy/common/components/AnswerReview/types';
import { getField } from '@/spa-legacy/utilities/application/config';
import { getApplicationConfiguration } from '@/spa-legacy/utilities/application/configs';
import { getFieldAnswersValue } from '@/spa-legacy/utilities/application/fields';
import {
  type ApplicantProfileConfig,
  FieldType,
  type Nullable,
  type ProfileKey,
  type Program,
  type User,
} from '@bybeam/platform-types';

export const getPartnerConfig = (
  config: ApplicantProfileConfig,
  searchKey: string,
): Nullable<ProfileKey> => config?.profileKeys?.find((record) => record.key === searchKey);

export const getPartnerConfigLabel = (
  config: ApplicantProfileConfig,
  searchKey: string,
): Nullable<string> => getPartnerConfig(config, searchKey)?.label;

export const getFormattedProfileAnswer = (
  user: User,
  partnerConfig: ApplicantProfileConfig,
  programs: Program[],
  searchKey: string,
) => {
  const { applicantProfile: profile } = user;
  const config = getPartnerConfig(partnerConfig, searchKey);
  const field = programs
    .filter((program) => hasApplicantType(program, user))
    .map((program) => getApplicationConfiguration(program, user))
    .map((applicationConfig) => getField(applicationConfig, searchKey))
    .find(Boolean);

  let answer: FieldAnswer;
  if (field?.type === FieldType.Address)
    answer = getFieldAnswersValue<string>(`${field.key}Id`, profile.answers);
  else answer = getFieldAnswersValue<FieldAnswer>(searchKey, profile.answers);

  if (!answer || !field || !field?.type || !config) return null;

  return <AnswerReview source={profile} field={field} answer={answer} defaultValue="-" />;
};
