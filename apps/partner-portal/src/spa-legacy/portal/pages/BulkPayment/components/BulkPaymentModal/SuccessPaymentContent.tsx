import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import Message from '@/spa-legacy/common/components/Message';
import pluralize from '@bybeam/platform-lib/format/pluralize';
import { Button, Flex } from '@radix-ui/themes';
import Link from 'next/link';
import { BulkActionStates } from '.';
import { useBulkPayment } from '../../hooks/useBulkPayment';
import { getAmountPerPayee } from '../../utils/bulkPayments';
import PaymentDetailList from './PaymentDetailList';

export default function SuccessPaymentContent(): JSX.Element {
  const { selectedCaseIds, bulkParams, casesHaveSameApprovedAmount, program } = useBulkPayment();
  const numberOfPayees = selectedCaseIds?.length;
  const paymentWording = pluralize('payment', numberOfPayees);
  return (
    <>
      <Flex direction="column" gap="6" my="6" mb="6" maxWidth="fit-content">
        <PaymentDetailList
          fund={bulkParams?.fund.name}
          {...(casesHaveSameApprovedAmount && {
            amount: getAmountPerPayee(bulkParams?.approvedAmount, selectedCaseIds?.length),
          })}
          count={numberOfPayees}
          bulkActionState={BulkActionStates.Success}
        />
        <Message variant="success">
          <strong>{numberOfPayees}</strong> {paymentWording} successfully initiated. Applicants have
          been notified of the application status change.
        </Message>
      </Flex>
      <Flex>
        <Button asChild variant="outline">
          <Link href={makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, { programId: program.id })}>
            Close
          </Link>
        </Button>
        <Button asChild data-cy="bulk-success-view-program-overview">
          <Link href={makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, { programId: program.id })}>
            View Program Overview
          </Link>
        </Button>
      </Flex>
    </>
  );
}
