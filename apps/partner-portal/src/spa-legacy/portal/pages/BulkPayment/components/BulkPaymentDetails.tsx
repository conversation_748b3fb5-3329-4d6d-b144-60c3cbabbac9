import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import { programHasPayments } from '@/app/_utils/programs';
import { useUserHomeRoute } from '@/app/hooks/useUserHomeRoute';
import Message from '@/spa-legacy/common/components/Message';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import pluralize from '@bybeam/platform-lib/format/pluralize';
import { FeatureName } from '@bybeam/platform-types';
import { Button } from '@radix-ui/themes';
import { Route } from 'next';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useBulkPayment } from '../hooks/useBulkPayment';
import BulkPaymentModal from './BulkPaymentModal';
import PayeeTable from './PayeeTable';
import RequestSummarySection from './RequestSummarySection';

export default function BulkPaymentDetails() {
  const [openModal, setOpenModal] = useState(false);
  const {
    program,
    bulkParams,
    bulkErrors,
    casesHaveFulfillmentAndSameFund,
    bulkPaymentInitiated,
    selectedCases,
  } = useBulkPayment();
  const defaultRoute = useUserHomeRoute();
  const router = useRouter();
  useEffect(() => {
    if (!program || !programHasPayments(program)) {
      router.replace(defaultRoute as Route);
    }
  }, [program, defaultRoute, router]);

  if (!program || !programHasPayments(program)) return null;

  const closeModal = (): void => {
    setOpenModal(false);
    if (bulkPaymentInitiated && program) {
      // @ts-expect-error experimental typedRoutes checks fail
      router.push(makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, { programId: program.id }));
    }
  };
  const { ids, ...errors } = bulkErrors ?? {};
  const payeeWording = pluralize('payee', ids.length);

  const canInitiate =
    !!bulkParams &&
    selectedCases.length > 0 &&
    casesHaveFulfillmentAndSameFund &&
    Object.values(errors).every((value) => !value);

  if (!checkFeature(program?.features, FeatureName.PaymentsClaimFunds)) return null;
  return (
    <>
      <Button onClick={() => setOpenModal(true)} disabled={!canInitiate} m="4" ml="auto">
        Initiate Bulk Payment
      </Button>
      {bulkErrors.apiError && (
        <div className="max-w-fit mb-6">
          <Message variant="error">
            Something went wrong while processing your request. Please try again.
          </Message>
        </div>
      )}
      <div className="flex flex-col w-full gap-6 px-6">
        <RequestSummarySection />
        {bulkErrors.incorrectStatus && (
          <Message variant="error">
            The {payeeWording} you’ve selected cannot have a bulk action applied to them because
            they’re in the incorrect case status. Please change their status or remove{' '}
            {payeeWording} from this bulk action.
          </Message>
        )}
        {bulkErrors.paymentAlreadyInitiated && (
          <Message variant="error">
            {ids.length} {payeeWording} you’ve selected cannot have a bulk action applied to them
            because a payment has already been sent. Please remove the {payeeWording} from this bulk
            action.
          </Message>
        )}
        {bulkErrors.tooManyPayees && (
          <Message variant="error">
            The number of payees selected exceed the maximum of 50 at one time for a bulk action.
            Please remove extra payees.
          </Message>
        )}
        {bulkErrors.noApplicantWithType && (
          <Message variant="error">
            {ids.length} {payeeWording} you’ve selected do not have any case participant(s) of the
            selected Payee Type. Please remove the {payeeWording} from this bulk action.
          </Message>
        )}
        {bulkErrors.multipleApplicantsWithType && (
          <Message variant="error">
            {ids.length} {payeeWording} you’ve selected has multiple case participants of the
            selected Payee Type. Please select the appropriate payee on the case page, or remove the{' '}
            {payeeWording} from this bulk action.
          </Message>
        )}
        {bulkErrors.submitError && !!ids?.length && (
          <Message variant="error">
            Required details are missing for {ids.length} {payeeWording}. For rows with an error,
            add/edit payee details or remove {payeeWording} from this bulk payment
          </Message>
        )}
        <PayeeTable />
      </div>
      {openModal && <BulkPaymentModal onClose={closeModal} />}
    </>
  );
}
