// TODO: EditVendor should not be a page component if it is being used on multiple pages
import EditVendor from '@/app/[partnerId]/(authenticated)/vendors/[vendorId]/_components/EditVendor';
import GetPayeeQuery from '@/spa-legacy/graphql/queries/GetPayeeQuery.graphql';
import EditApplicantModal from '@/spa-legacy/portal/components/EditApplicantModal';
import { useQuery } from '@apollo/client';
import { checkFeatures } from '@bybeam/platform-lib/features/check';
import { type Case, FeatureName, PayeeType, type Program } from '@bybeam/platform-types';

export default function EditPayeeModal({
  case: {
    applications: [application],
    fulfillments: [fulfillment],
  },
  program,
  closeModal,
}: { case: Case; program: Program; closeModal: () => void }): JSX.Element {
  const payee = fulfillment
    ? fulfillment.payments[0].payee
    : { id: application.submitter.id, payeeType: PayeeType.User };
  const { data } = useQuery(GetPayeeQuery, {
    variables: {
      payeeId: payee.id,
      isApplicant: payee.payeeType === PayeeType.User,
      includeBankAccount: checkFeatures(program?.features, [
        FeatureName.PaymentsPartnerIssued,
        FeatureName.PaymentsDirectDeposit,
      ]),
    },
  });

  // TODO: Loading/error state
  if (!data) return null;

  return payee.payeeType === PayeeType.User ? (
    <EditApplicantModal
      applicant={data.users.users[0]}
      onClose={closeModal}
      isModalOpen={true}
      dialogTrigger={<span />}
    />
  ) : (
    <EditVendor onCloseModal={closeModal} {...data.vendors.vendors[0]} />
  );
}
