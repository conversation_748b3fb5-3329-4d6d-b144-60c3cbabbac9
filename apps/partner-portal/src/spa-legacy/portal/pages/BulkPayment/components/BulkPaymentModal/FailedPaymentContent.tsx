import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import Message from '@/spa-legacy/common/components/Message';
import { Button, Flex } from '@radix-ui/themes';
import Link from 'next/link';
import { BulkActionStates, type BulkPaymentModalProps } from '.';
import { useBulkPayment } from '../../hooks/useBulkPayment';
import { getAmountPerPayee } from '../../utils/bulkPayments';
import PaymentDetailList from './PaymentDetailList';

export default function FailedPaymentContent({ onClose }: BulkPaymentModalProps): JSX.Element {
  const { selectedCaseIds, bulkParams, casesHaveSameApprovedAmount, program } = useBulkPayment();
  const numberOfPayees = selectedCaseIds?.length;

  return (
    <>
      <Flex direction="column" gap="6" my="6" mb="6">
        <PaymentDetailList
          fund={bulkParams?.fund.name}
          count={numberOfPayees}
          {...(casesHaveSameApprovedAmount && {
            amount: getAmountPerPayee(bulkParams?.approvedAmount, selectedCaseIds?.length),
          })}
          bulkActionState={BulkActionStates.Failed}
        />
        <Message variant="error">
          Your request to bulk pay {numberOfPayees} payees failed. Please review the payee(s) errors
          and try again.
        </Message>
      </Flex>
      <Flex gap="3" mt="4" justify="end">
        <Button asChild variant="outline">
          <Link href={makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, { programId: program.id })}>
            View Program Overview
          </Link>
        </Button>
        <Button type="submit" onClick={onClose}>
          Review Payee(s) Errors
        </Button>
      </Flex>
    </>
  );
}
