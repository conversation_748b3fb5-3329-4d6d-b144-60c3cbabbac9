import { VerificationStatus } from '@/app/components/features/Verification';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import useForm, { fieldUpdate } from '@/app/hooks/useForm';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { useValidateAddresses } from '@/spa-legacy/common/utilities/address';
import { DateFormat, formatDate } from '@/spa-legacy/common/utilities/format';
import AdminName from '@/spa-legacy/portal/components/AdminName';
import ApplicationTitle from '@/spa-legacy/portal/components/ApplicationTitle';
import { getApplicationConfiguration } from '@/spa-legacy/utilities/application/configs';
import { SpecialSectionKeys } from '@/spa-legacy/utilities/application/configs/standards/types';
import {
  type Application,
  type ApplicationContentInput,
  type ApplicationVersion,
  type ApplicationSection as ConfigApplicationSection,
} from '@bybeam/platform-types';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Box, Button, Callout, Flex, Heading, HoverCard, Text, TextArea } from '@radix-ui/themes';
import type { Route } from 'next';
import { useRouter, useSearchParams } from 'next/navigation';
import { useContext, useEffect, useMemo, useState } from 'react';
import ManualAddressEntryModeProvider, { ManualAddressEntryModeContext } from '../context';
import useSubmitApplication, { canSubmitApplication } from '../hooks/useSubmitApplication';
import type { UseVersionStatusResponse } from '../hooks/useVersionStatus';
import useAddVersion from '../useAddVersion';
import ApplicationActions from './ApplicationActions';
import styles from './ApplicationReviewForm.module.css';
import ApplicationSection from './ApplicationSection';

interface ApplicationReviewFormProps {
  application: Application;
  versionState: {
    states: UseVersionStatusResponse;
    backRoute: string;
  };
}

function ApplicationReviewFormInner({
  application,
  versionState,
}: ApplicationReviewFormProps): React.JSX.Element {
  const program = useProgram();
  if (!program) return <LoadingComponent />;

  const config = getApplicationConfiguration(program, application.submitter);
  const latestVersion = application.versions?.[0];
  if (!latestVersion) return <LoadingComponent />;

  const [selectedVersion, setSelectedVersion] = useState<ApplicationVersion>(latestVersion);
  const [description, setDescription] = useState(selectedVersion.description ?? '');
  const { addVersion, loading } = useAddVersion(application.id);
  const { submitApplication, loading: submitLoading } = useSubmitApplication();
  const { loading: geocoderLoading, validateAddresses } = useValidateAddresses();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { manualAddressEntryMode } = useContext(ManualAddressEntryModeContext);

  const {
    states: { mode, isEditing, setIsEditing, editingSections, setEditingSections },
    backRoute,
  } = versionState;

  const { dispatch, formData } = useForm({
    update: {
      answers: selectedVersion.answers,
      addresses: [],
      documents: [],
    } as ApplicationContentInput,
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: should trigger on isEditing change
  useEffect((): void => {
    dispatch(
      fieldUpdate('update', {
        answers: selectedVersion.answers,
        addresses: [],
        documents: [],
      }),
    );
  }, [dispatch, selectedVersion, isEditing]);

  const changeEditStatus = (section: string): void => {
    if (!editingSections.includes(section)) {
      setEditingSections?.([...editingSections, section]);
      return;
    }
    setEditingSections?.(editingSections.filter((sectionName) => section !== sectionName));
  };

  useEffect(() => {
    if (latestVersion) {
      setSelectedVersion(latestVersion);
    }
  }, [latestVersion]);

  useEffect(() => {
    setDescription(isEditing ? '' : (selectedVersion.description ?? ''));
  }, [selectedVersion, isEditing]);

  const add = (): void => {
    setIsEditing(true);
    setSelectedVersion(latestVersion);
  };

  const save = async (): Promise<void> => {
    const validatedAddresses = await validateAddresses(formData.update.addresses, {
      manualOverride: manualAddressEntryMode,
    });
    const versionId = mode === 'new' ? selectedVersion.id : undefined;

    setIsEditing(false);

    const savedApplication = await addVersion(
      { ...formData.update, addresses: validatedAddresses },
      description,
      versionId,
    );

    if (!savedApplication) {
      setIsEditing(true);
      return;
    }

    const canSubmit = canSubmitApplication(savedApplication?.answers, config, savedApplication);
    let submitted = undefined;
    if (canSubmit) {
      submitted = await submitApplication(application);
    }

    if (savedApplication && (submitted || !canSubmit)) {
      if (searchParams.get('mode')) router.back();
    }
  };

  const cancel = (): void => {
    if (mode === 'new') {
      router.push(backRoute as Route);
      return;
    }
    setIsEditing(false);
    setEditingSections?.([]);
  };

  const isNewApplication = mode === 'new';

  const sections = useMemo(
    (): ConfigApplicationSection[] =>
      config.sections.filter(
        (section) => mode === 'edit' || section.key !== SpecialSectionKeys.AdminOnly,
      ),
    [config.sections, mode],
  );

  if (loading || geocoderLoading || submitLoading) return <LoadingComponent />;
  // {/* TODO: Bring back prompt for unsaved changes in the Next.js way */}

  return (
    <Flex direction="column">
      <Flex
        px="4"
        py="2"
        width="100%"
        className={`${styles.header} ${styles.headerSticky}`}
        justify="center"
      >
        <Flex maxWidth="1200px" justify="between" width="100%" wrap="wrap" gap="2" align="center">
          <Box>
            <Flex align="center" gap="2">
              {application.case && (
                <ApplicationTitle
                  variant="h1"
                  name={application.submitter.name}
                  priority={application.case.priority}
                />
              )}
              {application?.verification && (
                <VerificationStatus verification={application.verification} showDialog={true} />
              )}
              {isEditing ? (
                <Callout.Root color="blue" highContrast mt="2">
                  <Callout.Icon>
                    <InfoCircledIcon />
                  </Callout.Icon>
                  <Callout.Text>
                    {isNewApplication
                      ? 'You are now creating a new application'
                      : 'You are now editing a new version'}
                  </Callout.Text>
                </Callout.Root>
              ) : null}
            </Flex>

            {selectedVersion?.updatedAt && !isEditing && selectedVersion.creator?.admin && (
              <Flex direction="column" gap="1">
                <Flex gap="1" align="center">
                  <Text size="2" highContrast weight="medium">
                    Application {application.displayId} updated{' '}
                    {formatDate(selectedVersion.updatedAt, DateFormat.Slashes)} by
                  </Text>
                  <AdminName
                    admin={selectedVersion.creator.admin}
                    format={() => selectedVersion.creator.name}
                    variant="label"
                  />
                  {selectedVersion.description && (
                    <HoverCard.Root>
                      <HoverCard.Trigger>
                        <Button size="1" variant="ghost" ml="2">
                          Version Notes
                        </Button>
                      </HoverCard.Trigger>
                      <HoverCard.Content
                        maxWidth="320px"
                        maxHeight="240px"
                        className={styles.versionNotesContent}
                      >
                        <Flex direction="column" gap="2">
                          <Heading size="2">Version Notes</Heading>
                          <Text size="2">{selectedVersion.description}</Text>
                        </Flex>
                      </HoverCard.Content>
                    </HoverCard.Root>
                  )}
                </Flex>
              </Flex>
            )}
          </Box>
          <Flex direction="column" gap="2" align="end">
            <ApplicationActions
              save={save}
              add={add}
              cancel={cancel}
              select={(version: ApplicationVersion): void => setSelectedVersion(version)}
              versions={application.versions ?? []}
              selectedVersion={selectedVersion}
              editing={isEditing}
              saveDisabled={!!editingSections.length}
              mode={mode}
            />
          </Flex>
        </Flex>
      </Flex>
      <Flex px="4" py="4" justify="center" width="100%" className={styles.contentScroller}>
        <Flex
          direction="column"
          gap="2"
          width="100%"
          maxWidth="1200px"
          className={styles.sectionsWrapper}
        >
          {isEditing ? (
            <Flex direction="column" gap="1" width="18rem">
              <Text as="label" size="2" weight="medium" htmlFor="description">
                Provide reasons for changes here.
              </Text>
              <TextArea
                id="description"
                value={description ?? ''}
                onChange={(e): void => setDescription(e.target.value)}
                rows={2}
                readOnly={!isEditing}
              />
            </Flex>
          ) : (
            <Flex direction="column" gap="1" maxWidth="48rem">
              <Heading size="3">Version Notes</Heading>
              <Text size="2">{description || 'No version notes provided.'}</Text>
            </Flex>
          )}
          {sections.map((section) => {
            return (
              <ApplicationSection
                key={section.key}
                editable={isEditing}
                answers={selectedVersion.answers}
                section={section}
                mode={mode}
                dispatchToVersion={dispatch}
                changeEditStatus={changeEditStatus}
                editing={editingSections.includes(section.key)}
                update={formData.update}
                application={application}
              />
            );
          })}
        </Flex>
      </Flex>
    </Flex>
  );
}

export default function ApplicationReviewForm(
  props: ApplicationReviewFormProps,
): React.JSX.Element {
  return (
    <ManualAddressEntryModeProvider>
      <ApplicationReviewFormInner {...props} />
    </ManualAddressEntryModeProvider>
  );
}
