import CanAccess from '@/app/components/features/CanAccess';
import { DateFormat, formatDate } from '@/spa-legacy/common/utilities/format';
import { formatLastInitial } from '@/spa-legacy/portal/utils/applications';
import type { ApplicationMode } from '@/spa-legacy/utilities/application/question';
import type { ApplicationVersion } from '@bybeam/platform-types';
import { Button, Flex, Select, Text } from '@radix-ui/themes';
import React from 'react';

interface ApplicationActionProps {
  save: () => Promise<void>;
  add: () => void;
  cancel: (e?: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>) => void;
  select: (value: ApplicationVersion) => void;
  versions: ApplicationVersion[];
  selectedVersion: ApplicationVersion;
  editing: boolean;
  saveDisabled: boolean;
  mode: ApplicationMode;
}

export default function ApplicationActions({
  save,
  add,
  cancel,
  select,
  versions,
  selectedVersion,
  editing,
  saveDisabled,
  mode,
}: ApplicationActionProps): React.JSX.Element {
  const isNewApplication = mode === 'new';

  const handleSelectVersion = (versionId: string) => {
    const version = versions.find((v) => v.id === versionId);
    if (version) {
      select(version);
    }
  };

  return (
    <Flex direction="column">
      {!editing && (
        <Flex gap="4">
          {versions.length > 1 && (
            <Select.Root
              value={selectedVersion.id}
              onValueChange={handleSelectVersion}
              aria-label="Select Previous Version"
            >
              <Select.Trigger placeholder="Previous Versions" variant="surface" />
              <Select.Content position="popper">
                {versions.map((version) => (
                  <Select.Item key={version.id} value={version.id}>
                    <Flex gap="2" align="center">
                      <Text>{formatDate(version.updatedAt, DateFormat.Slashes)}</Text>
                      <Text>|</Text>
                      <Text>
                        {version.creator?.admin
                          ? formatLastInitial(version.creator.name)
                          : version.creator.name}
                      </Text>
                    </Flex>
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
          )}
          <CanAccess>
            <Button onClick={add}>Add New Version</Button>
          </CanAccess>
        </Flex>
      )}
      {editing && (
        <Flex direction="row" width="100%" align="center" justify="between">
          <Flex gap="4" align="center">
            <Button variant="ghost" onClick={cancel}>
              Cancel
            </Button>
            <Button onClick={save} disabled={saveDisabled}>
              {isNewApplication ? 'Save as new application' : 'Save as new version'}
            </Button>
          </Flex>
        </Flex>
      )}
    </Flex>
  );
}
