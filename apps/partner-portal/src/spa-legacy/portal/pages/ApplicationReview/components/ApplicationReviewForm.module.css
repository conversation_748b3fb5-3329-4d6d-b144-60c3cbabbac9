.header {
  border-bottom: 1px solid var(--gray-6);
  background-color: var(--color-background);
  flex-shrink: 0; /* Prevent header from shrinking */
}

.headerSticky {
  position: sticky;
  top: 0;
  z-index: 10;
}

.contentScroller {
  /* No overflow - let Page.Content handle scrolling */
  flex: 1;
}

.sectionsWrapper::after {
  content: "";
  display: block;
  /* Creates invisible scroll space so last section can reach top */
  height: 20vh;
}

.versionNotesContent {
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}
