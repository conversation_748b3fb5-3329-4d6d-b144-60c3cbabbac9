'use client';
import { makeRoute } from '@/app/Routes';
import { PORTAL_ROUTES } from '@/app/Routes';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import Page from '@/app/components/ui/Page/Page';
import { useUserHomeRoute } from '@/app/hooks/useUserHomeRoute';
import { ApplicationContext } from '@/spa-legacy/common/hooks/contexts/useApplication';
import { ProgramContext } from '@/spa-legacy/common/hooks/contexts/useProgram';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { useQuery } from '@apollo/client';
import { type Application } from '@bybeam/platform-types';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import ApplicationReviewForm from './components/ApplicationReviewForm';
import GetApplicationQuery from './graphql/GetReviewApplicationQuery.graphql';
import useVersionStatus from './hooks/useVersionStatus';

export default function ApplicationReview(): JSX.Element {
  const { caseId, applicationId } = useParams<{
    caseId: string;
    applicationId: string;
  }>();
  const router = useRouter();
  const { showSnackbar } = useSnackbar();
  const { data, loading, error } = useQuery<
    { applications: { applications: Application[] } },
    { id: string }
  >(GetApplicationQuery, {
    variables: { id: applicationId },
  });
  const versionStatus = useVersionStatus();

  // TODO: Bring back unsaved changes prompt
  // useEffect(() => {
  //   const beforeUnloadListener = (event): void => {
  //     event.preventDefault();
  //     (event || window.event).returnValue = '';
  //   };

  //   if (versionStatus.isEditing) window.addEventListener('beforeunload', beforeUnloadListener);

  //   return (): void => window.removeEventListener('beforeunload', beforeUnloadListener);
  // }, [versionStatus.isEditing]);

  useEffect((): void => {
    if (error) showSnackbar('Something went wrong. Please refresh and try again.');
  }, [error, showSnackbar]);

  const defaultRoute = useUserHomeRoute();
  const application = data?.applications?.applications?.[0];

  useEffect(() => {
    if (!loading && !application) {
      router.replace(defaultRoute as Route);
    }
  }, [loading, application, defaultRoute, router]);

  if (loading) return <LoadingComponent />;
  if (!application) return null;

  const caseDetailRoute = makeRoute(PORTAL_ROUTES.CASE_REVIEW, {
    programId: application.case.program.id,
    caseId,
  });

  return (
    <ProgramContext.Provider value={application.case.program.id}>
      <ApplicationContext.Provider value={application}>
        <Page>
          <Page.Header>
            <Page.Breadcrumbs>
              <Page.Breadcrumbs.Crumb>
                <Link href={{ pathname: caseDetailRoute }}>Case {application.case?.displayId}</Link>
              </Page.Breadcrumbs.Crumb>
              <Page.Breadcrumbs.Crumb>{application.displayId}</Page.Breadcrumbs.Crumb>
            </Page.Breadcrumbs>
          </Page.Header>
          <Page.Content>
            <ApplicationReviewForm
              versionState={{
                states: versionStatus,
                backRoute: caseDetailRoute,
              }}
              application={application}
            />
          </Page.Content>
        </Page>
      </ApplicationContext.Provider>
    </ProgramContext.Provider>
  );
}
