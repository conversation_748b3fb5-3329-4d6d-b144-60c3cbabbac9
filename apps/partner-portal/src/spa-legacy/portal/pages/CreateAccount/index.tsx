import CreateAccount from '@/spa-legacy/common/components/CreateAccount';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { getAvailableApplicantTypes } from '@/spa-legacy/common/utilities/applicantType';
import useCreateUserApplication from './useCreateUserApplication';

export default function AdminCreateAccount(): JSX.Element {
  const program = useProgram();

  return (
    <CreateAccount
      applicantTypes={getAvailableApplicantTypes(program)}
      useHook={useCreateUserApplication}
    />
  );
}
