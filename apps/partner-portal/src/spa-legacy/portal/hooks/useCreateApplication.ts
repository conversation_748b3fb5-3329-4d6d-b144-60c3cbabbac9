import { makeRoute } from '@/app/Routes';
import { PORTAL_ROUTES } from '@/app/Routes';
import useErrorIndicator from '@/spa-legacy/common/hooks/useErrorIndicator';
import { useMutation } from '@apollo/client';
import type { Application, CreateApplicationInput, MutationResponse } from '@bybeam/platform-types';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import CreateApplicationMutation from '../graphql/mutations/CreateApplication.graphql';

interface CreateApplicationResponse {
  doMutation: (userId: string, programIds: string[]) => Promise<boolean>;
  loading: boolean;
  error?: string;
}

export default function useCreateApplication(): CreateApplicationResponse {
  const [createApplication, { data, loading, error: transportError }] = useMutation<
    { application: { create: MutationResponse<Application> } },
    { input: CreateApplicationInput }
  >(CreateApplicationMutation);
  const router = useRouter();
  const showErrorIndicator = useErrorIndicator();
  const doMutation = useCallback(
    async (userId: string, programIds: string[]) => {
      const responses = await Promise.all(
        programIds.map((programId) =>
          createApplication({ variables: { input: { programId, userId } } }),
        ),
      );

      const successfulResponses = responses.filter(
        ({
          data: {
            application: {
              create: {
                metadata: { status },
              },
            },
          },
        }) => status === 201,
      );

      const allSucceeded = successfulResponses.length === programIds.length;
      for (const [idx, response] of successfulResponses.entries()) {
        const {
          data: {
            application: {
              create: {
                record: {
                  id: applicationId,
                  case: { id: caseId },
                },
              },
            },
          },
        } = response;
        const pathname = makeRoute(PORTAL_ROUTES.APPLICATION_REVIEW_LEGACY, {
          caseId,
          applicationId,
        });
        const search = '?mode=new';

        if (idx === 0 && allSucceeded) {
          router.push(`${pathname}${search}` as __next_route_internal_types__.RouteImpl<string>);
        } else {
          // why was this trying to open a new tab if this failed?
          // openNewTab(`/${pathname}${search}`);
        }
      }

      if (!allSucceeded)
        showErrorIndicator(
          `${
            programIds.length === 1
              ? 'There was an error creating the application.'
              : 'There was an error creating some of the applications.'
          } Please refresh and try again.`,
        );
      return allSucceeded;
    },
    [createApplication, history, showErrorIndicator],
  );

  let error: string;
  if (transportError) error = 'Something went wrong!';
  else if (data?.application.create.metadata.errors)
    error = data?.application.create.metadata.errors[0];

  const memo = useMemo(() => ({ doMutation, loading, error }), [doMutation, loading, error]);
  return memo;
}
