import { renderHook } from '@testing-library/react';
import { usePathname, useRouter } from 'next/navigation';
import useCreateApplication from './useCreateApplication';

const useMutation = vi.fn();
vi.mock('@apollo/client', () => ({ useMutation: () => useMutation() }));

vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
  useRouter: vi.fn(),
  useSearchParams: vi.fn(),
}));

const useErrorIndicator = vi.fn();
vi.mock('@platform-ui-common/hooks/useErrorIndicator', () => ({
  default: () => useErrorIndicator(),
}));

const mockMakeRoute = (route, params) => {
  return Object.entries(params ?? {}).reduce(
    (path, [param, value]) => path.replace(`:${param}`, value),
    '/partner1/cases/:caseId/applications/:applicationId',
  );
};

// TODO: this doesn't seem right ... why are we mocking this here?
vi.mock('@/app/Routes', () => ({
  PORTAL_ROUTES: {
    APPLICATION_REVIEW_LEGACY: 'APPLICATION_REVIEW_LEGACY',
  },
  makeRoute: (route, params) => mockMakeRoute(route, params),
}));

describe('useCreateApplication', () => {
  afterEach(() => vi.resetAllMocks());

  const mockRouterPush = vi.fn();

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();

    (usePathname as vi.Mock).mockReturnValue('/test-path');
    (useRouter as vi.Mock).mockReturnValue({ push: mockRouterPush });
  });

  it('creates a new application and routes to it', async () => {
    const createApplicationFn = vi.fn().mockResolvedValueOnce({
      data: {
        application: {
          create: {
            metadata: { status: 201 },
            record: { id: 'mockAppId', case: { id: 'mockCaseId' } },
          },
        },
      },
    });
    useMutation.mockReturnValueOnce([createApplicationFn, {}]);

    const {
      result: {
        current: { doMutation },
      },
    } = renderHook(() => useCreateApplication());
    const success = await doMutation('mockUserId', ['mockProgramId']);

    expect(success).toBe(true);
    expect(createApplicationFn).toHaveBeenCalledTimes(1);
    expect(createApplicationFn).toHaveBeenCalledWith({
      variables: { input: { programId: 'mockProgramId', userId: 'mockUserId' } },
    });
    expect(mockRouterPush).toHaveBeenCalledWith(
      '/partner1/cases/mockCaseId/applications/mockAppId?mode=new',
    );
  });

  it('opens applications in new tabs if more than one is created', async () => {
    const createApplicationFn = vi
      .fn()
      .mockResolvedValueOnce({
        data: {
          application: {
            create: {
              metadata: { status: 201 },
              record: { id: 'mockApp1', case: { id: 'mockCase1' } },
            },
          },
        },
      })
      .mockResolvedValueOnce({
        data: {
          application: {
            create: {
              metadata: { status: 201 },
              record: { id: 'mockApp2', case: { id: 'mockCase2' } },
            },
          },
        },
      });
    useMutation.mockReturnValueOnce([createApplicationFn, {}]);

    const {
      result: {
        current: { doMutation },
      },
    } = renderHook(() => useCreateApplication());
    const success = await doMutation('mockUserId', ['mockProgram1', 'mockProgram2']);

    expect(success).toBe(true);
    expect(createApplicationFn).toHaveBeenCalledTimes(2);
    expect(createApplicationFn).toHaveBeenCalledWith({
      variables: { input: { programId: 'mockProgram1', userId: 'mockUserId' } },
    });
    expect(createApplicationFn).toHaveBeenCalledWith({
      variables: { input: { programId: 'mockProgram2', userId: 'mockUserId' } },
    });
    expect(mockRouterPush).toHaveBeenCalledTimes(1);
    expect(mockRouterPush).toHaveBeenCalledWith(
      '/partner1/cases/mockCase1/applications/mockApp1?mode=new',
    );
  });

  it('shows an error indicator and stays on the page if at least one application failed', async () => {
    const createApplicationFn = vi
      .fn()
      .mockResolvedValueOnce({
        data: {
          application: {
            create: {
              metadata: { status: 201 },
              record: { id: 'mockApp1', case: { id: 'mockCase1' } },
            },
          },
        },
      })
      .mockResolvedValueOnce({
        data: { application: { create: { metadata: { status: 400 } } } },
      });
    useMutation.mockReturnValueOnce([createApplicationFn, {}]);

    const showErrorIndicatorFn = vi.fn();
    useErrorIndicator.mockReturnValueOnce(showErrorIndicatorFn);

    const {
      result: {
        current: { doMutation },
      },
    } = renderHook(() => useCreateApplication());
    const success = await doMutation('mockUserId', ['mockProgram1', 'mockProgram2']);

    expect(success).toBe(false);
    expect(createApplicationFn).toHaveBeenCalledTimes(2);
    expect(createApplicationFn).toHaveBeenCalledWith({
      variables: { input: { programId: 'mockProgram1', userId: 'mockUserId' } },
    });
    expect(createApplicationFn).toHaveBeenCalledWith({
      variables: { input: { programId: 'mockProgram2', userId: 'mockUserId' } },
    });
    expect(mockRouterPush).toHaveBeenCalledTimes(0);
    expect(showErrorIndicatorFn).toHaveBeenCalledWith(
      'There was an error creating some of the applications. Please refresh and try again.',
    );
  });
});
