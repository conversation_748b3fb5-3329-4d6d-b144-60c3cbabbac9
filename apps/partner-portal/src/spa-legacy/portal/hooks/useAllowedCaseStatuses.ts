import { programHasPayments } from '@/app/_utils/programs';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { WORKFLOW_CASE_STATUS, getProgramWorkflow } from '@/spa-legacy/portal/utils/workflow';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { CaseStatus, FeatureName, type Program } from '@bybeam/platform-types';

function canTransitionStatus(program: Program, status: CaseStatus): boolean {
  const workflow = getProgramWorkflow(program);

  if (status === CaseStatus.InProgress) return false;

  if (workflow === FeatureName.WorkflowExtended)
    return ![CaseStatus.Denied, CaseStatus.Withdrawn].includes(status);

  if (!programHasPayments(program)) return status !== CaseStatus.PaymentSent;

  if (status === CaseStatus.PaymentSent)
    return !checkFeature(program?.features, FeatureName.PaymentsClaimFunds);

  // TODO: This check should really also included the Payment Sent status, but
  // <PERSON>TC is currently manually setting status = Payment Sent as a workaround when they
  // cut a check themselves.
  if (status === CaseStatus.Approved)
    return checkFeature(program?.features, FeatureName.PaymentsExternalTracking);

  return true;
}

export default function useAllowedCaseStatuses(): CaseStatus[] {
  const program = useProgram();
  return WORKFLOW_CASE_STATUS[getProgramWorkflow(program)].filter((status) =>
    canTransitionStatus(program, status),
  );
}
