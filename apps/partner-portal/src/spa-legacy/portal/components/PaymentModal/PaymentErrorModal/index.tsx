import { BEAM_SUPPORT_EMAIL } from '@/app/_utils/constants';
import Icon from '@/spa-legacy/common/components/Icon';
import Modal from '@/spa-legacy/common/components/Modal';
import Color from '@/spa-legacy/common/utilities/Color';
import { formatCurrency } from '@/spa-legacy/common/utilities/format';
import { Box, Button, Flex, Text } from '@radix-ui/themes';

interface PaymentErrorModalProps {
  amount: number;
  onClose: () => void;
}

export default function PaymentErrorModal({
  amount,
  onClose,
}: PaymentErrorModalProps): JSX.Element {
  return (
    <Modal size="S" onClickClose={onClose} isOpen title="Payment Initiation Failed">
      <Box my="2">
        <Flex direction="column" align="center" gap="4">
          <Icon color={Color.Error} type={'reportGmailErrorRed'} size="XXL" />
          <Flex direction="column" gap="4" py="2">
            <Text>
              Unfortunately, we were unable to initiate this payment. The award amount of{' '}
              <strong>{formatCurrency(amount, true)}</strong> has not been disbursed. We are hard at
              work to get you back up and running.
            </Text>
            <Text>
              Please contact us at{' '}
              <strong>
                <a target="_blank" rel="noreferrer" href={`mailto:${BEAM_SUPPORT_EMAIL}`}>
                  {BEAM_SUPPORT_EMAIL}
                </a>{' '}
              </strong>
              and we will work with you to resolve this issue as quickly as possible.
            </Text>
          </Flex>
          <Flex gap="3" mt="5" justify="end" width="100%">
            <Button onClick={onClose} variant="outline">
              Exit
            </Button>
            <Button onClick={onClose}>View Case Page</Button>
          </Flex>
        </Flex>
      </Box>
    </Modal>
  );
}
