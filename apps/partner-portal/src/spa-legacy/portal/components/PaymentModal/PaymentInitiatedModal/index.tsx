import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import Icon from '@/spa-legacy/common/components/Icon';
import Modal from '@/spa-legacy/common/components/Modal';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import Color from '@/spa-legacy/common/utilities/Color';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName } from '@bybeam/platform-types';
import { Box, Button, Flex, Heading } from '@radix-ui/themes';
import Link from 'next/link';
import type { PaymentModalProps } from '../utils/types';
import ClaimFundsContent from './ClaimFundsContent';
import PartnerIssuesContent from './PartnerIssuesContent';

export interface PaymentInitiatedProps extends PaymentModalProps {
  isOpen: boolean;
}

export default function PaymentInitiatedModal({
  case: case_,
  fulfillment,
  isOpen,
  onClose,
}: PaymentInitiatedProps): JSX.Element {
  const program = useProgram();
  const { payee } = fulfillment?.payments?.[0] ?? {};

  return (
    <Modal size="S" onClickClose={onClose} isOpen={isOpen} title="Payment Initiated">
      <Box mt="2" mb="6">
        <Flex justify="center" mb="4">
          <Icon type="checkCircle" color={Color.Primary} size="XXL" />
        </Flex>
        <Heading as="h3" size="6">
          {payee?.name ?? ''}
        </Heading>
      </Box>
      <Flex width="100%">
        {checkFeature(program?.features, FeatureName.PaymentsClaimFunds) && (
          <ClaimFundsContent case={case_} fulfillment={fulfillment} />
        )}
        {checkFeature(program?.features, FeatureName.PaymentsPartnerIssued) && (
          <PartnerIssuesContent case={case_} fulfillment={fulfillment} />
        )}
      </Flex>
      <Flex gap="3" mt="4" justify="end">
        <Button type="button" onClick={onClose} variant="outline">
          Close
        </Button>
        <Button asChild>
          <Link href={makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, { programId: program.id })}>
            View Program Overview
          </Link>
        </Button>
      </Flex>
    </Modal>
  );
}
