import useAdmins from '@/app/hooks/useAdmins';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { useQuery } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName, type Partner, type Program } from '@bybeam/platform-types';
import { FilterList } from '@mui/icons-material';
import { Autocomplete, TextField } from '@mui/material';
import * as Accordion from '@radix-ui/react-accordion';
import { ChevronRightIcon } from '@radix-ui/react-icons';
import { Box, Button, Checkbox, Flex, Inset, Popover, RadioGroup, Text } from '@radix-ui/themes';
import { useParams } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import React, { type SyntheticEvent, useEffect, useMemo, useRef, useState } from 'react';
import { Option, getFilterSections } from '../utils/filterSections';
import styles from './FilterMenu.module.css';
import GetPartnerTagsQuery from './GetPartnerTags.graphql';

const RadioOptions = ({
  parentValue,
  selected,
  setSelected,
  options,
}: {
  parentValue: string;
  selected: string[];
  setSelected: (val: string[]) => void;
  options: Option[];
}) => {
  const handleRadioGroupSelection = (value: string): void => {
    setSelected([...selected.filter((v) => !v.includes(parentValue)), value]);
  };
  return (
    <Flex asChild direction="column" gap="1   ">
      <RadioGroup.Root
        name={parentValue}
        onValueChange={handleRadioGroupSelection}
        defaultValue={selected?.find((val) => val.includes(parentValue)) ?? ''}
      >
        {options.map(({ label, value }) => (
          <Box key={value} asChild px="1" py="1" width="100%">
            <RadioGroup.Item key={value} value={value} className={styles.radioGroupOption}>
              {label}
            </RadioGroup.Item>
          </Box>
        ))}
      </RadioGroup.Root>
    </Flex>
  );
};

const CheckboxOption = ({
  selected,
  setSelected,
  label,
  value,
}: {
  key: string;
  selected: string[];
  setSelected: (val: string[]) => void;
  label: string;
  value: string;
}) => {
  const handleClickCheckbox = (_: SyntheticEvent, value: string): void =>
    setSelected(
      selected?.includes(value) ? selected.filter((val) => val !== value) : [...selected, value],
    );
  return (
    <Text as="label" size="2">
      <Flex p="1" gap="2" align="center" className={styles.checkboxOption}>
        <Checkbox
          size="2"
          checked={selected?.includes(value)}
          onClick={(e) => {
            handleClickCheckbox(e, value);
          }}
        />
        {label}
      </Flex>
    </Text>
  );
};

const AutocompleteOption = ({
  selected = [],
  setSelected,
  options,
  title,
  parentValue,
}: {
  options: Option[];
  selected: string[];
  setSelected: (val: string[]) => void;
  title: string;
  parentValue: string;
}) => {
  const onChange = (_: SyntheticEvent, selectedOptions: Option[]): void => {
    const freshSelected = selected?.filter((each) => !each?.includes(parentValue));
    setSelected(
      (selectedOptions?.length
        ? [...freshSelected, ...selectedOptions.map((each) => each.value)]
        : [...freshSelected]) as string[],
    );
  };
  const selectedValues = selected?.filter((each) => each?.includes(parentValue));
  return (
    <div className="max-w-[284px]">
      <Autocomplete
        multiple
        id={title}
        onChange={onChange}
        options={options}
        value={options.filter((each) => selectedValues.includes(each.value))}
        getOptionLabel={(option: { label: string }) => option.label}
        renderInput={(params) => <TextField {...params} placeholder={title} />}
      />
    </div>
  );
};

interface FilterMenuProps {
  setSelected: (filters: string[]) => void;
  selected: string[];
  filterSections?: string[];
}

// TODO
// - shrink option text and checkbox
export default function FilterMenu({
  selected,
  setSelected,
  filterSections = [],
}: FilterMenuProps): React.JSX.Element {
  const params = useParams();
  const pageCatgory = params?.programId ? 'program' : 'partner';
  const posthog = usePostHog();
  const program = useProgram();
  const { externalId, programs: partnerPrograms, features: partnerFeatures } = usePartner();
  const { admins } = useAdmins(false);

  //TODO: Refactor usePartner and its query to allow querying for protected data
  const { data } = useQuery<{ partners: { partners: Partner[] } }, { externalId: string }>(
    GetPartnerTagsQuery,
    {
      variables: { externalId },
    },
  );

  const tags = data?.partners?.partners[0]?.tags || [];

  const sections = useMemo(() => {
    const rawSections = getFilterSections({
      programs: (program ? [program] : partnerPrograms) as Program[],
      tags,
      admins,
      isDocsAiEnabled:
        posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.docsAi2024Q4) &&
        checkFeature(partnerFeatures, FeatureName.DocumentsClassify),
      isCaseEligibilityEnabled: posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.rulesEngine),
    });
    return Object.entries(rawSections)
      .filter(([key, _]) => !filterSections.includes(key))
      .map(([_, section]) => section);
  }, [program, partnerPrograms, tags, admins, filterSections]);

  const [menuSelected, setMenuSelected] = useState(selected);

  // Track the previous selected value to only sync when it actually changes
  const prevSelectedRef = useRef(selected);

  useEffect(() => {
    // Only sync if selected actually changed (by value, not reference)
    const selectedChanged = JSON.stringify(prevSelectedRef.current) !== JSON.stringify(selected);
    if (selectedChanged) {
      prevSelectedRef.current = selected;
      setMenuSelected(selected);
    }
  }, [selected]);

  const handleClickApplyFilters = (e: SyntheticEvent): void => {
    e.stopPropagation();
    setSelected(menuSelected);
    posthog.capture(`${pageCatgory}:filters_applied`, { filters: menuSelected });
  };

  const activeSections = useMemo(() => {
    // selected filters are prefixed with the section they belong to prefixed with `section_`
    return Array.from(new Set(selected.map((activeFilter) => activeFilter.split('_')[0])));
  }, [selected]);

  return (
    <Popover.Root>
      <Popover.Trigger>
        <Button
          id="open-filter-menu"
          variant="ghost"
          color="gray"
          className={styles.FilterMenuTrigger}
        >
          <FilterList fontSize="small" /> Filter
        </Button>
      </Popover.Trigger>
      <Popover.Content
        className={styles.FilterMenuContent}
        width={'240px'}
        maxHeight="80vh"
        side="right"
      >
        <Inset>
          <Flex justify="between" align="center" width="100%" py="2" px="3" pr="4">
            <Text size="2">{`(${menuSelected?.length ?? 0}) Selected`}</Text>
            <Button
              variant="ghost"
              disabled={menuSelected?.length === 0}
              onClick={(e) => {
                e.stopPropagation();
                setMenuSelected([]);
                posthog.capture(`${pageCatgory}:filters_cleared`);
              }}
            >
              Reset
            </Button>
          </Flex>
          <hr />
          <Box asChild width="100%" py="1">
            <Accordion.Root type="multiple" defaultValue={activeSections}>
              {sections?.map(({ title, value, type, options }) => (
                <Accordion.Item value={value} key={title}>
                  <Flex
                    justify="between"
                    align="center"
                    px="4"
                    py="1"
                    width="100%"
                    asChild
                    className={styles.AccordionTrigger}
                  >
                    <Accordion.Trigger>
                      <Text size="3">{title}</Text>
                      <ChevronRightIcon className={styles.AccordionIcon} />
                    </Accordion.Trigger>
                  </Flex>
                  <Accordion.Content className={styles.AccordionContent}>
                    <Box px="3" py="1">
                      {type === 'radio' && (
                        <RadioOptions
                          key={value}
                          parentValue={value}
                          selected={menuSelected}
                          setSelected={setMenuSelected}
                          options={options}
                        />
                      )}
                      {type === 'checkbox' && (
                        <Flex direction="column" gap="1">
                          {options.map((option) => (
                            <CheckboxOption
                              key={option.value}
                              selected={menuSelected}
                              setSelected={setMenuSelected}
                              {...option}
                            />
                          ))}
                        </Flex>
                      )}
                      {type === 'autocomplete' && (
                        <AutocompleteOption
                          selected={menuSelected}
                          setSelected={setMenuSelected}
                          title={title}
                          options={options}
                          parentValue={value}
                        />
                      )}
                    </Box>
                  </Accordion.Content>
                </Accordion.Item>
              ))}
            </Accordion.Root>
          </Box>
          <hr />
          <Box py="2" px="3">
            <CheckboxOption
              key="archived_true"
              selected={menuSelected}
              setSelected={setMenuSelected}
              label="Include Archived"
              value="archived_true"
            />
          </Box>
          <hr />
          <Box py="2" px="4">
            <Popover.Close>
              <Button variant="soft" onClick={handleClickApplyFilters}>
                Show Results
              </Button>
            </Popover.Close>
          </Box>
        </Inset>
      </Popover.Content>
    </Popover.Root>
  );
}
