import { FIRST_PARTY_APPLICANT_TYPE, enabledFeature } from '@/tests/mocks/mockData';
import { ApplicantTypeRole, FeatureName, type Program, type Tag } from '@bybeam/platform-types';
import { FilterSections, Option, getFilterSections } from './filterSections';

describe('getFilterSections', () => {
  it('returns Expedited, Tags and Assignee filters always', () => {
    expect(
      getFilterSections({
        programs: [
          { applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }], features: [] },
        ] as unknown as Program[],
        tags: [] as Tag[],
        admins: [] as Option[],
      }),
    ).toEqual({
      [FilterSections.AnswerReviewStatus]: expect.any(Object),
      [FilterSections.Expedited]: {
        title: 'Expedited Status',
        value: 'expedited',
        type: 'radio',
        options: [
          { label: 'Expedited', value: 'expedited_true' },
          { label: 'Not Expedited', value: 'expedited_false' },
        ],
      },
      [FilterSections.Tags]: {
        title: 'Tags',
        value: 'tags',
        type: 'checkbox',
        options: [],
      },
      [FilterSections.Assignee]: {
        title: 'Assignee',
        value: 'assigneeId',
        type: 'autocomplete',
        options: [{ label: 'No Assignee', value: 'assigneeId_none' }],
      },
    });
  });

  it('returns Case Participants and Participant Status filters if any program has multiple applicant types', () => {
    expect(
      getFilterSections({
        programs: [
          { applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }], features: [] },
          {
            applicantTypes: [
              { applicantType: FIRST_PARTY_APPLICANT_TYPE },
              {
                applicantType: {
                  id: 'third-party-id',
                  name: 'Third Party Type',
                  role: ApplicantTypeRole.ThirdParty,
                },
              },
            ],
            features: [],
          },
        ] as unknown as Program[],
        tags: [] as Tag[],
        admins: [] as Option[],
      }),
    ).toEqual({
      [FilterSections.CaseParticipants]: {
        title: 'Case Participants',
        value: 'applicantTypeId',
        type: 'checkbox',
        options: [
          { label: 'Applicant', value: `applicantTypeId_${FIRST_PARTY_APPLICANT_TYPE.id}` },
          { label: 'Third Party Type', value: 'applicantTypeId_third-party-id' },
        ],
      },
      [FilterSections.CaseParticipantStatus]: {
        title: 'Participant Status',
        value: 'caseParticipantStatus',
        type: 'checkbox',
        options: [
          { label: 'Linked', value: 'caseParticipantStatus_Linked' },
          { label: 'Pending link', value: 'caseParticipantStatus_PendingLink' },
          { label: 'Failed link', value: 'caseParticipantStatus_FailedLink' },
          { label: 'Unlinked', value: 'caseParticipantStatus_Unlinked' },
        ],
      },
      [FilterSections.Tags]: expect.any(Object),
      [FilterSections.Expedited]: expect.any(Object),
      [FilterSections.Assignee]: expect.any(Object),
      [FilterSections.AnswerReviewStatus]: expect.any(Object),
    });
  });

  it('includes Referral Status filter if any program has Programs: Referral feature', () => {
    expect(
      getFilterSections({
        programs: [
          { applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }], features: [] },
          {
            applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }],
            features: [enabledFeature(FeatureName.ProgramsReferral)],
          },
        ] as Program[],
        tags: [] as Tag[],
        admins: [] as Option[],
      }),
    ).toEqual({
      [FilterSections.Expedited]: expect.any(Object),
      [FilterSections.Tags]: expect.any(Object),
      [FilterSections.Assignee]: expect.any(Object),
      [FilterSections.AnswerReviewStatus]: expect.any(Object),
      [FilterSections.Referral]: {
        title: 'Referral Status',
        value: 'referred',
        type: 'radio',
        options: [
          { label: 'Referred', value: 'referred_true' },
          { label: 'Not Referred', value: 'referred_false' },
        ],
      },
    });
  });

  it('includes Verification Status filter if any program has Application: Verification feature', () => {
    expect(
      getFilterSections({
        programs: [
          { applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }], features: [] },
          {
            applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }],
            features: [enabledFeature(FeatureName.ApplicationVerification)],
          },
        ] as Program[],
        tags: [] as Tag[],
        admins: [] as Option[],
      }),
    ).toEqual({
      [FilterSections.Expedited]: expect.any(Object),
      [FilterSections.Tags]: expect.any(Object),
      [FilterSections.Assignee]: expect.any(Object),
      [FilterSections.AnswerReviewStatus]: expect.any(Object),
      [FilterSections.Verification]: {
        title: 'Verification Status',
        type: 'checkbox',
        value: 'verified',
        options: [
          { label: 'Verified', value: 'verified_Verified' },
          { label: 'Partially Verified', value: 'verified_PartiallyVerified' },
          { label: 'Unverified', value: 'verified_Unverified' },
        ],
      },
    });
  });

  it('includes Document Tag types filter if partner has Documents: Classify feature and posthog feature enabled', () => {
    expect(
      getFilterSections({
        programs: [
          { applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }], features: [] },
        ] as unknown as Program[],
        tags: [] as Tag[],
        admins: [] as Option[],
        isDocsAiEnabled: true,
      }),
    ).toEqual({
      [FilterSections.Expedited]: expect.any(Object),
      [FilterSections.Tags]: expect.any(Object),
      [FilterSections.Assignee]: expect.any(Object),
      [FilterSections.AnswerReviewStatus]: expect.any(Object),
      [FilterSections.DocumentTagTypes]: {
        title: 'Document Tags',
        type: 'checkbox',
        value: 'documentTagTypes',
        options: [
          {
            label: 'Defect',
            value: 'documentTagTypes_TagType.defect',
          },
        ],
      },
    });
  });
  it('includes Answer Review filter', () => {
    expect(
      getFilterSections({
        programs: [
          { applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }], features: [] },
        ] as unknown as Program[],
        tags: [] as Tag[],
        admins: [] as Option[],
      }),
    ).toEqual({
      [FilterSections.Expedited]: expect.any(Object),
      [FilterSections.Tags]: expect.any(Object),
      [FilterSections.Assignee]: expect.any(Object),
      [FilterSections.AnswerReviewStatus]: {
        title: 'Answer Review Status',
        value: 'answerReviews',
        type: 'radio',
        options: [
          { label: 'Needs Resubmission', value: 'answerReviews_NEEDS_RESUBMISSION' },
          { label: 'Needs Further Review', value: 'answerReviews_NEEDS_FURTHER_REVIEW' },
          { label: 'Rejected', value: 'answerReviews_DOES_NOT_MEET_REQUIREMENTS' },
        ],
      },
    });
  });
  it('includes Eligibility filter if partner has posthog feature enabled', () => {
    expect(
      getFilterSections({
        programs: [
          { applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }], features: [] },
        ] as unknown as Program[],
        tags: [] as Tag[],
        admins: [] as Option[],
        isCaseEligibilityEnabled: true,
      }),
    ).toEqual({
      [FilterSections.Expedited]: expect.any(Object),
      [FilterSections.Tags]: expect.any(Object),
      [FilterSections.Assignee]: expect.any(Object),
      [FilterSections.AnswerReviewStatus]: expect.any(Object),
      [FilterSections.Eligibility]: {
        title: 'Eligibility',
        value: 'eligibility',
        type: 'radio',
        options: [
          { label: 'Eligible', value: 'eligibility_Eligible' },
          { label: 'Ineligible', value: 'eligibility_Ineligible' },
        ],
      },
    });
  });
  describe('payment status', () => {
    it('includes Payment Status filter if partner has posthog feature enabled and any program has any payment feature', () => {
      expect(
        getFilterSections({
          programs: [
            {
              applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }],
              features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
            },
            {
              applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }],
              features: [{ enabled: true, feature: { name: FeatureName.PaymentsPartnerIssued } }],
            },
          ] as unknown as Program[],
          tags: [] as Tag[],
          admins: [] as Option[],
        }),
      ).toEqual({
        [FilterSections.Expedited]: expect.any(Object),
        [FilterSections.Tags]: expect.any(Object),
        [FilterSections.Assignee]: expect.any(Object),
        [FilterSections.AnswerReviewStatus]: expect.any(Object),
        [FilterSections.PaymentStatus]: {
          title: 'Payment Status',
          value: 'paymentStatus',
          type: 'checkbox',
          options: [
            { label: 'Needs initiation', value: 'paymentStatus_pending' },
            { label: 'Awaiting re-initiation or Unclaimed', value: 'paymentStatus_authorized' },
            { label: 'Paid', value: 'paymentStatus_success' },
            { label: 'Failed', value: 'paymentStatus_failed' },
          ],
        },
      });
    });
    it('includes customized Payment Status filter if a program has "Payments: Claim Funds" feature', () => {
      expect(
        getFilterSections({
          programs: [
            {
              applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }],
              features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
            },
          ] as unknown as Program[],
          tags: [] as Tag[],
          admins: [] as Option[],
        }),
      ).toEqual({
        [FilterSections.Expedited]: expect.any(Object),
        [FilterSections.Tags]: expect.any(Object),
        [FilterSections.Assignee]: expect.any(Object),
        [FilterSections.AnswerReviewStatus]: expect.any(Object),
        [FilterSections.PaymentStatus]: {
          title: 'Payment Status',
          value: 'paymentStatus',
          type: 'checkbox',
          options: [
            { label: 'Needs initiation', value: 'paymentStatus_pending' },
            { label: 'Unclaimed', value: 'paymentStatus_authorized' },
            { label: 'Paid', value: 'paymentStatus_success' },
            { label: 'Failed', value: 'paymentStatus_failed' },
          ],
        },
      });
    });
    it('includes customized Payment Status filter if a program has "Payments: Partner Issued" feature', () => {
      expect(
        getFilterSections({
          programs: [
            {
              applicantTypes: [{ applicantType: FIRST_PARTY_APPLICANT_TYPE }],
              features: [{ enabled: true, feature: { name: FeatureName.PaymentsPartnerIssued } }],
            },
          ] as unknown as Program[],
          tags: [] as Tag[],
          admins: [] as Option[],
        }),
      ).toEqual({
        [FilterSections.Expedited]: expect.any(Object),
        [FilterSections.Tags]: expect.any(Object),
        [FilterSections.Assignee]: expect.any(Object),
        [FilterSections.AnswerReviewStatus]: expect.any(Object),
        [FilterSections.PaymentStatus]: {
          title: 'Payment Status',
          value: 'paymentStatus',
          type: 'checkbox',
          options: [
            { label: 'Needs initiation', value: 'paymentStatus_pending' },
            { label: 'Awaiting re-initiation', value: 'paymentStatus_authorized' },
            { label: 'Paid', value: 'paymentStatus_success' },
            { label: 'Failed', value: 'paymentStatus_failed' },
          ],
        },
      });
    });
  });
});
