import {
  checkFeatureAllPrograms,
  checkFeatureAnyProgram,
  checkProgramHasAnyFeature,
} from '@/app/_utils/checkFeature';
import {
  getAvailableApplicantTypes,
  getUniqueApplicantTypes,
} from '@/spa-legacy/common/utilities/applicantType';
import { DocumentTagType } from '@bybeam/doctopus-types';
import sort from '@bybeam/platform-lib/utilities/sort';
import {
  ApplicationAnswerReviewStatus,
  CaseParticipantStatus,
  EligibilityStatus,
  FeatureName,
  NoAssignee,
  PaymentStatus,
  type Program,
  type Tag,
  VerificationStatus,
} from '@bybeam/platform-types';

export enum FilterSections {
  Assignee = 'assignee',
  ApplicantStatus = 'applicantStatus',
  AnswerReviewStatus = 'answerReviewStatus',
  CaseParticipants = 'caseParticipants',
  CaseParticipantStatus = 'caseParticipantStatus',
  Expedited = 'expedited',
  Referral = 'referral',
  Verification = 'verification',
  Tags = 'tags',
  DocumentTagTypes = 'documentTagTypes',
  PaymentStatus = 'paymentStatus',
  Eligibility = 'eligibility',
}

export type Option = { label: string; value: string };

type FilterSectionConfig = {
  title: string;
  value: string;
  type: string;
  options: Option[];
};

const getPaymentStatusOptions = (programs: Program[]) => {
  const paymentPrograms = programs.filter((program) =>
    checkProgramHasAnyFeature(program, [
      FeatureName.PaymentsClaimFunds,
      FeatureName.PaymentsPartnerIssued,
    ]),
  );

  let authorizedLabel = 'Awaiting re-initiation or Unclaimed';
  if (checkFeatureAllPrograms(paymentPrograms, FeatureName.PaymentsClaimFunds))
    authorizedLabel = 'Unclaimed';
  else if (checkFeatureAllPrograms(paymentPrograms, FeatureName.PaymentsPartnerIssued))
    authorizedLabel = 'Awaiting re-initiation';

  return [
    { label: 'Needs initiation', value: `paymentStatus_${PaymentStatus.Pending}` },
    {
      label: authorizedLabel,
      value: `paymentStatus_${PaymentStatus.Authorized}`,
    },
    { label: 'Paid', value: `paymentStatus_${PaymentStatus.Success}` },
    { label: 'Failed', value: `paymentStatus_${PaymentStatus.Failed}` },
  ];
};

// Eligibility options derived from shared enum
const DEFAULT_VISIBLE_ELIGIBILITY_STATUSES: readonly EligibilityStatus[] = [
  EligibilityStatus.Eligible,
  EligibilityStatus.Ineligible,
] as const;

const getEligibilityOptions = (
  visible: readonly EligibilityStatus[] = DEFAULT_VISIBLE_ELIGIBILITY_STATUSES,
): Option[] => visible.map((v) => ({ label: v, value: `eligibility_${v}` }));

export const getFilterSections = ({
  programs,
  tags,
  admins,
  isDocsAiEnabled = false,
  isCaseEligibilityEnabled = false,
}: {
  programs: Program[];
  tags: Tag[];
  admins?: Option[];
  isDocsAiEnabled?: boolean;
  isCaseEligibilityEnabled?: boolean;
}): Partial<Record<FilterSections, FilterSectionConfig>> => {
  const sections = {
    ...(programs.some((p) => getAvailableApplicantTypes(p).length > 1) && {
      [FilterSections.CaseParticipants]: {
        title: 'Case Participants',
        value: 'applicantTypeId',
        type: 'checkbox',
        options: sort(getUniqueApplicantTypes(programs), {
          accessor: ({ name, role }) => [role, name].join(' '),
        }).map(({ id, name }) => ({ label: name, value: `applicantTypeId_${id}` })),
      },
      [FilterSections.CaseParticipantStatus]: {
        title: 'Participant Status',
        value: 'caseParticipantStatus',
        type: 'checkbox',
        options: [
          { label: 'Linked', value: `caseParticipantStatus_${CaseParticipantStatus.Linked}` },
          {
            label: 'Pending link',
            value: `caseParticipantStatus_${CaseParticipantStatus.PendingLink}`,
          },
          {
            label: 'Failed link',
            value: `caseParticipantStatus_${CaseParticipantStatus.FailedLink}`,
          },
          { label: 'Unlinked', value: `caseParticipantStatus_${CaseParticipantStatus.Unlinked}` },
        ],
      },
    }),
    [FilterSections.AnswerReviewStatus]: {
      title: 'Answer Review Status',
      value: 'answerReviews',
      type: 'radio',
      options: [
        {
          label: 'Needs Resubmission',
          value: `answerReviews_${ApplicationAnswerReviewStatus.NEEDS_RESUBMISSION}`,
        },
        {
          label: 'Needs Further Review',
          value: `answerReviews_${ApplicationAnswerReviewStatus.NEEDS_FURTHER_REVIEW}`,
        },
        {
          label: 'Rejected',
          value: `answerReviews_${ApplicationAnswerReviewStatus.DOES_NOT_MEET_REQUIREMENTS}`,
        },
      ],
    },
    [FilterSections.Expedited]: {
      title: 'Expedited Status',
      value: 'expedited',
      type: 'radio',
      options: [
        { label: 'Expedited', value: 'expedited_true' },
        { label: 'Not Expedited', value: 'expedited_false' },
      ],
    },
    [FilterSections.Tags]: {
      title: 'Tags',
      value: 'tags',
      type: 'checkbox',
      options: tags.map(({ id, name }) => {
        return { label: name, value: `tags_${id}` };
      }),
    },
    [FilterSections.Assignee]: {
      title: 'Assignee',
      value: 'assigneeId',
      type: 'autocomplete',
      options: [{ label: 'No Assignee', value: NoAssignee }, ...(admins ?? [])].map((admin) => ({
        label: admin.label,
        value: `assigneeId_${admin.value}`,
      })),
    },
    ...(checkFeatureAnyProgram(programs, FeatureName.ProgramsReferral) && {
      [FilterSections.Referral]: {
        title: 'Referral Status',
        value: 'referred',
        type: 'radio',
        options: [
          { label: 'Referred', value: 'referred_true' },
          { label: 'Not Referred', value: 'referred_false' },
        ],
      },
    }),
    ...(checkFeatureAnyProgram(programs, FeatureName.ApplicationVerification) && {
      [FilterSections.Verification]: {
        title: 'Verification Status',
        type: 'checkbox',
        value: 'verified',
        options: [
          { label: 'Verified', value: `verified_${VerificationStatus.Verified}` },
          {
            label: 'Partially Verified',
            value: `verified_${VerificationStatus.PartiallyVerified}`,
          },
          { label: 'Unverified', value: `verified_${VerificationStatus.Unverified}` },
        ],
      },
    }),
    ...(isDocsAiEnabled && {
      [FilterSections.DocumentTagTypes]: {
        title: 'Document Tags',
        type: 'checkbox',
        value: 'documentTagTypes',
        options: [
          {
            label: 'Defect',
            value: `documentTagTypes_${DocumentTagType.Defect}`,
          },
        ],
      },
    }),
    ...(programs.some((program) =>
      checkProgramHasAnyFeature(program, [
        FeatureName.PaymentsClaimFunds,
        FeatureName.PaymentsPartnerIssued,
      ]),
    ) && {
      [FilterSections.PaymentStatus]: {
        title: 'Payment Status',
        value: 'paymentStatus',
        type: 'checkbox',
        options: getPaymentStatusOptions(programs),
      },
    }),
    ...(isCaseEligibilityEnabled && {
      [FilterSections.Eligibility]: {
        title: 'Eligibility',
        value: 'eligibility',
        type: 'radio',
        options: getEligibilityOptions(),
      },
    }),
  };

  return sections;
};
