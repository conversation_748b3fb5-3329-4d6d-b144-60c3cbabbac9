import CanAccess from '@/app/components/features/CanAccess';
import useForm, {
  conditionallyRequiredField,
  defaultRequiredErrorMessage,
  emailField,
  fieldUpdate,
  fullNameField,
  optionalField,
  phoneField,
  ValidationMode,
} from '@/app/hooks/useForm';
import Dropdown from '@/spa-legacy/common/components/Dropdown';
import ErrorDisplay from '@/spa-legacy/common/components/ErrorDisplay';
import Modal from '@/spa-legacy/common/components/Modal';
import TextInput from '@/spa-legacy/common/components/TextInput';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Color from '@/spa-legacy/common/utilities/Color';
import { isThirdPartyUser } from '@/spa-legacy/common/utilities/applicantType';
import { mask } from '@/spa-legacy/common/utilities/format';
import { AccountTypesDisplay } from '@/spa-legacy/common/utilities/payment';
import { hasBankInfoChanged, validateAccountNumber } from '@/spa-legacy/portal/utils/bankAccount';
import {
  checkFeatureAnyProgram,
  checkFeaturesAnyProgram,
} from '@/spa-legacy/utilities/checkFeature';
import { nulled } from '@/spa-legacy/utilities/nullableFields';
import isRoutingNumberValid from '@/spa-legacy/utilities/routingNumber';
import { useMutation } from '@apollo/client';
import { formatPhone, isPhoneNumber, sanitizeFullName } from '@bybeam/formatting';
import {
  AccountType,
  FeatureName,
  type MutationResponse,
  TransactionErrorDisplay,
  type UpdateUserInput,
  type User,
} from '@bybeam/platform-types';
import { Button, Flex } from '@radix-ui/themes';
import { type FormEvent, useState } from 'react';
import UpdateUserMutation from '../../graphql/mutations/UpdateUserMutation.graphql';

interface EditApplicantModalProps {
  applicant: User;
  dialogTrigger: React.ReactNode;
  onClose?: () => void;
  isModalOpen?: boolean;
}

export default function EditApplicantModal({
  applicant: { id, ...rest },
  dialogTrigger,
  onClose: onCloseHandler,
  isModalOpen = false,
}: EditApplicantModalProps): JSX.Element {
  const [open, setOpen] = useState(false);
  const onClose = (): void => {
    setOpen(false);
    onCloseHandler?.();
  };

  const { programs } = usePartner();
  const partnerIssuedPaymentsEnabled = checkFeatureAnyProgram(
    programs,
    FeatureName.PaymentsPartnerIssued,
  );
  const partnerIssuedACH = checkFeaturesAnyProgram(programs, [
    FeatureName.PaymentsPartnerIssued,
    FeatureName.PaymentsDirectDeposit,
    FeatureName.PaymentsApplicants,
  ]);
  const { bankAccount } = rest;
  const {
    formData: {
      name,
      email,
      secondaryEmail,
      phone,
      taxId,
      accountType,
      routingNumber,
      accountNumber,
    },
    formData,
    trySubmit,
    dispatch,
    errors,
    isPristine,
    counters,
  } = useForm(
    {
      name: rest.name,
      email: rest.email,
      secondaryEmail: rest?.applicantProfile?.secondaryEmail,
      taxId: rest.taxId,
      phone: rest?.phone ? formatPhone(rest.phone) : '',
      accountType: bankAccount?.accountType,
      routingNumber: bankAccount?.routingNumber ?? '',
      accountNumber: bankAccount?.accountNumber ?? '',
    },
    {
      name: fullNameField('name', true, !isThirdPartyUser(rest as User)),
      email: emailField(true, 'email', 'Email'),
      secondaryEmail: emailField(false, 'secondaryEmail', 'Secondary Email'),
      phone: phoneField(),
      taxId: optionalField(),
      accountType: conditionallyRequiredField(
        undefined,
        (form) => partnerIssuedACH && hasBankInfoChanged(form, bankAccount),
        defaultRequiredErrorMessage('Account Type'),
      ),
      routingNumber: conditionallyRequiredField(
        (form) => isRoutingNumberValid(form.routingNumber),
        (form) => partnerIssuedACH && hasBankInfoChanged(form, bankAccount),
        defaultRequiredErrorMessage('Routing Number'),
      ),
      accountNumber: conditionallyRequiredField(
        (form) => validateAccountNumber(form, bankAccount),
        (form) => partnerIssuedACH && hasBankInfoChanged(form, bankAccount),
        defaultRequiredErrorMessage('Account Number'),
      ),
    },
    ValidationMode.RequiredOnSubmit,
  );

  const [updateUser, { loading }] = useMutation<
    { user: { update: MutationResponse<User> } },
    { input: UpdateUserInput }
  >(UpdateUserMutation);

  const { showSnackbar } = useSnackbar();
  const doMutation = async (): Promise<void> => {
    if (isPristine) return onClose();
    const {
      data: {
        user: {
          update: { metadata: updateUserMetadata },
        },
      },
    } = await updateUser({
      variables: {
        input: {
          id,
          name: sanitizeFullName(name),
          phone: formatPhone(phone, 'E164'),
          email,
          ...nulled({ taxId, secondaryEmail }),
          ...(partnerIssuedACH &&
            hasBankInfoChanged({ accountNumber, accountType, routingNumber }, bankAccount) && {
              bankAccount: { accountType, accountNumber, routingNumber },
            }),
        },
      },
    });

    if (updateUserMetadata.status < 400) {
      const barText =
        email !== rest.email
          ? `Changes saved! A verification email was sent to ${email}. This email address will display on this page when the user completes email verification.`
          : 'User successfully updated.';
      showSnackbar(barText);
      return onClose();
    }

    let error = 'Something went wrong';
    if (updateUserMetadata.status === 400 && updateUserMetadata?.errors)
      error = TransactionErrorDisplay[updateUserMetadata.errors[0]] || updateUserMetadata.errors[0];

    showSnackbar(
      error,
      <Button variant="text" textColor={Color.Link} onClick={(): Promise<void> => doMutation()}>
        Try Again
      </Button>,
    );
  };

  const Trigger = () => {
    // biome-ignore lint/a11y/useKeyWithClickEvents: controls are fully keyboard accessible; eventually replace this with radix Dialog.Trigger
    return <div onClick={() => setOpen(true)}>{dialogTrigger}</div>;
  };

  return (
    <div>
      <CanAccess>
        <Trigger />
      </CanAccess>
      <Modal size="S" onClickClose={onClose} isOpen={open || isModalOpen} title="">
        <form
          className="w-field m-auto"
          onSubmit={(e: FormEvent<HTMLFormElement>): void => {
            e.preventDefault();
            trySubmit(doMutation);
          }}
          noValidate
        >
          <div className="flex flex-col gap-y-4 mb-4">
            <Typography variant="h1">Edit Applicant Details</Typography>
            <Typography variant="h3">Edit the details for this applicant:</Typography>
          </div>
          <div className="flex flex-col gap-y-4 mb-6 items-center">
            <TextInput
              id="name"
              label="Name"
              value={name}
              onChange={(value: string): void => dispatch(fieldUpdate('name', value))}
              error={errors.name as string}
              required
              autoComplete="name"
            />
            <TextInput
              id="email"
              label="Email"
              value={email}
              onChange={(value): void => dispatch(fieldUpdate('email', value))}
              error={errors.email as string}
              type="email"
              required
            />
            {/* TODO display only for extended? */}
            <TextInput
              id="secondaryEmail"
              label="Secondary Email (optional)"
              value={secondaryEmail}
              onChange={(value): void => dispatch(fieldUpdate('secondaryEmail', value))}
              error={errors.secondaryEmail as string}
              type="email"
            />
            <TextInput
              id="phone"
              label="Phone Number"
              value={phone}
              onChange={(value): void => {
                const formatted = isPhoneNumber(value) ? formatPhone(value) : value;
                dispatch(fieldUpdate('phone', formatted));
              }}
              error={errors.phone as string}
              type="tel"
              required
            />
            {partnerIssuedPaymentsEnabled && (
              <TextInput
                id="taxIdSsn"
                label="Tax ID/SSN"
                value={mask(taxId)}
                onChange={(value): void => dispatch(fieldUpdate('taxId', value))}
                error={errors.taxId as string}
                type="text"
              />
            )}
            {partnerIssuedACH && (
              <>
                <Dropdown
                  id="accountType"
                  label="Account Type"
                  value={accountType}
                  items={[
                    {
                      label: AccountTypesDisplay[AccountType.Checking],
                      value: AccountType.Checking,
                    },
                    {
                      label: AccountTypesDisplay[AccountType.Savings],
                      value: AccountType.Savings,
                    },
                  ]}
                  onChange={(value: AccountType): void => {
                    dispatch(fieldUpdate('accountType', value));
                  }}
                  required={hasBankInfoChanged(formData, bankAccount)}
                  error={errors.accountType as string}
                />
                <TextInput
                  id="routingNumber"
                  label="Routing Number"
                  value={routingNumber}
                  onChange={(value): void => dispatch(fieldUpdate('routingNumber', value))}
                  error={errors.routingNumber as string}
                  type="text"
                  required={hasBankInfoChanged(formData, bankAccount)}
                />
                <TextInput
                  id="accountNumber"
                  label="Account Number"
                  value={accountNumber}
                  onChange={(value): void => dispatch(fieldUpdate('accountNumber', value))}
                  onFocus={(): void => {
                    if (
                      bankAccount?.accountNumber &&
                      accountNumber === bankAccount?.accountNumber
                    ) {
                      dispatch(fieldUpdate('accountNumber', ''));
                    }
                  }}
                  error={errors.accountNumber as string}
                  type="text"
                  required={hasBankInfoChanged(formData, bankAccount)}
                />
              </>
            )}
          </div>
          <ErrorDisplay
            errors={counters?.missingRequiredFields}
            visible={counters?.missingRequiredFields > 0}
          />
          <Flex gap="3" mt="4" justify="end">
            <Button type="button" onClick={onClose} variant="outline">
              Cancel
            </Button>
            <Button id="save-applicant-details" type="submit" loading={loading}>
              Save
            </Button>
          </Flex>
        </form>
      </Modal>
    </div>
  );
}
