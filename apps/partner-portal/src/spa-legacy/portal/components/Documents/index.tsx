import CanAccess from '@/app/components/features/CanAccess';
import DocumentPreview from '@/app/components/features/Documents/DocumentPreview/DocumentPreview';
import type { ApplicantNameConsistency } from '@/app/components/features/NameMatching';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { useCanAccess } from '@/app/providers/CanAccessProvider';
import type { TemporaryDocument, TemporaryNewDocument } from '@/spa-legacy/@types/document';
import DocumentFeedbackLabel from '@/spa-legacy/common/components/DocumentFeedbackLabel';
import DocumentUpload from '@/spa-legacy/common/components/DocumentUpload';
import type { DocumentFeedback } from '@/spa-legacy/common/components/DocumentUpload/types';
import UnstyledUploadButton from '@/spa-legacy/common/components/Upload/Button/UnstyledButton';
import { formatDate } from '@/spa-legacy/common/utilities/format';
import type { Document, User } from '@bybeam/platform-types';
import {
  DotsHorizontalIcon,
  DrawingPinFilledIcon,
  DrawingPinIcon,
  PlusIcon,
  TrashIcon,
} from '@radix-ui/react-icons';
import { Box, Button, DropdownMenu, Flex, Heading, IconButton, Table } from '@radix-ui/themes';
import { usePostHog } from 'posthog-js/react';
import styles from './Documents.module.css';

export const SYSTEM_GENERATED = 'System Generated';
export const getSystemGeneratedUploader = (doc: Document) =>
  ({
    name: `${doc.filename.split('.')[0]} - ${SYSTEM_GENERATED}`,
  }) as User;

type DocumentWithNameMatching = Document & {
  nameMatchingIndicator?: React.ReactNode;
  nameMatchingData?: {
    consistency: ApplicantNameConsistency;
    bestMatchedName: string;
  };
};

interface DocumentsProps {
  id: string;
  documents: Document[];
  feedback?: DocumentFeedback;
  removeDocument: { doRemove: (documentId: string) => void; loading: boolean };
  uploadDocument: { doUpload: (files: File[]) => void; loading: boolean };
  pinDocument: { doPin: (documentId: string, pinned: boolean) => void };
}

export default function Documents({
  id,
  documents,
  feedback,
  removeDocument: { doRemove, loading: removeLoading },
  uploadDocument: { doUpload, loading: uploadLoading },
  pinDocument: { doPin },
}: DocumentsProps): JSX.Element {
  const posthog = usePostHog();
  const { canAccess } = useCanAccess();
  const onUploadDocuments = (docs: TemporaryNewDocument[]): void =>
    doUpload(docs.map(({ file }) => file));

  const mappedDocuments = documents.map<TemporaryDocument>(
    ({ id: docId, previewUrl: url, filename: name, ...rest }) => ({
      id: docId,
      url,
      name,
      permanent: rest.uploader.name.includes(SYSTEM_GENERATED) ?? false,
      ...rest,
    }),
  );

  const isNewDocumentsList = posthog.isFeatureEnabled(
    POSTHOG_FEATURE_FLAGS.caseDetailsDocumentsList,
  );

  return (
    <Box className={isNewDocumentsList ? '' : styles.documentsSection}>
      <Flex direction="column" gap="2">
        <Flex justify="between" gap="2">
          <Heading size="4">Documents</Heading>
          <Flex gap="2" align="center">
            <CanAccess>
              <Button asChild variant="outline">
                <UnstyledUploadButton
                  id={id}
                  onUploadDocuments={onUploadDocuments}
                  className=""
                  disabled={uploadLoading}
                  multiple
                >
                  <PlusIcon />
                  Upload Document
                </UnstyledUploadButton>
              </Button>
            </CanAccess>
          </Flex>
        </Flex>
        {isNewDocumentsList ? (
          <Flex direction="column">
            <Table.Root variant="surface">
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeaderCell>Preview</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Document type</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell>Uploaded at</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell justify="center">Pinned</Table.ColumnHeaderCell>
                  <Table.ColumnHeaderCell justify="end" pr="4">
                    Actions
                  </Table.ColumnHeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {documents.map((doc) => {
                  const docWithNameMatching = doc as DocumentWithNameMatching;
                  return (
                    doc && (
                      <Table.Row key={doc.id} align="center">
                        <Table.Cell>
                          <Flex>
                            <DocumentPreview
                              document={doc}
                              nameMatchingData={docWithNameMatching.nameMatchingData}
                              triggerId={`doc-preview-${doc.id}`}
                            />
                          </Flex>
                        </Table.Cell>
                        <Table.Cell>
                          <Flex align="center" gap="1">
                            <Box
                              onClick={() => {
                                // Trigger the document preview dialog
                                const trigger = document.querySelector(
                                  `[data-trigger-id="doc-preview-${doc.id}"]`,
                                ) as HTMLElement;
                                trigger?.click();
                              }}
                              style={{ cursor: 'pointer', display: 'inline-flex' }}
                            >
                              {docWithNameMatching.nameMatchingIndicator || null}
                            </Box>
                            <DocumentFeedbackLabel
                              document={doc}
                              feedback={feedback}
                              enableFeedback={false}
                            />
                          </Flex>
                        </Table.Cell>
                        <Table.Cell>
                          {doc.createdAt ? formatDate(doc.createdAt) : ''} by {doc.uploader?.name}
                        </Table.Cell>
                        <Table.Cell justify={'center'}>
                          <IconButton onClick={() => doPin(doc.id, !doc.pinned)} variant="ghost">
                            {doc.pinned ? <DrawingPinFilledIcon /> : <DrawingPinIcon />}
                          </IconButton>
                        </Table.Cell>
                        <Table.Cell justify="end" pr="6">
                          <CanAccess>
                            <DropdownMenu.Root>
                              <DropdownMenu.Trigger>
                                <IconButton variant="ghost" aria-label="Actions">
                                  <DotsHorizontalIcon />
                                </IconButton>
                              </DropdownMenu.Trigger>
                              <DropdownMenu.Content>
                                <DropdownMenu.Item
                                  onSelect={() => {
                                    const confirmed = confirm(
                                      'Are you sure you want to remove this document? This cannot be undone.',
                                    );
                                    if (confirmed) {
                                      doRemove(doc.id);
                                    }
                                  }}
                                >
                                  <TrashIcon /> Remove
                                </DropdownMenu.Item>
                              </DropdownMenu.Content>
                            </DropdownMenu.Root>
                          </CanAccess>
                        </Table.Cell>
                      </Table.Row>
                    )
                  );
                })}
              </Table.Body>
            </Table.Root>
          </Flex>
        ) : (
          <Flex justify="start">
            <DocumentUpload
              id={id}
              data-cy={id}
              documents={mappedDocuments}
              onRemoveDocument={(idx: string): void =>
                doRemove(documents[Number.parseInt(idx, 10)].id)
              }
              onPinDocument={(idx: string, pinned: boolean): void =>
                doPin(documents[Number.parseInt(idx, 10)].id, pinned)
              }
              feedback={feedback}
              buttonText="Upload Documents"
              uploadedText={false}
              loading={{ remove: removeLoading, add: uploadLoading }}
              readonly={!canAccess}
            />
          </Flex>
        )}
      </Flex>
    </Box>
  );
}
