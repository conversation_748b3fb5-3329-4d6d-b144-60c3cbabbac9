import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';
import Link from '@/spa-legacy/common/components/Link';
import { Flex } from '@radix-ui/themes';
import { usePathname, useSearchParams } from 'next/navigation';
import ApplicationTitle from '../../ApplicationTitle';
import type { ApplicantNameCellValue } from './types';

export default function ApplicantNameCell({
  value: { programId, caseId, name, priority, displayId, hasReferral, verification },
}: { value: ApplicantNameCellValue }): JSX.Element {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  return (
    <Flex direction="column" gap="1">
      <ApplicationTitle
        name={name}
        priority={priority}
        hasReferral={hasReferral}
        verification={verification}
      />
      <Link
        to={{
          pathname: makeRoute(PORTAL_ROUTES.CASE_REVIEW, { programId, caseId }),
          query: { prev: `${pathname}?${searchParams.toString()}` },
        }}
      >
        {displayId}
      </Link>
    </Flex>
  );
}
