import { EnvelopeClosedIcon } from '@radix-ui/react-icons';
import { Flex, Text, Tooltip } from '@radix-ui/themes';
import { ReactElement } from 'react';

export interface DisplayEmailProps {
  email: string;
}

export default function DisplayEmail({ email }: DisplayEmailProps): ReactElement {
  return (
    <Tooltip content={`Send Email to ${email}`}>
      <Flex gap="1" align="center" minWidth="0" asChild>
        <a href={`mailto:${email}`}>
          <Text truncate>{email}</Text>
          <div>
            <EnvelopeClosedIcon height={12} width={12} />
          </div>
        </a>
      </Flex>
    </Tooltip>
  );
}
