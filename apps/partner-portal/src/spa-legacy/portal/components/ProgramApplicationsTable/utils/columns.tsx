import ProgramStatusCell from '@/app/[partnerId]/(authenticated)/programs/[programId]/(details)/components/PaymentStatus/PaymentStatusCell';
import { checkProgramHasAnyFeature } from '@/app/_utils/checkFeature';
import CanAccess from '@/app/components/features/CanAccess';
import Checkbox from '@/spa-legacy/common/components/Checkbox';
import Icon from '@/spa-legacy/common/components/Icon';
import type { Column } from '@/spa-legacy/common/components/Table/types';
import Tooltip from '@/spa-legacy/common/components/Tooltip';
import Typography from '@/spa-legacy/common/components/Typography';
import Color from '@/spa-legacy/common/utilities/Color';
import { getAvailableApplicantTypes } from '@/spa-legacy/common/utilities/applicantType';
import { getQuestion } from '@/spa-legacy/utilities/application/config';
import { checkFeature, checkFeatures } from '@bybeam/platform-lib/features/check';
import {
  type ApplicationConfig,
  type Case,
  FeatureName,
  NHCustomQuestionKeys,
  type Partner,
  type Program,
} from '@bybeam/platform-types';
import ApplicationTierTooltip from '../../ApplicationTierTooltip';
import ApplicantNameCell from '../../CasesTable/cells/ApplicantNameCell';
import CaseParticipantsCell from '../../CasesTable/cells/CaseParticipantsCell';
import TagsCell from '../../CasesTable/cells/TagsCell';
import { Tab, type TableRow } from '../types';

export default function getColumns(
  tab: Tab,
  allCasesChecked: boolean,
  checkCases: () => void,
  totalRows: number,
  includeCheckbox: boolean,
  program: Program,
  partner: Partner,
  applicationConfig: ApplicationConfig,
  /**
   * When true, show the Eligibility column (between Case Status and Payment Status).
   * This is gated by the PostHog 'rules-engine-enabled' flag to line up with
   * RulesEngineEvaluationService.
   */
  showEligibility = false,
): Column<TableRow>[] {
  const columns: Column<TableRow>[] = [];

  if (includeCheckbox) {
    columns.push({
      Header: function ApplicantNameHeader(): JSX.Element {
        return (
          <CanAccess resource={{ objectId: program?.id, objectType: 'PROGRAM' }}>
            <Tooltip title={allCasesChecked ? 'Deselect all on page' : 'Select all on page'}>
              <Checkbox
                checked={allCasesChecked}
                disabled={totalRows === 0}
                onClick={checkCases}
                ariaLabel={allCasesChecked ? 'Deselect All' : 'Select All'}
                label=""
                border={false}
                squashPadding
              />
            </Tooltip>
          </CanAccess>
        );
      },
      accessor: 'checkBox',
      Cell: function CheckboxCell({
        value,
      }: {
        value: {
          checked: boolean;
          case: Case;
          checkCase: (case_: Case) => void;
        };
      }): JSX.Element {
        return (
          <CanAccess resource={{ objectId: program?.id, objectType: 'PROGRAM' }}>
            <Checkbox
              checked={value.checked}
              onClick={(): void => value.checkCase(value.case)}
              label=""
              ariaLabel={`Select ${value?.case?.name ?? ''} Application`}
              border={false}
              squashPadding
            />
          </CanAccess>
        );
      },
    });
  }

  columns.push({
    Header: 'Name',
    accessor: 'applicantName',
    enableSorting: true,
    Cell: ApplicantNameCell,
  });

  if (getAvailableApplicantTypes(program).length > 1)
    columns.push({
      Header: 'Case Participant(s)',
      accessor: 'caseParticipants',
      Cell: CaseParticipantsCell,
    });

  const hasAddressField = !!getQuestion(applicationConfig, 'address.address');
  if (hasAddressField) {
    columns.push({
      Header: 'Property Address',
      accessor: 'propertyAddress',
      enableSorting: true,
      Cell: function AddressCell({
        value,
      }: {
        value: TableRow['propertyAddress'];
      }): JSX.Element {
        if (!value.address) return <>-</>;
        const { addressLine1, addressLine2, state, zip, city } = value.address;
        return (
          <div>
            <Typography variant="body">
              {addressLine1} {addressLine2}
            </Typography>
            <Typography variant="body">
              {city}, {state} {zip}
            </Typography>
          </div>
        );
      },
    });
  }

  columns.push({
    Header: [Tab.InProgress, Tab.Incomplete].includes(tab) ? 'Last Edited' : 'Submitted',
    accessor: 'date',
    enableSorting: true,
  });

  if (
    checkFeatures(program?.features, [FeatureName.ApplicationScoring, FeatureName.WorkflowCore]) &&
    tab !== Tab.InProgress
  ) {
    columns.push({
      Header: function NeedLevelTierHeader(): JSX.Element {
        return (
          <span className="inline-flex">
            <span>Need Level | Tier</span>
            <ApplicationTierTooltip>
              <Icon size="MD" color={Color.Text} type="infoOutline" className="ml-3" />
            </ApplicationTierTooltip>
          </span>
        );
      },
      accessor: 'needLevelTier',
    });
  }

  const showRequested = !!getQuestion(
    applicationConfig,
    NHCustomQuestionKeys.REQUESTS_RENTAL_ASSISTANCE,
  );
  if (showRequested) {
    columns.push({
      Header: 'Requested',
      accessor: 'requested',
      Cell: function RequestedCell({
        value,
      }: {
        value: { rental: boolean; utility: boolean; otherExpenses: boolean };
      }): JSX.Element {
        return (
          <div className="flex flex-col gap-y-1">
            {value.rental && <Typography variant="body">Rental Assistance</Typography>}
            {value.utility && <Typography variant="body">Utility Assistance</Typography>}
            {value.otherExpenses && <Typography variant="body">Other Expenses</Typography>}
          </div>
        );
      },
    });
  }

  columns.push({
    Header: 'Case Status',
    accessor: 'status',
    enableSorting: true,
  });

  if (showEligibility) {
    columns.push({
      Header: 'Eligibility',
      accessor: 'eligibility',
      enableSorting: true,
      Cell: function EligibilityCell({
        value,
      }: {
        value: TableRow['eligibility'];
      }): JSX.Element {
        if (!value?.value) return <>-</>;
        const reason = value.reason?.trim();
        return reason ? (
          <Tooltip title={reason}>
            <span>{value.value}</span>
          </Tooltip>
        ) : (
          <span>{value.value}</span>
        );
      },
    });
  }

  if (
    checkProgramHasAnyFeature(program, [
      FeatureName.PaymentsClaimFunds,
      FeatureName.PaymentsPartnerIssued,
    ])
  ) {
    columns.push({
      Header: 'Payment Status',
      accessor: 'paymentStatus',
      Cell: ({ value }) => <ProgramStatusCell {...value} program={program} />,
    });

    columns.push({
      Header: 'Awarded Amount',
      accessor: 'awardedAmount',
      enableSorting: true,
    });

    if (
      checkFeature(program?.features, FeatureName.PaymentsRecurring) &&
      [Tab.PaymentSent, Tab.All].includes(tab)
    ) {
      columns.push({
        Header: 'Total Paid',
        accessor: 'totalPaid',
        enableSorting: true,
      });
    }
  }

  columns.push({
    Header: 'Tags',
    accessor: 'caseTags',
    Cell: TagsCell,
  });

  columns.push({
    Header: 'Assignee',
    accessor: 'assignee',
    enableSorting: true,
  });

  return columns;
}
