import { useNextAuth } from '@/app/providers/NextAuthProvider';
import { useResetPageOnParamChange } from '@/spa-legacy/common/components/Table/hooks/useResetPageOnParamChange';
import type { TableState } from '@/spa-legacy/common/components/Table/types';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { type ProgramWorkflow, getProgramWorkflow } from '@/spa-legacy/portal/utils/workflow';
import { useQuery } from '@apollo/client';
import {
  type Case,
  type CaseCounts,
  type CaseFilter,
  CaseSortColumn,
  CaseStatus,
  FeatureName,
  type OffsetPagination,
  type PageInfo,
  type Program,
  type Sort,
  SortDirection,
} from '@bybeam/platform-types';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import type { IdType } from 'react-table';
import GetCaseAssignmentCount from '../../../graphql/queries/programCases/GetCaseAssignmentCount.graphql';
import GetCaseCounts from '../../../graphql/queries/programCases/GetCaseCounts.graphql';
import GetProgramApplications from '../../../graphql/queries/programCases/GetProgramApplications.graphql';
import getSortColumn from '../../CasesTable/utils/sort';
import { mapFilterVariables } from '../../SearchAndFilter/useSearchAndFilters';
import { Action, type ProgramCases, Tab, type TableRow } from '../types';
import parseCase from './parseCases';

const PAGE_SIZE = 150;
const PAGE_SORT = 'date';

function prepData(
  program: Program,
  data: Case[],
  casesChecked: string[],
  setCasesChecked: Dispatch<SetStateAction<string[]>>,
  tabFilter: Tab,
): TableRow[] {
  if (!data) return null;
  // Change the checkbox value for an case.
  const checkCase = (case_: Case): void => {
    setCasesChecked((previousCases) => {
      const indexOfApp = previousCases.indexOf(case_.id);

      if (indexOfApp === -1) {
        return [...previousCases, case_.id];
      }
      const newCases = [...previousCases];
      newCases.splice(indexOfApp, 1);

      return newCases;
    });
  };

  // Parse cases
  return data.map((entry) => parseCase(program, tabFilter, entry, casesChecked, checkCase));
}

const { Expedite, Reassign, BulkAction } = Action;
const TAB_ACTIONS = {
  [Tab.MyAssignments]: [BulkAction],
  [Tab.Denied]: [BulkAction],
  [Tab.PaymentSent]: [BulkAction],
  [Tab.InProgress]: [BulkAction],
  [Tab.Incomplete]: [BulkAction],
  [Tab.ReadyForReview]: [BulkAction, Expedite, Reassign],
  [Tab.InReview]: [BulkAction, Reassign],
  [Tab.PendingCertification]: [Reassign],
  [Tab.FiscalReview]: [Reassign],
  [Tab.Approved]: [BulkAction],
  [Tab.All]: [],
};

function getTabVariables(tab: Tab, adminId: string): Partial<CaseFilter> {
  switch (tab) {
    case Tab.MyAssignments:
      return { assigneeId: [adminId] };
    case Tab.InProgress:
      return { status: [CaseStatus.InProgress] };
    case Tab.Incomplete:
      return { status: [CaseStatus.Incomplete] };
    case Tab.ReadyForReview:
      return { status: [CaseStatus.ReadyForReview] };
    case Tab.InReview:
      return { status: [CaseStatus.InReview] };
    case Tab.PendingCertification:
      return { status: [CaseStatus.PendingCertification] };
    case Tab.FiscalReview:
      return { status: [CaseStatus.FiscalReview] };
    case Tab.Approved:
      return { status: [CaseStatus.Approved] };
    case Tab.PaymentSent:
      return { status: [CaseStatus.PaymentSent] };
    case Tab.Denied:
      return { status: [CaseStatus.Denied] };
    default:
      return {};
  }
}

function getProgramSpecificSortColumn(tab: Tab, column: IdType<TableRow>): CaseSortColumn {
  if (column === 'date')
    return [Tab.InProgress, Tab.Incomplete].includes(tab)
      ? CaseSortColumn.UpdatedAt
      : CaseSortColumn.SubmittedAt;
  return getSortColumn(column);
}

const WORKFLOW_TAB: { [feature in ProgramWorkflow]: Tab[] } = {
  [FeatureName.WorkflowCore]: [
    Tab.MyAssignments,
    Tab.InProgress,
    Tab.Incomplete,
    Tab.ReadyForReview,
    Tab.InReview,
    Tab.Approved,
    Tab.PaymentSent,
    Tab.Denied,
    Tab.All,
  ],
  [FeatureName.WorkflowExtended]: [
    Tab.MyAssignments,
    Tab.InProgress,
    Tab.Incomplete,
    Tab.ReadyForReview,
    Tab.InReview,
    Tab.PendingCertification,
    Tab.FiscalReview,
    Tab.All,
  ],
};

export default function useProgramCases(): ProgramCases {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Create a single stable string from URL to avoid reference instability
  const urlString = useMemo(() => searchParams.toString(), [searchParams]);

  // Parse values from the stable URL string
  const {
    search,
    tabFilter,
    filters,
    searchCategories,
    searchAnswersKey,
    searchAnswersValue,
    sortById,
    sortByDesc,
    pageIndex,
    pageSize,
  } = useMemo(() => {
    const params = new URLSearchParams(urlString);
    return {
      search: params.get('search') || '',
      tabFilter: (params.get('tab') as Tab) || Tab.ReadyForReview,
      filters: params.getAll('filters'),
      searchCategories: params.getAll('searchCategories'),
      searchAnswersKey: params.get('searchAnswersKey') || '',
      searchAnswersValue: params.get('searchAnswersValue') || '',
      sortById: params.get('sortById') || PAGE_SORT,
      sortByDesc: params.get('sortByDesc') === 'true',
      pageIndex: Number(params.get('pageIndex') || 0),
      pageSize: Number(params.get('pageSize') || PAGE_SIZE),
    };
  }, [urlString]);

  // Create stable update functions using useCallback with urlString dependency
  const setSearch = useCallback(
    (newSearch: string) => {
      const params = new URLSearchParams(urlString);
      if (newSearch) {
        params.set('search', newSearch);
      } else {
        params.delete('search');
      }
      router.replace(`?${params.toString()}`, { scroll: false });
    },
    [urlString, router],
  );

  const setTabFilter = useCallback(
    (tab: Tab) => {
      const params = new URLSearchParams(urlString);
      params.set('tab', tab);
      params.delete('search');
      params.delete('filters');
      router.replace(`?${params.toString()}`, { scroll: false });
    },
    [urlString, router],
  );

  const setFilters = useCallback(
    (newFilters: string[]) => {
      const params = new URLSearchParams(urlString);
      params.delete('filters');
      for (const f of newFilters) {
        params.append('filters', f);
      }
      router.replace(`?${params.toString()}`, { scroll: false });
    },
    [urlString, router],
  );

  const setSearchCategories = useCallback(
    (categories: string[]) => {
      const params = new URLSearchParams(urlString);
      params.delete('searchCategories');
      for (const c of categories) {
        params.append('searchCategories', c);
      }
      router.replace(`?${params.toString()}`, { scroll: false });
    },
    [urlString, router],
  );

  const setSearchAnswers = useCallback(
    (answers: Record<string, string | string[]>) => {
      const params = new URLSearchParams(urlString);
      const key = answers.searchAnswersKey as string;
      const value = answers.searchAnswersValue as string;

      if (key) {
        params.set('searchAnswersKey', key);
      } else {
        params.delete('searchAnswersKey');
      }

      if (value) {
        params.set('searchAnswersValue', value);
      } else {
        params.delete('searchAnswersValue');
      }

      router.replace(`?${params.toString()}`, { scroll: false });
    },
    [urlString, router],
  );

  const tableParams = useMemo<TableState<TableRow>>(
    () => ({
      sortBy: sortById ? { id: sortById as keyof TableRow, desc: sortByDesc } : undefined,
      pageIndex,
      pageSize,
    }),
    [sortById, sortByDesc, pageIndex, pageSize],
  );

  const setTableParams = useCallback(
    (newParams: TableState<TableRow>) => {
      const params = new URLSearchParams(urlString);

      if (newParams.sortBy?.id) {
        params.set('sortById', newParams.sortBy.id);
        params.set('sortByDesc', String(!!newParams.sortBy.desc));
      } else {
        // Remove sort params when sorting is cleared
        params.delete('sortById');
        params.delete('sortByDesc');
      }

      params.set('pageIndex', String(newParams.pageIndex));
      params.set('pageSize', String(newParams.pageSize));
      router.replace(`?${params.toString()}`, { scroll: false });
    },
    [urlString, router],
  );

  const [casesChecked, setCasesChecked] = useState<string[]>([]);
  const {
    user: { admin },
  } = useNextAuth();

  const program = useProgram();
  const programWorkflow = getProgramWorkflow(program);
  const tabs = WORKFLOW_TAB[programWorkflow];

  // Extract only the IDs we need to avoid unnecessary re-renders
  const programId = program?.id;
  const adminId = admin?.id;

  // Memoize tab variables to prevent unnecessary re-renders
  const tabVariables = useMemo(() => getTabVariables(tabFilter, adminId), [tabFilter, adminId]);

  // Create searchAnswers object for compatibility with existing code
  const searchAnswers = useMemo(
    () => ({
      searchAnswersKey,
      searchAnswersValue,
    }),
    [searchAnswersKey, searchAnswersValue],
  );

  const [queryVariables, setQueryVariables] = useState({
    filter: {
      ...tabVariables,
      programId,
      search,
      searchCategories,
      searchAnswersKey,
      searchAnswersValue,
      ...mapFilterVariables(filters, tabFilter === Tab.MyAssignments),
    },
    pagination: { page: pageIndex, take: pageSize },
    sort: {
      column: getProgramSpecificSortColumn(tabFilter, sortById || 'date'),
      direction: sortByDesc ? SortDirection.Descending : SortDirection.Ascending,
    },
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: All parsed values (search, filters, etc.) are memoized from urlString, so depending on urlString captures all changes
  useEffect(() => {
    const updater = setTimeout(() => {
      const filterVariables = mapFilterVariables(filters, tabFilter === Tab.MyAssignments);
      const pagination = { page: pageIndex, take: pageSize };
      const sort = {
        column: getProgramSpecificSortColumn(tabFilter, sortById || 'date'),
        direction: sortByDesc ? SortDirection.Descending : SortDirection.Ascending,
      };
      setQueryVariables({
        filter: {
          ...tabVariables,
          programId,
          search,
          searchCategories,
          searchAnswersKey,
          searchAnswersValue,
          ...filterVariables,
        },
        pagination,
        sort,
      });
    }, 300);

    return () => clearTimeout(updater);
  }, [urlString, programId, tabVariables]);

  const {
    data: queryData,
    loading,
    error,
  } = useQuery<
    { cases: { pageInfo: PageInfo; cases: Case[] } },
    {
      filter: Partial<CaseFilter>;
      pagination: OffsetPagination;
      sort: Sort<CaseSortColumn>;
    }
  >(GetProgramApplications, {
    variables: queryVariables,
    skip: !tableParams || !programId,
    fetchPolicy: 'cache-and-network',
  });

  const { data: assignmentData } = useQuery<{ cases: { pageInfo: { count: number } } }>(
    GetCaseAssignmentCount,
    {
      variables: { programId, adminId },
      skip: !programId || !adminId,
      fetchPolicy: 'cache-and-network',
    },
  );

  // TODO update counts based on filters also?
  const { data: countsData } = useQuery<{
    programs: { programs: [{ stats: { caseCounts: CaseCounts } }] };
  }>(GetCaseCounts, {
    // TODO only map these once and memo
    variables: { programId },
    skip: !programId,
    fetchPolicy: 'cache-and-network',
  });

  const counts = useMemo(
    () =>
      countsData && assignmentData
        ? {
            MyAssignments: assignmentData?.cases?.pageInfo?.count,
            ...countsData?.programs?.programs?.[0]?.stats?.caseCounts,
          }
        : undefined,
    [countsData, assignmentData],
  );

  const checkCases = useCallback(
    (): void =>
      setCasesChecked((prevCasesChecked) =>
        prevCasesChecked.length === queryData?.cases?.cases.length
          ? []
          : (queryData?.cases?.cases.map(({ id }) => id) ?? []),
      ),
    [queryData?.cases?.cases],
  );

  const uncheckCases = () => setCasesChecked([]);

  // Get the cases for the current tab, and parse.
  // Storing these two values in state so that they're not cleared out when
  // the user goes to the new page, which looks weird.
  const [preppedData, setPreppedData] = useState<TableRow[]>([]);
  const [totalRows, setTotalRows] = useState<number>(undefined);
  useEffect(() => {
    if (queryData && program) {
      setPreppedData(
        prepData(program, queryData.cases.cases, casesChecked, setCasesChecked, tabFilter),
      );
      setTotalRows(queryData.cases.pageInfo.count);
    }
  }, [queryData, casesChecked, tabFilter, program]);

  useEffect(() => {
    if (!loading) setCasesChecked([]);
  }, [loading]);

  const { showSnackbar } = useSnackbar();
  useEffect(() => {
    if (error) showSnackbar('Uh oh! An error occurred! Please refresh and try again.');
  }, [error, showSnackbar]);

  useResetPageOnParamChange<TableRow>({
    search,
    searchCategories,
    setTableParams,
    tableParams,
  });

  return {
    actions: TAB_ACTIONS[tabFilter],
    checkCases,
    checkedCases: casesChecked,
    counts,
    fetchData: setTableParams,
    filters,
    loading,
    preppedData,
    search,
    searchAnswers,
    searchCategories,
    setFilters,
    setSearch,
    setSearchAnswers,
    setSearchCategories,
    setTabFilter,
    tabFilter,
    tableParams,
    tabs,
    totalRows,
    uncheckCases,
  };
}
