import { makeRoute } from '@/app/Routes';
import Icon from '@/spa-legacy/common/components/Icon';
import MenuButton from '@/spa-legacy/common/components/MenuButton';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import Color from '@/spa-legacy/common/utilities/Color';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { PORTAL_ROUTES } from '@/app/Routes';
import { useBulkPayment } from '@/spa-legacy/portal/pages/BulkPayment/hooks/useBulkPayment';
import { Route } from 'next';
import ChangeStatusOrAssigneeModal from '../../ChangeStatusOrAssigneeModal';
import ExpediteModal from '../../ExpediteModal';
import SendMessageModal from '../../SendMessageModal';

export enum BulkActionType {
  PAYMENT = 'payment',
  STATUS_OR_ASSIGNEE = 'status_or_assignee',
  EXPEDITE = 'expedite',
  SEND_MESSAGE = 'send_message',
}

export function BulkActionsButton({
  actionProps,
  onClick,
}: {
  actionProps: Record<
    BulkActionType,
    { hidden?: boolean; disabled?: boolean; hasExceeded?: boolean }
  >;
  onClick: (value: BulkActionType) => void;
}): JSX.Element {
  const options = [
    {
      label: `Initiate Payment${actionProps[BulkActionType.PAYMENT]?.hasExceeded ? ' (max limit of 50 items)' : ''}`,
      value: BulkActionType.PAYMENT,
      key: BulkActionType.PAYMENT,
      icon: <Icon type="dollarSign" size="MD" />,
      disabled: actionProps[BulkActionType.PAYMENT]?.disabled,
      hidden: actionProps[BulkActionType.PAYMENT]?.hidden,
      // biome-ignore lint/complexity/useLiteralKeys: linter is wrong
      ['data-cy']: 'bulk-actions-initiate-payment',
    },
    {
      label: 'Change Status or Assignee',
      value: BulkActionType.STATUS_OR_ASSIGNEE,
      key: BulkActionType.STATUS_OR_ASSIGNEE,
      icon: <Icon type="syncAlt" size="MD" />,
      disabled: actionProps[BulkActionType.STATUS_OR_ASSIGNEE]?.disabled,
      hidden: actionProps[BulkActionType.STATUS_OR_ASSIGNEE]?.hidden,
      // biome-ignore lint/complexity/useLiteralKeys: linter is wrong
      ['data-cy']: 'bulk-actions-change-status',
    },
    {
      label: 'Expedite',
      value: BulkActionType.EXPEDITE,
      key: BulkActionType.EXPEDITE,
      icon: <Icon type="flag" size="MD" color={Color.Error} />,
      disabled: actionProps[BulkActionType.EXPEDITE]?.disabled,
      hidden: actionProps[BulkActionType.EXPEDITE]?.hidden,
      // biome-ignore lint/complexity/useLiteralKeys: linter is wrong
      ['data-cy']: 'bulk-actions-expedite',
    },
    {
      label: 'Send New Message',
      value: BulkActionType.SEND_MESSAGE,
      key: BulkActionType.SEND_MESSAGE,
      icon: <Icon type="send" size="MD" />,
      disabled: actionProps[BulkActionType.SEND_MESSAGE]?.disabled,
      hidden: actionProps[BulkActionType.SEND_MESSAGE]?.hidden,
      // biome-ignore lint/complexity/useLiteralKeys: linter is wrong
      ['data-cy']: 'bulk-actions-send-message',
    },
  ]
    .filter((action) => !action.hidden)
    .map(({ hidden, ...action }) => action);

  return (
    <MenuButton
      id="bulk_actions"
      type="button"
      variant="text"
      icon="bulkAction"
      onClick={onClick}
      options={options}
      data-cy={'bulk-actions-button'}
    >
      Bulk Action
    </MenuButton>
  );
}

interface BulkActionProps {
  action: BulkActionType;
  ids: string[];
  onClose: () => void;
}

export function BulkAction({ action, ...props }: BulkActionProps) {
  const { setSelectedCaseIds } = useBulkPayment();
  const program = useProgram();
  const router = useRouter();
  useEffect(() => {
    if (program) {
      if (action === BulkActionType.PAYMENT) {
        setSelectedCaseIds(props.ids);
        const route = makeRoute(PORTAL_ROUTES.BULK_PAYMENT, { programId: program.id });
        router.replace(route as Route);
      }
    }
  }, [action, program, props.ids, setSelectedCaseIds, router]);
  switch (action) {
    case BulkActionType.EXPEDITE:
      return <ExpediteModal {...props} mode="BULK" />;
    case BulkActionType.STATUS_OR_ASSIGNEE:
      return <ChangeStatusOrAssigneeModal {...props} mode="BULK" />;
    case BulkActionType.SEND_MESSAGE:
      return <SendMessageModal {...props} mode="BULK" />;
    default:
      return null;
  }
}
