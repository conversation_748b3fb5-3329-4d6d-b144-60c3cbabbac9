import CanAccess from '@/app/components/features/CanAccess';
import Color, { BackgroundColors } from '@/spa-legacy/common/utilities/Color';
import { Button } from '@radix-ui/themes';
import { Heading } from '@radix-ui/themes';
import type { ReactNode } from 'react';

interface DetailSectionProps {
  title: string;
  children: ReactNode;
  action?: { children: ReactNode; onClick: () => void };
  customAction?: ReactNode;
}

export default function DetailSection({
  title,
  children,
  action,
  customAction,
}: DetailSectionProps): JSX.Element {
  return (
    <div className={`${BackgroundColors[Color.TableBackground]} py-4 px-5 rounded-lg`}>
      <div className="flex w-full justify-between items-center">
        <Heading size="4" as="h2">
          {title}
        </Heading>
        {action && (
          <CanAccess>
            <Button variant="ghost" color="blue" onClick={action.onClick}>
              {action.children}
            </Button>
          </CanAccess>
        )}
        {customAction}
      </div>
      <div className="mr-8 mt-6 ml-3">{children}</div>
    </div>
  );
}
