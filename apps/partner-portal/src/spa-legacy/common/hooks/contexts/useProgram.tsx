import type { Nullable, Program } from '@bybeam/platform-types';
import { useParams } from 'next/navigation';
import React, { useContext, useMemo, type ReactNode } from 'react';
import usePartner from './usePartner';

export const ProgramContext = React.createContext<string>(undefined);

export function ProgramRouteContent({ children }: { children: ReactNode }): JSX.Element {
  const { programId } = useParams<{ programId: string }>();
  return <ProgramContext.Provider value={programId}>{children}</ProgramContext.Provider>;
}

export default function useProgram(): Nullable<Program> {
  const { programs } = usePartner() ?? {};
  const programId = useContext(ProgramContext);

  return useMemo(() => programs?.find(({ id }) => id === programId), [programs, programId]);
}
