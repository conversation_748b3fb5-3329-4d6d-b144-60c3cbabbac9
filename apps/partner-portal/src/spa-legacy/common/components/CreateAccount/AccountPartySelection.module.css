.formContainer {
  border: 1px solid var(--gray-6);
  border-radius: var(--radius-3);
  padding: var(--space-5);
}

.radioOption {
  border: 1px solid var(--gray-6);
  border-radius: var(--radius-2);
  cursor: pointer;
  transition: all 150ms ease;
}

.radioOption:hover {
  background: var(--gray-2);
  border-color: var(--gray-7);
}

.radioOption[data-selected="true"] {
  background: var(--accent-2);
  border-color: var(--accent-8);
}

