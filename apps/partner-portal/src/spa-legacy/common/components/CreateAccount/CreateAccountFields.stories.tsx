import { ApplicantTypeRole } from '@bybeam/platform-types';
import type { Meta, StoryObj } from '@storybook/nextjs';
import CreateAccountFields from './CreateAccountFields';

const mockApplicantTypes = [
  {
    id: '1',
    name: 'Individual',
    role: ApplicantTypeRole.FirstParty,
    partnerId: 'partner-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    isArchived: false,
  },
  {
    id: '2',
    name: 'Business',
    role: ApplicantTypeRole.FirstParty,
    partnerId: 'partner-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    isArchived: false,
  },
];

const mockThirdPartyApplicantTypes = [
  {
    id: '3',
    name: 'Agency',
    role: ApplicantTypeRole.ThirdParty,
    partnerId: 'partner-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    isArchived: false,
  },
  {
    id: '4',
    name: 'Non-Profit',
    role: ApplicantTypeRole.ThirdParty,
    partnerId: 'partner-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    isArchived: false,
  },
  {
    id: '5',
    name: 'Service Provider',
    role: ApplicantTypeRole.ThirdParty,
    partnerId: 'partner-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    isArchived: false,
  },
];

const mockUseHook = () => ({
  doMutation: (formData: Record<string, unknown>) => {
    console.log('Form submitted:', formData);
  },
  error: undefined,
  loading: false,
  displayLoginLink: false,
});

const mockUseHookWithError = () => ({
  doMutation: (formData: Record<string, unknown>) => {
    console.log('Form submitted:', formData);
  },
  error: 'Failed to create account. Email address is already in use.',
  loading: false,
  displayLoginLink: true,
});

const mockUseHookLoading = () => ({
  doMutation: (formData: Record<string, unknown>) => {
    console.log('Form submitted:', formData);
  },
  error: undefined,
  loading: true,
  displayLoginLink: false,
});

const meta: Meta<typeof CreateAccountFields> = {
  component: CreateAccountFields,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof CreateAccountFields>;

export const FirstParty: Story = {
  args: {
    useHook: mockUseHook,
    applicantTypeRole: ApplicantTypeRole.FirstParty,
    applicantTypes: mockApplicantTypes,
  },
};

export const ThirdParty: Story = {
  args: {
    useHook: mockUseHook,
    applicantTypeRole: ApplicantTypeRole.ThirdParty,
    applicantTypes: [...mockApplicantTypes, ...mockThirdPartyApplicantTypes],
  },
};

export const ThirdPartySingleType: Story = {
  args: {
    useHook: mockUseHook,
    applicantTypeRole: ApplicantTypeRole.ThirdParty,
    applicantTypes: [
      ...mockApplicantTypes,
      {
        id: '3',
        name: 'Agency',
        role: ApplicantTypeRole.ThirdParty,
        partnerId: 'partner-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        isArchived: false,
      },
    ],
  },
};

export const Loading: Story = {
  args: {
    useHook: mockUseHookLoading,
    applicantTypeRole: ApplicantTypeRole.FirstParty,
    applicantTypes: mockApplicantTypes,
  },
};

export const WithError: Story = {
  args: {
    useHook: mockUseHookWithError,
    applicantTypeRole: ApplicantTypeRole.FirstParty,
    applicantTypes: mockApplicantTypes,
  },
};
