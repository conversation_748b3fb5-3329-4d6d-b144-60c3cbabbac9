import useSnackbar, { <PERSON>nac<PERSON><PERSON><PERSON><PERSON> } from '@/spa-legacy/common/hooks/useSnackbar';
import { isEmail } from '@/spa-legacy/common/utilities/email';
import {
  formatPhone,
  getFirstName,
  getLastName,
  isPhoneNumber,
  sanitizeFullName,
} from '@bybeam/formatting';
import { ApplicantType, ApplicantTypeRole } from '@bybeam/platform-types';
import usePartner from '@platform-ui-common/hooks/contexts/usePartner';
import {
  getDefaultApplicantTypeId,
  getSortedApplicantTypes,
} from '@platform-ui-common/utilities/applicantType';
import { ExclamationTriangleIcon } from '@radix-ui/react-icons';
import * as Label from '@radix-ui/react-label';
import {
  Box,
  Button,
  Callout,
  Checkbox,
  Flex,
  Heading,
  Link,
  Select,
  Text,
  TextField,
} from '@radix-ui/themes';
import { type ReactElement, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import styles from './CreateAccountFields.module.css';

/**
 * Regex pattern for validating full names.
 * Allows:
 * - Letters (a-z, A-Z, and accented characters À-ÿ) - must start with at least one letter
 * - Hyphens (for names like "Mary-Jane")
 * - Apostrophes (for names like "O'Brien")
 * - Ampersands (for names like "Smith & Jones")
 * - Numbers (for suffixes like "III" or "2nd")
 * - Spaces (between name parts)
 */
const FULL_NAME_PATTERN = /^[a-zA-ZÀ-ÿ]+[-a-zA-ZÀ-ÿ0-9''& ]*$/u;

interface BaseFormData {
  fullName: string;
  email: string;
  phone: string;
  userAgreement: boolean;
  applicantTypeId: string;
}

export interface CreateAccountProps {
  useHook: (formData: BaseFormData) => {
    doMutation: (formData: Record<string, unknown>) => void;
    error?: string;
    loading: boolean;
  };
  applicantTypeRole?: ApplicantTypeRole;
  applicantTypes: ApplicantType[];
}

export default function CreateAccountFields({
  useHook,
  applicantTypeRole = ApplicantTypeRole.FirstParty,
  applicantTypes,
}: CreateAccountProps): ReactElement {
  const { showSnackbar, removeSnackbar } = useSnackbar();
  const partner = usePartner();
  const [applicantHasEmail, setApplicantHasEmail] = useState(true);

  const isThirdParty = applicantTypeRole === ApplicantTypeRole.ThirdParty;

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    clearErrors,
    control,
    formState: { errors, isSubmitted },
  } = useForm<BaseFormData>({
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      userAgreement: false,
      applicantTypeId: getDefaultApplicantTypeId(applicantTypes, applicantTypeRole),
    },
    mode: 'onTouched',
  });

  const formData = watch();
  const { doMutation, error, loading } = useHook(formData);

  const thirdParties = getSortedApplicantTypes(applicantTypes, ApplicantTypeRole.ThirdParty);

  const onSubmit = (data: BaseFormData): void => {
    doMutation({
      partnerId: partner.id,
      name: sanitizeFullName(data.fullName),
      email: data.email?.toLowerCase() || null,
      phone: (data.phone ? formatPhone(data.phone, 'E164') : null) as string,
      applicantTypeId: data.applicantTypeId,
    });
  };

  const validateFullName = (value: string): string | undefined => {
    if (!value.match(FULL_NAME_PATTERN)) {
      return 'Please enter only valid characters';
    }
    if (!isThirdParty) {
      const firstName = getFirstName(value);
      const lastName = getLastName(value);
      if (!firstName || !lastName) {
        return 'Please enter first and last name';
      }
    }
    if (value.length <= 4) {
      return 'Full Name must be at least 4 characters long';
    }
    return undefined;
  };

  const validateEmail = (value: string): string | undefined => {
    if (!applicantHasEmail) return undefined;
    if (!value) return 'Email Address is required';
    if (!isEmail(value)) return 'Please enter a valid email address';
    return undefined;
  };

  const validatePhone = (value: string): string | undefined => {
    if (!value) return undefined;
    if (!isPhoneNumber(value)) return 'Please enter a valid phone number';
    return undefined;
  };

  useEffect(() => {
    if (error) {
      showSnackbar(error, (key: SnackbarKey) => (
        <Button variant="ghost" onClick={(): void => removeSnackbar(key)}>
          Dismiss
        </Button>
      ));
    }
  }, [error, removeSnackbar, showSnackbar]);

  useEffect(() => {
    if (!applicantHasEmail) {
      setValue('email', '');
      clearErrors('email');
    }
  }, [applicantHasEmail, setValue, clearErrors]);

  const missingRequiredFields = isSubmitted ? Object.keys(errors).length : 0;

  return (
    <Flex align="center" justify="center" p="2">
      <Flex
        asChild
        direction="column"
        gap="4"
        maxWidth="24rem"
        width="100%"
        className={styles.formContainer}
      >
        <form onSubmit={handleSubmit(onSubmit)} noValidate>
          <Heading size="6" weight="medium" align="center">
            Create Account for User
          </Heading>
          <Text size="2" color="gray">
            * An asterisk indicates a required response
          </Text>

          <Box>
            <Label.Root htmlFor="create-account-fullName">
              <Text size="2" weight="medium">
                Full Name *
              </Text>
            </Label.Root>
            <TextField.Root
              id="create-account-fullName"
              {...register('fullName', {
                required: 'Full Name is required',
                validate: validateFullName,
              })}
            />
            {errors.fullName && (
              <Text size="1" color="red" mt="1">
                {errors.fullName.message}
              </Text>
            )}
          </Box>

          {isThirdParty && thirdParties?.length > 0 && (
            <Flex direction="column" gap="1">
              <Label.Root htmlFor="create-account-applicantTypeId">
                <Text size="2" weight="medium">
                  Account Type *
                </Text>
              </Label.Root>
              <Controller
                name="applicantTypeId"
                control={control}
                rules={{ required: 'Account Type is required' }}
                render={({ field }): ReactElement => (
                  <Select.Root
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={thirdParties.length === 1}
                  >
                    <Select.Trigger id="create-account-applicantTypeId" />
                    <Select.Content>
                      {thirdParties.map((applicantType) => (
                        <Select.Item key={applicantType.id} value={applicantType.id}>
                          {applicantType.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                )}
              />
              {errors.applicantTypeId && (
                <Text size="1" color="red" mt="1">
                  {errors.applicantTypeId.message}
                </Text>
              )}
            </Flex>
          )}

          <Box>
            <Label.Root htmlFor="create-account-email">
              <Text size="2" weight="medium">
                Email Address {applicantHasEmail && '*'}
              </Text>
            </Label.Root>
            <TextField.Root
              id="create-account-email"
              type="email"
              disabled={!applicantHasEmail}
              {...register('email', {
                validate: validateEmail,
              })}
            />
            {errors.email && (
              <Text size="1" color="red" mt="1">
                {errors.email.message}
              </Text>
            )}
          </Box>

          <Box>
            <Label.Root htmlFor="create-account-phone">
              <Text size="2" weight="medium">
                Phone Number
              </Text>
            </Label.Root>
            <TextField.Root
              id="create-account-phone"
              type="tel"
              {...register('phone', {
                validate: validatePhone,
                onChange: (e): void => {
                  const value = e.target.value;
                  const formatted = isPhoneNumber(value) ? formatPhone(value) : value;
                  setValue('phone', formatted, { shouldDirty: true, shouldValidate: true });
                },
              })}
            />
            {errors.phone && (
              <Text size="1" color="red" mt="1">
                {errors.phone.message}
              </Text>
            )}
          </Box>

          <Flex gap="2" align="center" asChild>
            <Text as="label" size="2">
              <Checkbox
                id="no-email-checkbox"
                checked={!applicantHasEmail}
                onCheckedChange={(): void => setApplicantHasEmail(!applicantHasEmail)}
              />
              <span>
                This applicant <strong>does not</strong> have an email address
              </span>
            </Text>
          </Flex>

          <Box>
            <Controller
              name="userAgreement"
              control={control}
              rules={{
                required: 'Please agree to our Terms of Service and Privacy Policy',
              }}
              render={({ field }): ReactElement => (
                <Text as="label" size="2">
                  <Flex gap="2" align="start">
                    <Checkbox
                      id="user-agreement-checkbox"
                      checked={field.value}
                      onCheckedChange={(checked) => field.onChange(!!checked)}
                      mt="1"
                    />
                    <Text size="2">
                      By checking this box you agree to our{' '}
                      <Link
                        href="https://www.bybeam.co/terms-of-service"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Terms of Service
                      </Link>{' '}
                      and{' '}
                      <Link
                        href="https://www.bybeam.co/privacy-policy"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Privacy Policy
                      </Link>
                      .*
                    </Text>
                  </Flex>
                </Text>
              )}
            />
            {errors.userAgreement && (
              <Text size="1" color="red" mt="1">
                {errors.userAgreement.message}
              </Text>
            )}
          </Box>

          {isSubmitted && missingRequiredFields > 0 && (
            <Callout.Root color="red">
              <Callout.Icon>
                <ExclamationTriangleIcon />
              </Callout.Icon>
              <Callout.Text>
                {missingRequiredFields} required {missingRequiredFields === 1 ? 'field' : 'fields'}{' '}
                missing
              </Callout.Text>
            </Callout.Root>
          )}
          <Flex asChild width="100%">
            <Button type="submit" loading={loading}>
              Get Started
            </Button>
          </Flex>
        </form>
      </Flex>
    </Flex>
  );
}
