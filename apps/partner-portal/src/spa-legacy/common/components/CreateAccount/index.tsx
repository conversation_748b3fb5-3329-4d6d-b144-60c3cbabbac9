import { ApplicantTypeRole } from '@bybeam/platform-types';
import { ArrowLeftIcon } from '@radix-ui/react-icons';
import { Box, Button } from '@radix-ui/themes';
import { useState } from 'react';
import AccountPartySelection from './AccountPartySelection';
import CreateAccountFields, { CreateAccountProps } from './CreateAccountFields';

export default function CreateAccount({ applicantTypes, ...rest }: CreateAccountProps) {
  const isMultiParty = applicantTypes?.length > 1;

  const [selectedApplicantTypeRole, setSelectedApplicantTypeRole] = useState<ApplicantTypeRole>();
  const [showCreateAccount, setShowCreateAccount] = useState<boolean>(!isMultiParty);

  return (
    <Box p="4">
      {isMultiParty && !showCreateAccount && (
        <AccountPartySelection
          onSubmit={(role: ApplicantTypeRole) => {
            setSelectedApplicantTypeRole(role);
            setShowCreateAccount(true);
          }}
          defaultRole={selectedApplicantTypeRole}
        />
      )}
      {isMultiParty && showCreateAccount && (
        <Button
          type="button"
          variant="outline"
          color="gray"
          onClick={() => {
            setShowCreateAccount(false);
          }}
        >
          <ArrowLeftIcon /> Start over
        </Button>
      )}
      {showCreateAccount && (
        <CreateAccountFields
          {...rest}
          applicantTypeRole={selectedApplicantTypeRole}
          applicantTypes={applicantTypes}
        />
      )}
    </Box>
  );
}
