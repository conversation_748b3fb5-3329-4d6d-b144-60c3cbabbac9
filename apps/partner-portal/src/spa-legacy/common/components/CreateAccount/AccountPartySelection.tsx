import { ApplicantTypeRole } from '@bybeam/platform-types';
import { Box, Button, Flex, Heading, RadioGroup, Text } from '@radix-ui/themes';
import { type FormEvent, useState } from 'react';
import styles from './AccountPartySelection.module.css';

const ROLE_OPTIONS = [
  {
    value: ApplicantTypeRole.FirstParty,
    label: 'A person who wants to apply to receive benefits',
  },
  {
    value: ApplicantTypeRole.ThirdParty,
    label: "Someone who needs to provide supplemental information on others' applications",
  },
];

export default function AccountPartySelection({
  onSubmit,
  defaultRole,
}: {
  onSubmit: (role: ApplicantTypeRole) => void;
  defaultRole?: ApplicantTypeRole;
}) {
  const [selectedRole, setSelectedRole] = useState<ApplicantTypeRole | undefined>(defaultRole);
  const [error, setError] = useState('');

  const handleFormSubmit = (e: FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    if (!selectedRole) {
      setError('Please select an option');
      return;
    }
    onSubmit(selectedRole);
  };

  const handleRoleChange = (value: string): void => {
    setSelectedRole(value as ApplicantTypeRole);
    setError('');
  };

  return (
    <Flex align="center" justify="center" p="2">
      <Flex
        asChild
        direction="column"
        gap="2"
        maxWidth="26rem"
        width="100%"
        className={styles.formContainer}
      >
        <form onSubmit={handleFormSubmit} noValidate>
          <Heading size="6" weight="medium" align="center">
            Create account
          </Heading>
          <Text size="1" color="gray" align="center">
            * An asterisk indicates a required response
          </Text>
          <Box>
            <Text as="div" size="3" weight="medium" mb="3" id="applicant-type-label">
              Which statement best describes this applicant? *
            </Text>
            <Flex direction="column" gap="2">
              <RadioGroup.Root
                value={selectedRole || ''}
                onValueChange={handleRoleChange}
                aria-labelledby="applicant-type-label"
              >
                {ROLE_OPTIONS.map((option) => (
                  <Box
                    key={option.value}
                    p="3"
                    className={styles.radioOption}
                    data-selected={selectedRole === option.value}
                    asChild
                  >
                    <label htmlFor={option.value}>
                      <Flex gap="3" align="center">
                        <RadioGroup.Item value={option.value} id={option.value} />
                        <Text size="2">{option.label}</Text>
                      </Flex>
                    </label>
                  </Box>
                ))}
              </RadioGroup.Root>
              {error && (
                <Text size="1" color="red" mt="1">
                  {error}
                </Text>
              )}
            </Flex>
          </Box>

          <Flex asChild width="100%">
            <Button type="submit">Continue</Button>
          </Flex>
        </form>
      </Flex>
    </Flex>
  );
}
