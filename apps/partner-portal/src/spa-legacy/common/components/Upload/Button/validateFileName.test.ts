import { VALID_FILE_TYPES } from '@bybeam/platform-types';
import { validateFileName } from './UnstyledButton';

describe('validateFileName', () => {
  /**
   * Creates a mock FileList from an array of File objects
   */
  function createFileList(files: File[]): FileList {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('name', 'file-upload');
    input.multiple = true;
    const fileList = Object.create(input.files);
    files.forEach((file, index) => {
      fileList[index] = file;
    });
    Object.defineProperty(fileList, 'length', { value: files.length });
    return fileList;
  }

  describe('valid file types', () => {
    test.each(VALID_FILE_TYPES)('should accept valid %p filename', (type) => {
      const file = new File(['content'], `test.${type}`);
      const fileList = createFileList([file]);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBeUndefined();
    });

    it('should accept multiple valid files', () => {
      const files = VALID_FILE_TYPES.slice(0, 3).map(
        (type) => new File(['content'], `test.${type}`),
      );
      const fileList = createFileList(files);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBeUndefined();
    });
  });

  describe('invalid file types', () => {
    it('should reject .zip files', () => {
      const file = new File(['content'], 'archive.zip');
      const fileList = createFileList([file]);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBe('Only images, pdfs, word documents, or excel files are allowed.');
    });

    it('should reject .exe files', () => {
      const file = new File(['content'], 'program.exe');
      const fileList = createFileList([file]);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBe('Only images, pdfs, word documents, or excel files are allowed.');
    });

    it('should reject files with no extension', () => {
      const file = new File(['content'], 'file');
      const fileList = createFileList([file]);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBe('Only images, pdfs, word documents, or excel files are allowed.');
    });

    it('should reject if any file in a list is invalid', () => {
      const files = [
        new File(['content'], 'valid.pdf'),
        new File(['content'], 'invalid.zip'),
        new File(['content'], 'valid.jpg'),
      ];
      const fileList = createFileList(files);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBe('Only images, pdfs, word documents, or excel files are allowed.');
    });

    it('should handle files with no name', () => {
      const file = new File(['content'], '');
      const fileList = createFileList([file]);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBe('Only images, pdfs, word documents, or excel files are allowed.');
    });
  });

  describe('edge cases', () => {
    it('should handle empty FileList', () => {
      const fileList = createFileList([]);
      const errorMessage = validateFileName(fileList);
      expect(errorMessage).toBeUndefined();
    });
  });
});
