import type { TemporaryNewDocument } from '@/spa-legacy/@types/document';
import { allowedExtensions } from '@bybeam/platform-lib/verification/allowedExtensions';
import { VALID_FILE_TYPES } from '@bybeam/platform-types';
import cx from 'classnames';
import type { ChangeEvent, ReactNode } from 'react';
import sanitize from 'sanitize-filename';
import styles from './UnstyledButton.module.css';

/**
 * Validates that all files in a FileList have allowed extensions.
 * @returns Error message string if validation fails, undefined if all files are valid
 */
export function validateFileName(rawDocuments: FileList): string | undefined {
  let errorMessage: string | undefined = undefined;
  for (const document of Array.from(rawDocuments)) {
    const originalFilename = document.name ?? '';
    if (!allowedExtensions.test(originalFilename)) {
      errorMessage = 'Only images, pdfs, word documents, or excel files are allowed.';
      break;
    }
  }
  return errorMessage;
}

interface UnstyledButtonProps {
  children: ReactNode;
  className: string;
  disabled?: boolean;
  hasError?: boolean;
  helperTextId?: string;
  id: string;
  multiple?: boolean;
  onUploadDocuments?: (docs: TemporaryNewDocument[]) => void;
  onUploadError?: (errorMessage: string | ReactNode) => void;
}

export default function UnstyledButton({
  children,
  className,
  disabled,
  hasError = false,
  helperTextId,
  id,
  multiple,
  onUploadDocuments,
  onUploadError,
}: UnstyledButtonProps): JSX.Element {
  const onChange = (e: ChangeEvent<HTMLInputElement>): void => {
    if (!e.target.files || e.target.files.length === 0 || !onUploadDocuments) return;

    const rawDocuments = e.target.files;
    if (rawDocuments.length > 100) {
      onUploadError?.(
        <>
          <strong>Document upload failed:</strong> You can upload a max of 100 documents at a time.
          Please try again.
        </>,
      );
      return;
    }

    const filesHaveError = validateFileName(rawDocuments);
    if (filesHaveError) {
      onUploadError?.(
        <>
          <strong>Document upload failed:</strong> {filesHaveError}
        </>,
      );
      return;
    }

    const formattedDocuments: TemporaryNewDocument[] = Array.from(rawDocuments).map((file) => ({
      file,
      url: `${URL.createObjectURL(file)}`,
      name: sanitize(file.name),
      mimetype: file.type,
      isNew: true,
    }));
    onUploadDocuments(formattedDocuments);
    e.target.value = null;
  };

  const acceptedTypes = VALID_FILE_TYPES.map((type) => `.${type}`).join(',');

  return (
    <label
      htmlFor={`upload-${id}`}
      className={cx(
        className,
        'focus-within:outline focus-within:outline-2 focus-within:outline-firefoxDefaultFocus focus-within:outline-webkitDefaultFocus',
      )}
    >
      <input
        id={`upload-${id}`}
        type="file"
        accept={acceptedTypes}
        className={`${styles.UnstyledButton} w-0 h-0 absolute`}
        multiple={multiple}
        disabled={disabled}
        onChange={onChange}
        aria-errormessage={helperTextId}
        aria-invalid={hasError}
      />
      {children}
    </label>
  );
}
