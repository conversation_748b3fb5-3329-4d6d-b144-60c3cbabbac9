import { Menu, MenuItem } from '@mui/material';
import { IconButton, type ButtonProps as RadixButtonProps, Text } from '@radix-ui/themes';
import { ReactElement, type ReactNode, useRef, useState } from 'react';
import MenuButtonOptions from './Options';

export interface MenuButtonOption<T> {
  label: ReactNode;
  value: T;
  key: string | number;
  icon?: ReactNode;
  disabled?: boolean;
  hidden?: boolean;
}

export interface MenuButtonProps<T> extends Omit<RadixButtonProps, 'onClick'> {
  id: string;
  onClick: (option: T) => void;
  options: MenuButtonOption<T>[];
  prompt?: string;
  title?: string;
}

export default function MenuButton<T>(props: MenuButtonProps<T>): ReactElement {
  const { id, onClick, options, prompt, title, ...rest } = props;
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLButtonElement>();
  return (
    <>
      <IconButton
        ref={ref}
        onClick={(): void => setOpen(true)}
        id={`${id}-button`}
        aria-controls={open ? `${id}-menu` : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        radius="full"
        {...rest}
      />
      <Menu
        id={`${id}-menu`}
        anchorEl={ref.current}
        open={open}
        onClose={(): void => setOpen(false)}
        MenuListProps={{ 'aria-labelledby': `${id}-button` }}
      >
        <div>
          {title && (
            <div className="py-3 px-4">
              <Text as="label">{title}</Text>
            </div>
          )}
          {prompt && (
            <MenuItem disabled>
              <Text as="label" color="gray">
                {prompt}
              </Text>
            </MenuItem>
          )}
          <MenuButtonOptions {...props} setOpen={setOpen} />
        </div>
      </Menu>
    </>
  );
}
