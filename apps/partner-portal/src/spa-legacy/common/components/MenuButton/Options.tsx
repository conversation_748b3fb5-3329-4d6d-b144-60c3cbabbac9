import { ListItemIcon, ListItemText, MenuItem } from '@mui/material';
import type { MenuButtonProps } from '.';

export default function MenuButtonOptions<T>({
  onClick,
  options,
  setOpen,
}: MenuButtonProps<T> & { setOpen: (open: boolean) => void }) {
  return (
    <>
      {options?.map(({ label, value, key, icon, disabled, hidden, ...rest }) => {
        const iconItem = icon && (
          <>
            <ListItemIcon className="!min-w-fit mr-2">{icon}</ListItemIcon>
            <ListItemText>{label}</ListItemText>
          </>
        );
        const defaultItem = <span key={key}>{label}</span>;
        return !hidden ? (
          <MenuItem
            key={key}
            onClick={(): void => {
              onClick(value);
              setOpen(false);
            }}
            disabled={disabled}
            {...rest}
          >
            {iconItem ? iconItem : defaultItem}
          </MenuItem>
        ) : null;
      })}
    </>
  );
}
