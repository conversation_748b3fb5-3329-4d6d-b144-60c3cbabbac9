import { checkProgramHasAnyFeature } from '@/app/_utils/checkFeature';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import type { FeatureName } from '@bybeam/platform-types';
import type { ReactNode } from 'react';

export default function AnyFeatures({
  children,
  features,
}: { children: ReactNode; features: FeatureName[] }): JSX.Element {
  const program = useProgram();
  if (!checkProgramHasAnyFeature(program, features)) return null;
  return <>{children}</>;
}
