import ReactMarkdown from 'react-markdown';
import Link from '../Link';
import SimpleList from '../SimpleList';
import Typography, { type Alignment, type Tag, type Variant } from '../Typography';

interface MarkdownProps {
  children: string;
  variant?: Variant;
  alignment?: Alignment;
  tags?: { p?: Tag };
}

export default function Markdown({ children, variant = 'body', tags, ...props }: MarkdownProps) {
  // TODO: Automated spacing between elements?
  return (
    <ReactMarkdown
      components={{
        p: ({ children: tagChildren }) => (
          <Typography variant={variant} tag={tags?.p} {...props}>
            {tagChildren}
          </Typography>
        ),
        a: ({ children: tagChildren, href }) => (
          <Link
            to={href}
            variant={variant}
            target="_blank"
            external
            rel={href.startsWith('mailto:') ? 'noopener noreferrer' : undefined}
          >
            {tagChildren}
          </Link>
        ),
        ol: ({ children: tagChildren }) => <SimpleList type="number">{tagChildren}</SimpleList>,
        ul: ({ children: tagChildren }) => <SimpleList type="bullet">{tagChildren}</SimpleList>,
        li: ({ children: tagChildren }) => (
          <Typography variant={variant} tag="li">
            {tagChildren}
          </Typography>
        ),
      }}
    >
      {children}
    </ReactMarkdown>
  );
}
