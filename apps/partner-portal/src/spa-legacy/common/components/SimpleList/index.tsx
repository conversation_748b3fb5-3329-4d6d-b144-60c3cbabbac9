import cx from 'classnames';
import type { ReactNode } from 'react';

type ListStyleType = 'bullet' | 'number';

export interface BulletListProps {
  children: ReactNode;
  space?: boolean;
  type?: ListStyleType;
}

const SimpleList = ({ children, space, type = 'bullet' }: BulletListProps): JSX.Element => {
  const Component = type === 'number' ? 'ol' : 'ul';
  return (
    // TODO do better w/ space
    <Component
      className={cx('pl-6', {
        'space-y-4': space,
        'list-disc': type === 'bullet',
        'list-decimal': type === 'number',
      })}
    >
      {children}
    </Component>
  );
};

export default SimpleList;
