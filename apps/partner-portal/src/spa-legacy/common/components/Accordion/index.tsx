import { Button, type ButtonProps } from '@radix-ui/themes';
import cx from 'classnames';
import uniqueId from 'lodash/uniqueId';
import { ReactElement, type ReactNode, useRef, useState } from 'react';
import Color, { BackgroundColors } from '../../../../spa-legacy/common/utilities/Color';
import Icon from '../Icon';
import MenuButton, { type MenuButtonProps } from '../MenuButton';
import Typography from '../Typography';

type AccordionActionProps =
  | Pick<ButtonProps, 'children' | 'onClick'>
  | ({ variant: 'menu' } & Pick<
      MenuButtonProps<string>,
      'id' | 'children' | 'onClick' | 'options'
    >);

export interface AccordionProps {
  title: string;
  children: ReactNode;
  action?: AccordionActionProps;
  actionComponent?: ReactNode;
  initializeOpen?: boolean;
  inMenu?: boolean;
}

const Accordion = ({
  title,
  children,
  action,
  actionComponent,
  initializeOpen = false,
  inMenu = false,
}: AccordionProps): ReactElement => {
  const [open, setOpen] = useState(initializeOpen);
  const onToggleAccordion = (): void => setOpen((wasOpen) => !wasOpen);
  const sectionId = useRef(`${uniqueId()}-section`).current;
  const buttonId = `${sectionId}-button`;

  function Action(): ReactElement {
    if (!action || !open) return null;
    if (!('variant' in action)) return <Button variant="ghost" {...action} />;
    const { variant: _, ...rest } = action;
    return <MenuButton variant="ghost" {...rest} />;
  }

  return (
    <div
      className={cx({
        [`py-4 px-5 ${BackgroundColors[Color.TableBackground]}`]: !inMenu,
        [`py-2 px-4 ${BackgroundColors[Color.LightBackground]}`]: inMenu,
        'rounded-lg': true,
      })}
    >
      <div className="flex w-full justify-between items-center">
        <button
          type="button"
          onClick={onToggleAccordion}
          className="grow text-left"
          aria-expanded={open}
          aria-controls={sectionId}
        >
          <Typography tag="span" variant={inMenu ? 'body' : 'h4'} color={Color.Text} id={buttonId}>
            {title}
          </Typography>
        </button>
        <Action />
        {open && actionComponent}
        <button
          type="button"
          onClick={onToggleAccordion}
          className="flex pl-6"
          aria-labelledby={buttonId}
          aria-expanded={open}
          aria-controls={sectionId}
        >
          <Icon
            color={Color.Text}
            type={open ? 'keyboardArrowUp' : 'keyboardArrowDown'}
            size="LG"
          />
        </button>
      </div>
      <div
        aria-labelledby={buttonId}
        id={sectionId}
        hidden={!open}
        className={cx({ 'mr-8 mt-6 ml-3': !inMenu })}
      >
        {children}
      </div>
    </div>
  );
};

export default Accordion;
