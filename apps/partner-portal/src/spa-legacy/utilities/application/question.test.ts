import { checkboxField, dateField, requiredField } from '@/app/hooks/useForm';
import {
  type ApplicantProfile,
  type ApplicantProfileConfiguration,
  type Application,
  type ApplicationAnswers,
  type ApplicationQuestion,
  type Field,
  FieldType,
} from '@bybeam/platform-types';
import { answer, literal } from './ast/utils';
import {
  getQuestionFields,
  getQuestionFormInit,
  getQuestionUpdate,
  isPrefilled,
  isQuestionEnabled,
  isQuestionGroupEnabled,
} from './question';

describe('question', () => {
  describe('getQuestionFormInit', () => {
    it('combines the form inits for each field', () => {
      // See the fields/getFieldFormInit tests for expected form data per field type
      expect(
        getQuestionFormInit(
          {
            fields: [
              { type: FieldType.Checkbox, key: 'checkbox.field' },
              { type: FieldType.CheckboxGroup, key: 'checkbox.group' },
              { type: FieldType.Typography, key: 'text' },
              { type: FieldType.RadioList, key: 'radio.list' },
              {
                type: FieldType.Complex,
                key: 'complex',
                subFields: [
                  {
                    type: FieldType.Date,
                    key: 'date',
                    copy: 'Date Field',
                  },
                  {
                    type: FieldType.Text,
                    key: 'text',
                    copy: 'Text Field',
                  },
                ],
              },
            ],
          } as ApplicationQuestion,
          {
            checkbox: { field: true, group: ['one', 'two'] },
            radio: { list: 'keyOne' },
          } as Partial<ApplicationAnswers>,
          {} as Application,
        ),
      ).toEqual([
        {
          'checkbox.field': true,
          'checkbox.group': ['one', 'two'],
          'radio.list': 'keyOne',
          complex: [{ date: '', text: '' }],
        },
        {
          'checkbox.field': {
            ...checkboxField('checkbox.field'),
            fieldIsPresent: expect.any(Function),
          },
          'checkbox.group': requiredField(undefined, 'A selection is required'),
          'radio.list': requiredField(undefined, 'A selection is required'),
          complex: {
            type: 'repeated',
            required: true,
            schema: {
              date: {
                required: true,
                type: 'simple',
                requiredErrorMessage: 'Date Field is required',
                validate: expect.any(Function),
              },
              text: {
                required: true,
                type: 'simple',
                requiredErrorMessage: 'Text Field is required',
              },
            },
          },
        },
      ]);
    });

    it('sets the correct value for skippable complex fields with multilevel question keys', () => {
      expect(
        getQuestionFormInit(
          {
            key: 'question.key',
            skippable: true,
            fields: [
              {
                type: FieldType.Complex,
                key: 'question.key.complex',
                subFields: [
                  {
                    type: FieldType.Text,
                    key: 'text',
                    copy: 'Text Field',
                  },
                ],
              },
            ],
          } as ApplicationQuestion,
          {
            question: {
              key: {
                skipped: true,
              },
            },
          } as Partial<ApplicationAnswers>,
          {} as Application,
        ),
      ).toEqual([
        { 'question.key.complex': [{ text: '' }], 'question.key.skipped': true },
        {
          'question.key.complex': {
            required: true,
            schema: {
              text: {
                requiredErrorMessage: 'Text Field is required',
                required: true,
                type: 'simple',
              },
            },
            type: 'repeated',
          },
          'question.key.skipped': { type: 'simple', required: false },
        },
      ]);
    });

    it('parses empty document answers for a skipped question', () => {
      expect(
        getQuestionFormInit(
          {
            key: 'question.key',
            skippable: true,
            fields: [{ type: FieldType.Document, key: 'question.key' }],
          } as ApplicationQuestion,
          {
            question: { key: { skipped: true } },
          } as Partial<ApplicationAnswers>,
          {} as Application,
        ),
      ).toEqual([
        { 'question.key': [], 'question.key.skipped': true },
        {
          'question.key': {
            required: true,
            type: 'simple',
            requiredErrorMessage: 'At least one document is required',
          },
          'question.key.skipped': { type: 'simple', required: false },
        },
      ]);
    });

    it('ignores any existing answers for a skipped question', () => {
      expect(
        getQuestionFormInit(
          {
            key: 'question.key',
            skippable: true,
            fields: [{ type: FieldType.Text, key: 'question.key.text', copy: 'Text Field' }],
          } as ApplicationQuestion,
          { question: { key: { skipped: true, text: 'value' } } } as Partial<ApplicationAnswers>,
          {} as Application,
        ),
      ).toEqual([
        { 'question.key.text': '', 'question.key.skipped': true },
        {
          'question.key.skipped': { type: 'simple', required: false },
          'question.key.text': {
            type: 'simple',
            required: true,
            requiredErrorMessage: 'Text Field is required',
          },
        },
      ]);
    });
  });

  describe('getQuestionAnswers', () => {
    describe('when there are no existing answers', () => {
      it('merges all of the answers to the field', () => {
        // see the fields tests for more details on the expected type of each field
        expect(
          getQuestionUpdate(
            {
              key: 'question.key',
              fields: [
                { type: 'checkbox', key: 'question.key.checkbox.field' },
                { type: 'checkboxGroup', key: 'question.key.checkbox.group' },
                {
                  type: 'radioList',
                  key: 'question.key.radio.list',
                  options: [{ value: 'keyOne' }, { value: 'keyTwo' }],
                },
                {
                  type: FieldType.Complex,
                  key: 'question.key.complex',
                  subFields: [
                    { type: FieldType.Date, key: 'date', validation: dateField('date') },
                    { type: FieldType.Text, key: 'text' },
                  ],
                },
              ],
            } as ApplicationQuestion,
            {},
            {
              'question.key.checkbox.field': true,
              'question.key.checkbox.group': ['one', 'two'],
              'question.key.radio.list': 'keyOne',
              'question.key.complex': [{ date: '01/01/1990', text: 'data' }],
            },
          ),
        ).toEqual({
          answers: {
            question: {
              key: {
                checkbox: {
                  field: true,
                  group: ['one', 'two'],
                },
                complex: [{ date: '01/01/1990', text: 'data' }],
                radio: { list: 'keyOne' },
              },
            },
          },
          addresses: [],
          documents: [],
        });
      });
    });

    describe('when there are existing answers', () => {
      it('merges the field answers with the relevant existing answers, overwriting arrays', () => {
        expect(
          getQuestionUpdate(
            {
              key: 'question.key',
              fields: [
                { type: 'checkbox', key: 'question.key.checkbox.field' },
                { type: 'checkboxGroup', key: 'question.key.checkbox.group' },
                {
                  type: 'radioList',
                  key: 'question.key.radio.list',
                  options: [{ value: 'keyOne' }, { value: 'keyTwo' }],
                },
                {
                  type: FieldType.Complex,
                  key: 'question.key.complex',
                  subFields: [
                    { type: FieldType.Date, key: 'date', validation: dateField('date') },
                    { type: FieldType.Text, key: 'text' },
                  ],
                },
              ],
            } as ApplicationQuestion,
            {
              question: {
                key: {
                  checkbox: {
                    group: ['three', 'two', 'one'],
                    documents: ['id'],
                  },
                  text: 'answer',
                  complex: [{ date: '01/01/1990', text: 'data' }],
                  radio: { list: 'keyOne' },
                },
              },
              otherQuestion: {
                field: 'answer',
              },
            } as Partial<ApplicationAnswers>,
            {
              'question.key.checkbox.field': true,
              'question.key.checkbox.group': ['one', 'two'],
              'question.key.radio.list': 'keyOne',
              'question.key.complex': [
                { date: '01/01/1990', text: 'data' },
                { date: '01/02/1990', text: 'more data' },
              ],
            },
          ),
        ).toEqual({
          answers: {
            question: {
              key: {
                checkbox: {
                  field: true,
                  group: ['one', 'two'],
                  documents: ['id'],
                },
                complex: [
                  { date: '01/01/1990', text: 'data' },
                  { date: '01/02/1990', text: 'more data' },
                ],
                text: 'answer',
                radio: { list: 'keyOne' },
              },
            },
          },
          addresses: [],
          documents: [],
        });
      });
    });
  });

  describe('getQuestionFields', () => {
    it('should return fields that do not have type of Typography and Request', () => {
      expect(
        getQuestionFields([
          { key: 'keyOne', type: FieldType.Typography },
          { key: 'keyTwo' },
        ] as Field[]),
      ).toEqual([{ key: 'keyTwo' }]);
    });
  });

  describe('isPrefilled', () => {
    it('should return true if any of the fields exist in the partner config, the applicant profile, and the answers', () => {
      expect(
        isPrefilled(
          [
            {
              key: 'documents.all',
              type: FieldType.Document,
            },
          ] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { documents: { all: 'documentId' } },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [
                  { key: 'documents.all', label: 'Supporting Documents', type: 'document' },
                ],
              },
            },
          ] as ApplicantProfileConfiguration[],
          { documents: { all: 'documentId' } } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });
    it('should return true if any of the plan fields exist in the partner config, the applicant profile, and the answers', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne', type: FieldType.Date }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { keyOne: 'Some Data' },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyOne' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          { keyOne: 'A Value' } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });
    it('should return true if any of the fields exist in the partner config, the applicant profile, and the answers', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne' }, { key: 'keyTwo' }, { key: 'keyThree' }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { keyTwo: 'Data Two', keyThree: 1800 },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyTwo' }, { key: 'keyThree' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyTwo: 'A Value',
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
      expect(
        isPrefilled(
          [{ key: 'keyOne' }, { key: 'keyTwo' }, { key: 'keyThree' }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { keyThree: ['array', 'data'] },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyTwo' }, { key: 'keyThree' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyTwo: 'A Value',
            keyThree: ['array', 'data'],
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });
    it('should return false if none of the fields exist in the partner config', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne' }, { key: 'keyTwo' }, { key: 'keyThree' }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { keyTwo: 'Data Here', keyThree: ['Array', 'Data'] },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyFour' }, { key: 'keyFive' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyTwo: 'A Value',
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(false);
    });
    it('should return false if none of the fields exist in the applicant profile', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne' }, { key: 'keyTwo' }, { key: 'keyThree' }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { keyFour: 'Data Here', keyFive: ['Array', 'Data'] },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyTwo' }, { key: 'keyThree' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyTwo: 'A Value',
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(false);
    });
    it('should return false if none of the fields exist in the answers', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne' }, { key: 'keyTwo' }, { key: 'keyThree' }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: [{ key: 'keyTwo' }, { key: 'keyThree' }],
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyTwo' }, { key: 'keyThree' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyFour: 'A Value',
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(false);
    });
    it('should return false the applicantProfile is undefined', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne' }, { key: 'keyTwo' }, { key: 'keyThree' }] as Field[],
          undefined as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyTwo' }, { key: 'keyThree' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyFour: 'A Value',
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(false);
    });
    it('should return false if the partner config is undefined', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne' }, { key: 'keyTwo' }, { key: 'keyThree' }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: [{ key: 'keyTwo' }, { key: 'keyThree' }],
          } as ApplicantProfile,
          undefined as ApplicantProfileConfiguration[],
          {
            keyFour: 'A Value',
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(false);
    });
    it('should append "Id" to the field key when matching to the profile answers if it is an address', () => {
      expect(
        isPrefilled(
          [{ key: 'test.address', type: FieldType.Address }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { 'test.addressId': 'mockAddressId', keyThree: 'Data Three' },
            addresses: [
              { id: 'mockAddressId', addressLine1: '1000 Ridge Ave', city: 'Philadelphia' },
            ],
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'test.address' }, { key: 'keyThree' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyFour: 'A Value',
            test: { addressId: 'mockAddressId' },
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });
    it('should check only that the key is included in the profileKeys if the field is complex', () => {
      expect(
        isPrefilled(
          [{ key: 'keyOne', type: 'complex' }] as Field[],
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { keyOne: [{ complex: 'A' }, { complex: 'B' }] },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: {
                profileKeys: [{ key: 'keyOne' }],
              },
            },
          ] as ApplicantProfileConfiguration[],
          {
            keyOne: [{ complex: 'A' }, { complex: 'B' }],
            aDifferentKey: 'An unimportant value',
          } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });
    describe('Mailing Address field', () => {
      it('should return true if Mailing Address exists in the applicant profile and the answers', () => {
        expect(
          isPrefilled(
            [{ type: FieldType.Address, key: 'address.mailingAddress' }] as Field[],
            {
              applicantType: { id: 'mockApplicantTypeId' },
              mailingAddress: { id: 'addressId', city: 'city' },
            } as ApplicantProfile,
            [
              {
                applicantTypeId: 'mockApplicantTypeId',
                config: {
                  profileKeys: [],
                },
              },
            ] as ApplicantProfileConfiguration[],
            {
              address: { mailingAddressId: 'addressId' },
            } as unknown as ApplicationAnswers,
          ),
        ).toBe(true);
      });
      it('should return false if Mailing Address does not exist in the applicant profile or the answers', () => {
        expect(
          isPrefilled(
            [{ type: FieldType.Address, key: 'address.mailingAddress' }] as Field[],
            {
              applicantType: { id: 'mockApplicantTypeId' },
              mailingAddress: undefined,
            } as ApplicantProfile,
            [
              {
                applicantTypeId: 'mockApplicantTypeId',
                config: {
                  profileKeys: [],
                },
              },
            ] as ApplicantProfileConfiguration[],
            {
              address: { mailingAddressId: 'addressId' },
            } as unknown as ApplicationAnswers,
          ),
        ).toBe(false);
        expect(
          isPrefilled(
            [{ type: FieldType.Address, key: 'address.mailingAddress' }] as Field[],
            {
              applicantType: { id: 'mockApplicantTypeId' },
              mailingAddress: { id: 'addressId', city: 'city' },
            } as ApplicantProfile,
            [
              {
                applicantTypeId: 'mockApplicantTypeId',
                config: {
                  profileKeys: [],
                },
              },
            ] as ApplicantProfileConfiguration[],
            {} as unknown as ApplicationAnswers,
          ),
        ).toBe(false);
      });
    });
  });

  describe('isQuestionGroupEnabled', () => {
    it('returns true if the field has no condition', () => {
      expect(isQuestionGroupEnabled({} as ApplicationQuestionGroup, {})).toBe(true);
    });

    it('returns true if the field has a condition that evaluates to true', () => {
      expect(
        isQuestionGroupEnabled(
          {
            dynamicLogic: literal(true),
          } as ApplicationQuestionGroup,
          { answer: 'value' } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });

    it('returns false if the field has a condition that evaluates to false', () => {
      expect(
        isQuestionGroupEnabled(
          {
            dynamicLogic: literal(false),
          } as ApplicationQuestionGroup,
          { answer: 'value' } as unknown as ApplicationAnswers,
        ),
      ).toBe(false);
    });
  });

  describe('isQuestionEnabled', () => {
    it('returns true if the field has no condition', () => {
      expect(isQuestionEnabled({} as ApplicationQuestion, {})).toBe(true);
    });

    it('returns true if the field has a condition that evaluates to true', () => {
      expect(
        isQuestionEnabled(
          { dynamicLogic: literal(true) } as ApplicationQuestion,
          { answer: 'value' } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });

    it('returns false if the field has a condition that evaluates to false', () => {
      expect(
        isQuestionEnabled(
          { dynamicLogic: literal(false) } as ApplicationQuestion,
          { answer: 'value' } as unknown as ApplicationAnswers,
        ),
      ).toBe(false);
    });

    it('uses the answers to evaluate the condition', () => {
      expect(
        isQuestionEnabled(
          { dynamicLogic: answer('answer') } as ApplicationQuestion,
          { answer: true } as unknown as ApplicationAnswers,
        ),
      ).toBe(true);
    });
  });
});
