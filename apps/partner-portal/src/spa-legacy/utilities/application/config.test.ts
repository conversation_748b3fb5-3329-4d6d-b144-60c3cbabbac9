import {
  type ApplicantProfile,
  type ApplicantProfileConfig,
  type ApplicantProfileConfiguration,
  type ApplicationConfig,
  type ApplicationSection,
  type ProfileKey,
} from '@bybeam/platform-types';
import {
  applicationHasPrefilledAnswers,
  getAllQuestionKeys,
  getQuestion,
  sectionHasPrefilledAnswers,
} from './config';

describe('config', () => {
  describe('getQuestion', () => {
    describe('when a question with the given key exists', () => {
      it('returns that question', () => {
        const question = { key: 'sectionOne.questionTwo' };
        expect(
          getQuestion(
            {
              sections: [
                {
                  key: 'sectionOne',
                  questionGroups: [
                    {
                      key: 'sectionOne.groupOne',
                      questions: [{ key: 'sectionOne.questionOne' }, question],
                    },
                  ],
                },
              ],
            } as ApplicationConfig,
            'sectionOne.questionTwo',
          ),
        ).toBe(question);
      });
    });

    describe('when no question exists with the given key', () => {
      it('returns undefined', () => {
        expect(
          getQuestion(
            {
              sections: [
                {
                  key: 'sectionOne',
                  questionGroups: [
                    {
                      key: 'sectionOne.groupOne',
                      questions: [
                        { key: 'sectionOne.questionOne' },
                        { key: 'sectionOne.questionTwo' },
                      ],
                    },
                  ],
                },
              ],
            } as ApplicationConfig,
            'sectionOne.questionThree',
          ),
        ).toBe(undefined);
      });
    });
  });

  describe('sectionHasPrefilledAnswers', () => {
    it("should return true if the section contains any key that is in both the partner profileKeys and the applicant's profile answers", () => {
      expect(
        sectionHasPrefilledAnswers(
          {
            questionGroups: [{ questions: [{ key: 'questionOne', fields: [{ key: 'keyOne' }] }] }],
          } as ApplicationSection,
          { answers: { keyOne: 'data' } } as ApplicantProfile,
          { profileKeys: [{ key: 'keyOne' }] } as ApplicantProfileConfig,
        ),
      ).toBe(true);
      expect(
        sectionHasPrefilledAnswers(
          {
            questionGroups: [{ questions: [{ key: 'questionOne', fields: [{ key: 'keyTwo' }] }] }],
          } as ApplicationSection,
          { answers: { keyTwo: ['array', 'data'] } } as ApplicantProfile,
          { profileKeys: [{ key: 'keyTwo' }] } as ApplicantProfileConfig,
        ),
      ).toBe(true);
      expect(
        sectionHasPrefilledAnswers(
          {
            questionGroups: [
              { questions: [{ key: 'questionOne', fields: [{ key: 'keyTwo', type: 'complex' }] }] },
            ],
          } as ApplicationSection,
          { answers: { keyTwo: [{ complex: 'array' }, { complex: 'data' }] } } as ApplicantProfile,
          { profileKeys: [{ key: 'keyTwo' }] } as ApplicantProfileConfig,
        ),
      ).toBe(true);
    });
    it("should return false if the section does not contains any key in both the partner profileKeys and the applicant's profile answers", () => {
      expect(
        sectionHasPrefilledAnswers(
          {
            questionGroups: [{ questions: [{ key: 'questionOne', fields: [{ key: 'keyOne' }] }] }],
          } as ApplicationSection,
          { answers: { keyTwo: 'data' } } as ApplicantProfile,
          { profileKeys: [{ key: 'keyTwo' }] } as ApplicantProfileConfig,
        ),
      ).toBe(false);
    });
    it('should return false if the applicant profile is undefined', () => {
      expect(
        sectionHasPrefilledAnswers(
          {
            questionGroups: [{ questions: [{ fields: [{ key: 'keyOne' }] }] }],
          } as ApplicationSection,
          undefined as ApplicantProfile,
          { profileKeys: [{ key: 'keyFour' }] } as ApplicantProfileConfig,
        ),
      ).toBe(false);
    });
    it('should return false if the partner config is undefined', () => {
      expect(
        sectionHasPrefilledAnswers(
          {
            questionGroups: [{ questions: [{ key: 'questionOne', fields: [{ key: 'keyOne' }] }] }],
          } as ApplicationSection,
          { answers: [{ key: 'keyFour' }] } as ApplicantProfile,
          undefined as ApplicantProfileConfig,
        ),
      ).toBe(false);
    });
    it("should map 'Id' onto the section keys if looking for an address", () => {
      expect(
        sectionHasPrefilledAnswers(
          {
            questionGroups: [{ questions: [{ fields: [{ key: 'test.address' }] }] }],
          } as ApplicationSection,
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { 'test.addressId': 'mockAddressId' },
          } as ApplicantProfile,
          { profileKeys: [{ key: 'test.address' }] } as ApplicantProfileConfig,
        ),
      ).toBe(true);
    });
  });

  describe('applicationHasPrefilledAnswers', () => {
    it('should return false if there are no sections in the application config', () => {
      expect(
        applicationHasPrefilledAnswers(
          {} as ApplicationConfig,
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { commonKey: 'Here is common data' },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: { profileKeys: [{ key: 'commonKey' }] as ProfileKey[] },
            },
          ] as ApplicantProfileConfiguration[],
        ),
      ).toBe(false);
    });
    it('should return false if no sections contain a field sharing a key with the profile answers and the partner profile keys', () => {
      expect(
        applicationHasPrefilledAnswers(
          {
            sections: [
              { questionGroups: [{ questions: [{ fields: [{ key: 'nonSharedKey' }] }] }] },
            ],
          } as ApplicationConfig,
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { commonKey: 'Here is common data' },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: { profileKeys: [{ key: 'commonKey' }] as ProfileKey[] },
            },
          ] as ApplicantProfileConfiguration[],
        ),
      ).toBe(false);
    });
    it('should return true if at least one section contains a field sharing a key with the profile answers and the partner profile keys', () => {
      expect(
        applicationHasPrefilledAnswers(
          {
            sections: [{ questionGroups: [{ questions: [{ fields: [{ key: 'commonKey' }] }] }] }],
          } as ApplicationConfig,
          {
            applicantType: { id: 'mockApplicantTypeId' },
            answers: { commonKey: 'Here is common data' },
          } as ApplicantProfile,
          [
            {
              applicantTypeId: 'mockApplicantTypeId',
              config: { profileKeys: [{ key: 'commonKey' }] as ProfileKey[] },
            },
          ] as ApplicantProfileConfiguration[],
        ),
      ).toBe(true);
    });
  });

  describe('getAllQuestionKeys', () => {
    it('should return a flattened array of the question keys', () => {
      expect(
        getAllQuestionKeys({
          sections: [
            {
              questionGroups: [
                { questions: [{ fields: [{ key: 'keyA' }, { key: 'keyB' }] }] },
                { questions: [{ fields: [{ key: 'keyC' }] }] },
              ],
            },
          ],
        } as unknown as ApplicationConfig),
      ).toEqual(['keyA', 'keyB', 'keyC']);
    });
  });
});
