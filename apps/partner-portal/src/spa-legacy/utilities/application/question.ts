import { getApplicantProfileConfig } from '@/app/_utils/applicantProfile';
import { type GenericFormData, type ValidationConfig, optionalField } from '@/app/hooks/useForm';
import splitArrayKey from '@bybeam/platform-lib/answers/splitArrayKey';
import {
  type ApplicantProfile,
  type ApplicantProfileConfiguration,
  type Application,
  type ApplicationAnswers,
  type ApplicationContentInput,
  type ApplicationQuestion,
  type ApplicationQuestionGroup,
  ApplicationQuestionKeys,
  type Field,
  FieldType,
} from '@bybeam/platform-types';
import { flatten } from 'flat';
import { evaluate } from './ast/evaluate';
import {
  getAddressAnswers,
  getFieldAnswersValue,
  getFieldFormInit,
  getFieldUpdate,
  makeSkipIndicator,
} from './fields';
import mergeUpdates from './mergeUpdates';

export type ApplicationMode = 'new' | 'edit';

// Returns the parameters for the `useForm` hook initialization for the given question.
// Merge the initial state and validation for each field in the question to produce
// a single initial state/validation that encompasses all the fields.
export function getQuestionFormInit<FormData extends GenericFormData>(
  question: ApplicationQuestion,
  answers: Partial<ApplicationAnswers>,
  application: Application,
): [FormData, ValidationConfig<FormData>] {
  const skippedKey = [question.key, 'skipped'].join('.');
  const skipped = !!getFieldAnswersValue(skippedKey, answers);
  return question.fields.reduce(
    ([data, config], field) => {
      const [fieldData, fieldConfig] = getFieldFormInit(field, skipped ? {} : answers, application);
      return [
        {
          ...data,
          ...fieldData,
          ...(question.skippable && { [skippedKey]: skipped }),
        },
        {
          ...config,
          ...fieldConfig,
          ...(question.skippable && { [skippedKey]: optionalField() }),
        },
      ];
    },
    [{} as FormData, {}],
  );
}

// Given the useForm data for the question, returns the partial of the answers to
// be updated. Returns the full structure of the answers at the top level key of the
// question (e.g., if the question key is financialHardship.experience, it would
// merge the complete existing tree at answers.financialHardship). This means it is
// important that the keys for all of the question's fields have the same top-level
// key as the question, to avoid accidentally overwriting any existing answers.
export function getQuestionUpdate<FormData extends GenericFormData>(
  question: ApplicationQuestion,
  answers: Partial<ApplicationAnswers>,
  update: FormData,
): ApplicationContentInput {
  const highestKey = question.key.split('.')[0];
  const fields: Field[] = question.skippable
    ? [...question.fields, makeSkipIndicator(question.key)]
    : question.fields;
  return mergeUpdates([
    { answers: { [highestKey]: answers[highestKey] } },
    ...fields.map((field) => getFieldUpdate(field, update)),
  ]);
}

export function getQuestionFields(fields: Field[]): Field[] {
  return fields.filter((field) => FieldType.Typography !== field.type);
}

export function isPrefilled(
  fields: Field[],
  applicantProfile: ApplicantProfile,
  applicantProfileConfigs: ApplicantProfileConfiguration[],
  answers: Partial<ApplicationAnswers>,
) {
  const addressKeyRegex = /address/gi;
  const applicantProfileConfig = getApplicantProfileConfig(
    applicantProfileConfigs,
    applicantProfile,
  );

  return getQuestionFields(fields).some((field) => {
    let partnerConfigMatch = !!applicantProfileConfig?.profileKeys?.find(
      ({ key }) => key === field.key,
    );

    let applicantProfileMatch = undefined;
    let presentInAnswers = undefined;

    const profileKeys = Object.keys(
      // biome-ignore lint/suspicious/noExplicitAny: only considering the keys
      flatten<ApplicationAnswers, Record<string, any>[]>(applicantProfile?.answers ?? {}),
    ).map((key) => splitArrayKey(key)?.key);

    if (field.key.match(addressKeyRegex) && field.type === FieldType.Address) {
      let addresses = undefined;
      if (`${field.key}Id` === ApplicationQuestionKeys.MailingAddress) {
        partnerConfigMatch = true;
        applicantProfileMatch = !!applicantProfile.mailingAddress?.id;
        addresses = applicantProfile.mailingAddress ? [applicantProfile.mailingAddress] : [];
      } else {
        applicantProfileMatch = !!profileKeys.find((key) => key === `${field.key}Id`);
        addresses = applicantProfile.addresses;
      }
      presentInAnswers = !!getAddressAnswers(field, answers, addresses)?.[`${field.key}.city`];
    } else if (field.type === FieldType.Complex) {
      applicantProfileMatch = !!profileKeys.find((key) => key.includes(field.key));
      presentInAnswers = !!getFieldAnswersValue(field.key, answers);
    } else {
      applicantProfileMatch = !!profileKeys.find((key) => key === field.key);
      presentInAnswers = !!getFieldAnswersValue(field.key, answers);
    }

    return partnerConfigMatch && applicantProfileMatch && presentInAnswers;
  });
}

export function isQuestionGroupEnabled(
  group: ApplicationQuestionGroup,
  answers: Partial<ApplicationAnswers>,
): boolean {
  return !group.dynamicLogic || evaluate(group.dynamicLogic, answers);
}

export function isQuestionEnabled(
  question: ApplicationQuestion,
  answers: Partial<ApplicationAnswers>,
): boolean {
  return !question.dynamicLogic || evaluate(question.dynamicLogic, answers);
}
