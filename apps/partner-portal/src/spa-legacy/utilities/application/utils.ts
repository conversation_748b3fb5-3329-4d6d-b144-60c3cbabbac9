import { Key } from '@bybeam/platform-types';

const isEmptyField = (value): boolean => {
  return (
    [undefined, null, ''].includes(value) ||
    (Array.isArray(value) && value.length === 0) ||
    value?.address?.city === ''
  );
};

export function getLabelProps(
  readOnly: boolean,
  // biome-ignore lint/suspicious/noExplicitAny: reason
  value: any,
): { shrink: boolean; focused: boolean } | undefined {
  if (readOnly && isEmptyField(value)) return { shrink: false, focused: false };
  return undefined;
}

export const sortByAlphabetical = <T>(property: string, array: T[]): T[] =>
  array.sort((a: T, b: T) => a[property].localeCompare(b[property]));

// Strips '.' characters from a key and replaces them with '-'
export function safeKey(key: Key = ''): string {
  return key.replace(/\./g, '-');
}
