import { sortByAlphabetical } from './utils';

describe('sortByAlphabetical', () => {
  it('should sort objects by property in alphabetical order', () => {
    const groceries = [
      { name: 'Apple', price: 1 },
      { name: 'Cantaloupe', price: 3 },
      { name: 'Banana', price: 2 },
    ];
    expect(sortByAlphabetical('name', groceries)).toEqual([
      { name: 'Apple', price: 1 },
      { name: 'Banana', price: 2 },
      { name: 'Cantaloupe', price: 3 },
    ]);
  });

  it('should sort objects by property in alphabetical order, even when the value includes numbers', () => {
    const funds = [{ name: 'ERA 3' }, { name: 'ERA 1' }, { name: 'ERA 2' }];
    expect(sortByAlphabetical('name', funds)).toEqual([
      { name: 'ERA 1' },
      { name: 'ERA 2' },
      { name: 'ERA 3' },
    ]);
  });
});
