import { checkboxField, dateField } from '@/app/hooks/useForm';
import {
  type Application,
  type ApplicationAnswers,
  type ApplicationSection,
  FieldType,
} from '@bybeam/platform-types';
import { literal } from './ast/utils';
import { getSectionFormInit, getSectionUpdate } from './section';

describe('section', () => {
  describe('getSectionAnswers', () => {
    describe('when there are no skippable questions', () => {
      it('merges all of the field answers and the top level of each question', () => {
        expect(
          getSectionUpdate(
            {
              questionGroups: [
                {
                  questions: [
                    {
                      key: 'questionOne.firstKey',
                      fields: [
                        { type: 'checkbox', key: 'questionOne.firstKey.checkbox.field' },
                        { type: 'checkboxGroup', key: 'questionOne.firstKey.checkbox.group' },
                      ],
                    },
                    {
                      key: 'questionOne.secondKey',
                      fields: [
                        {
                          type: 'radioList',
                          key: 'questionOne.secondKey.radio.list',
                          options: [{ value: 'one' }, { value: 'two' }],
                        },
                      ],
                    },
                  ],
                },
                {
                  questions: [
                    {
                      key: 'questionTwo.firstKey',
                      fields: [{ type: 'checkbox', key: 'questionTwo.firstKey.checkbox' }],
                    },
                  ],
                },
                {
                  questions: [
                    {
                      key: 'questionThree.firstKey',
                      fields: [
                        {
                          type: FieldType.Complex,
                          key: 'questionThree.complex',
                          subFields: [
                            { type: FieldType.Date, key: 'date', validation: dateField('date') },
                            { type: FieldType.Text, key: 'text' },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            } as ApplicationSection,
            {
              questionOne: {
                firstKey: { radio: { list: 'one' } },
              },
              questionTwo: {
                text: 'answer',
              },
              questionThree: {
                complex: [{ date: '01/01/1990', text: 'data' }],
              },
            } as Partial<ApplicationAnswers>,
            {
              'questionOne.firstKey.checkbox.field': true,
              'questionOne.firstKey.checkbox.group': ['one'],
              'questionOne.secondKey.radio.list': 'two',
              'questionTwo.firstKey.checkbox': true,
              'questionThree.complex': [
                { date: '01/01/1990', text: 'data' },
                { date: '01/02/1990', text: 'other' },
              ],
            },
          ),
        ).toEqual({
          answers: {
            questionOne: {
              firstKey: {
                checkbox: { field: true, group: ['one'] },
                radio: { list: 'one' },
              },
              secondKey: { radio: { list: 'two' } },
            },
            questionTwo: {
              firstKey: { checkbox: true },
              text: 'answer',
            },
            questionThree: {
              complex: [
                { date: '01/01/1990', text: 'data' },
                { date: '01/02/1990', text: 'other' },
              ],
            },
          },
          addresses: [],
          documents: [],
        });
      });
    });
    describe('when there are skippable questions', () => {
      it('adds skipped and sets the fields to undefined when a question is skipped', () => {
        expect(
          getSectionUpdate(
            {
              questionGroups: [
                {
                  questions: [
                    {
                      key: 'questionOne.firstKey',
                      fields: [
                        { type: 'checkbox', key: 'questionOne.firstKey.checkbox.field' },
                        { type: 'checkboxGroup', key: 'questionOne.firstKey.checkbox.group' },
                      ],
                    },
                    {
                      key: 'questionOne.secondKey',
                      skippable: true,
                      fields: [
                        {
                          type: 'radioList',
                          key: 'questionOne.secondKey.radio.list',
                          options: [{ value: 'one' }, { value: 'two' }],
                        },
                      ],
                    },
                  ],
                },
              ],
            } as ApplicationSection,
            {
              questionOne: { firstKey: { radio: { list: 'one' } } },
            } as Partial<ApplicationAnswers>,
            {
              'questionOne.firstKey.checkbox.field': true,
              'questionOne.firstKey.checkbox.group': ['one'],
              'questionOne.secondKey.skipped': true,
            },
          ),
        ).toEqual({
          answers: {
            questionOne: {
              firstKey: {
                checkbox: { field: true, group: ['one'] },
                radio: { list: 'one' },
              },
              secondKey: {
                radio: { list: undefined },
                skipped: true,
              },
            },
          },
          addresses: [],
          documents: [],
        });
      });
      it('set skip to false and populates the fields when a question is unskipped', () => {
        expect(
          getSectionUpdate(
            {
              questionGroups: [
                {
                  questions: [
                    {
                      key: 'questionOne.firstKey',
                      skippable: true,
                      fields: [
                        { type: 'checkbox', key: 'questionOne.firstKey.checkbox.field' },
                        { type: 'checkboxGroup', key: 'questionOne.firstKey.checkbox.group' },
                      ],
                    },
                  ],
                },
              ],
            } as ApplicationSection,
            {
              questionOne: {
                firstKey: {
                  skipped: true,
                },
              },
            } as Partial<ApplicationAnswers>,
            {
              'questionOne.firstKey.skipped': false,
              'questionOne.firstKey.checkbox.field': true,
              'questionOne.firstKey.checkbox.group': ['one'],
            },
          ),
        ).toEqual({
          answers: {
            questionOne: {
              firstKey: {
                skipped: false,
                checkbox: { field: true, group: ['one'] },
              },
            },
          },
          addresses: [],
          documents: [],
        });
      });
    });
  });

  describe('getSectionFormInit', () => {
    it('merges all of the question form inits, making each conditional on whether the question is enabled or is skipped', () => {
      const [formData, validationConfig] = getSectionFormInit(
        {
          questionGroups: [
            {
              questions: [
                {
                  key: 'questionOne',

                  fields: [{ key: 'questionOne.key', type: 'checkbox' }],
                },
              ],
            },
            {
              questions: [
                {
                  key: 'questionTwo',
                  dynamicLogic: literal(false),
                  fields: [
                    { key: 'questionTwo.key', type: 'radioList', options: [{ value: 'one' }] },
                  ],
                },
              ],
            },
            {
              questions: [
                {
                  key: 'questionThree',
                  dynamicLogic: literal(false),
                  fields: [
                    { key: 'questionThree.key', type: 'radioList', options: [{ value: 'one' }] },
                  ],
                },
              ],
            },
          ],
        } as ApplicationSection,
        {
          questionOne: { key: true },
          questionTwo: { key: undefined },
          questionThree: { skipped: true },
        } as Partial<ApplicationAnswers>,
        {} as Application,
      );

      expect(formData).toEqual({ 'questionOne.key': true, 'questionTwo.key': undefined });
      expect(validationConfig).toEqual({
        'questionOne.key': {
          ...checkboxField('questionOne.key'),
          fieldIsPresent: expect.any(Function),
          condition: expect.any(Function),
        },
        'questionTwo.key': {
          type: 'simple',
          required: true,
          requiredErrorMessage: 'A selection is required',
          validate: undefined,
          condition: expect.any(Function),
        },
        'questionThree.key': {
          type: 'simple',
          required: true,
          requiredErrorMessage: 'A selection is required',
          validate: undefined,
          condition: expect.any(Function),
        },
      });
      expect(validationConfig['questionOne.key'].condition({})).toBe(true);
      expect(validationConfig['questionTwo.key'].condition({})).toBe(false);
      expect(validationConfig['questionThree.key'].condition({})).toBe(false);
    });

    describe('When a question group is Disabled', () => {
      it('CONDITION of every question inside the question group must return false', () => {
        const [_formData, validationConfig] = getSectionFormInit(
          {
            questionGroups: [
              {
                questions: [
                  {
                    key: 'questionOne',

                    fields: [{ key: 'questionOne.key', type: 'checkbox' }],
                  },
                ],
              },
              {
                dynamicLogic: literal(false),
                questions: [
                  {
                    key: 'questionTwo1',
                    fields: [
                      { key: 'questionTwo1.key', type: 'radioList', options: [{ value: 'one' }] },
                      { key: 'questionTwo2.key', type: 'radioList', options: [{ value: 'two' }] },
                    ],
                  },
                ],
              },
              {
                questions: [
                  {
                    key: 'questionThree',
                    dynamicLogic: literal(false),
                    fields: [
                      { key: 'questionThree.key', type: 'radioList', options: [{ value: 'one' }] },
                    ],
                  },
                ],
              },
            ],
          } as ApplicationSection,
          {
            questionOne: { key: true },
            questionTwo: { key: 'one' },
            questionThree: { skipped: true },
          } as Partial<ApplicationAnswers>,
          {} as Application,
        );

        expect(validationConfig['questionOne.key'].condition({})).toBe(true);
        expect(validationConfig['questionTwo1.key'].condition({})).toBe(false);
        expect(validationConfig['questionTwo2.key'].condition({})).toBe(false);
        expect(validationConfig['questionThree.key'].condition({})).toBe(false);
      });
    });

    describe('When a question group is Enabled but the question is Disabled', () => {
      it('CONDITION for every field must return false', () => {
        const [_formData, validationConfig] = getSectionFormInit(
          {
            questionGroups: [
              {
                questions: [
                  {
                    key: 'questionOne',

                    fields: [{ key: 'questionOne.key', type: 'checkbox' }],
                  },
                ],
              },
              {
                questions: [
                  {
                    key: 'questionTwo',
                    dynamicLogic: literal(false),
                    fields: [
                      { key: 'questionTwo1.key', type: 'radioList', options: [{ value: 'one' }] },
                      { key: 'questionTwo2.key', type: 'radioList', options: [{ value: 'two' }] },
                    ],
                  },
                ],
              },
              {
                questions: [
                  {
                    key: 'questionThree',
                    dynamicLogic: literal(false),
                    fields: [
                      { key: 'questionThree.key', type: 'radioList', options: [{ value: 'one' }] },
                    ],
                  },
                ],
              },
            ],
          } as ApplicationSection,
          {
            questionOne: { key: true },
            questionTwo1: { key: 'one' },
            questionTwo2: { key: undefined },
            questionThree: { skipped: true },
          } as Partial<ApplicationAnswers>,
          {} as Application,
        );
        expect(validationConfig['questionOne.key'].condition({})).toBe(true);
        expect(validationConfig['questionTwo1.key'].condition({})).toBe(false);
        expect(validationConfig['questionTwo2.key'].condition({})).toBe(false);
        expect(validationConfig['questionThree.key'].condition({})).toBe(false);
      });
    });
  });
});
