import type { GenericFormData, ValidationConfig } from '@/app/hooks/useForm';
import type {
  Application,
  ApplicationAnswers,
  ApplicationContentInput,
  ApplicationSection,
} from '@bybeam/platform-types';
import { getFieldUpdate, makeSkipIndicator } from './fields';
import mergeUpdates from './mergeUpdates';
import { getQuestionFormInit, isQuestionEnabled, isQuestionGroupEnabled } from './question';

// Given the useForm data for the section, returns the partial of the answers to be
// updated. Returns the full structure of the answers at the top level for each of the
// section's questions (e.g., for a section with questions financialHardship.experience
// and housingInstability.statement, it would merge in the complete existing tree for
// both answers.financialHardship and answers.housingInstability).
export function getSectionUpdate<FormData extends GenericFormData>(
  section: ApplicationSection,
  answers: Partial<ApplicationAnswers>,
  update: FormData,
): ApplicationContentInput {
  const allQuestions = section.questionGroups.flatMap(({ questions }) => questions);
  const fieldUpdates = allQuestions
    .flatMap(({ fields, skippable, key }) =>
      skippable ? [...fields, makeSkipIndicator(key)] : fields,
    )
    .map((field) => getFieldUpdate(field, update));
  const baseKeys = Array.from(new Set(allQuestions.map(({ key }) => key.split('.')[0])));
  const baseAnswers = Object.fromEntries(baseKeys.map((key) => [key, answers[key]]));
  return mergeUpdates([{ answers: baseAnswers }, ...fieldUpdates]);
}

// Returns the parameters for the `useForm` hook initialization for the given section.
// It merges the initial state and validation for each question in the section to produce
// a single initial state/validation that encompasses all the questions.
//
// Additionally adds additional configuration to the validation to mark fields as conditionally
// required depending on whether the questions are enabled or not (i.e., accounting for
// automated skip logic). This behavior has a couple of known gaps that likely will need to
// be addressed as additional questions and field types are built out:
// - If a field already has conditional logic, that will be overridden by the logic checking
//   if the question is enabled. Instead, it should check both conditions.
// - If a field has validation beyond just required or not, it will still run even if
//   the condition is not met. This could lead to an edge case where a field that had an
//   invalid value before the field was marked as not enabled could prevent the overall
//   validation from passing
export function getSectionFormInit<FormData extends GenericFormData>(
  section: ApplicationSection,
  answers: Partial<ApplicationAnswers>,
  application: Application,
): [FormData, ValidationConfig<FormData>] {
  return section.questionGroups
    .flatMap(({ questions }) => questions)
    .reduce(
      ([data, config], question) => {
        const [questionData, questionConfig] = getQuestionFormInit(question, answers, application);
        return [
          { ...data, ...questionData },
          {
            ...config,
            ...Object.fromEntries(
              Object.keys(questionData).map((key) => [
                key,
                {
                  type: 'simple',
                  ...questionConfig[key],
                  condition: (formData): boolean => {
                    const questionGroup = section.questionGroups.find(({ questions }) =>
                      questions.find(({ key: qKey }) => qKey === question.key),
                    );
                    const enabled =
                      isQuestionGroupEnabled(
                        questionGroup,
                        getSectionUpdate(section, answers, formData).answers,
                      ) &&
                      !formData[`${question.key}.skipped`] &&
                      isQuestionEnabled(
                        question,
                        getSectionUpdate(section, answers, formData).answers,
                      );
                    return questionConfig[key]?.condition
                      ? questionConfig[key].condition(formData) && enabled
                      : enabled;
                  },
                },
              ]),
            ),
          },
        ];
      },
      [{} as FormData, {}],
    );
}
