import mergeObjects from '@bybeam/platform-lib/answers/mergeObjects';
import {
  type ApplicationQuestion,
  type ApplicationStatus,
  CommonQuestionKeys,
  FieldType,
  StatusOverride,
  type StepsIntroPage,
  TextInputType,
} from '@bybeam/platform-types';
import STANDARD_COMPONENTS from '../standards';
import { makeEduConfig } from './baseConfig';

const CONFIG = makeEduConfig();

const DEFAULT_STATUSES: {
  [status in ApplicationStatus]: Pick<StatusOverride, 'label' | 'message'>;
} = {
  Application: {
    label: 'Apply for emergency assistance',
    message:
      'You could be found eligible to receive cash assistance up to {MAX_AMOUNT}. These funds do not require repayment and are intended to help people who are in high need of financial assistance.',
  },
  Approved: {
    label: 'Status: Application Decision',
    message:
      "You are approved to receive {APPROVED_AMOUNT}. A member of {PARTNER_NAME}'s staff will follow up with you on how you will receive these funds.",
  },
  Denied: {
    label: 'Status: Application Decision',
    message:
      'Unfortunately, {PARTNER_NAME} is unable to provide you with funds at this time. If you have any questions about this decision, you can contact {PARTNER_NAME} at {PARTNER_EMAIL} or {PARTNER_PHONE}.',
  },
  AwaitingReview: {
    label: 'Status: Awaiting Review',
    message:
      'Thank you for submitting your application! You will be notified once a case manager is assigned to review your application. Check back here for updates.',
  },
  InReview: {
    label: 'Status: In Review',
    message:
      "We're currently reviewing your application and will reach a decision shortly. You'll receive an email once a decision has been made and no further action is required on your part.",
  },
  InProgress: {
    label: 'Continue where you left off',
    message:
      "You've made great progress so far! Keep working on your application to receive emergency assistance for your household needs.",
  },
  Incomplete: {
    label: 'Continue Application',
    message: 'Your application is missing information.',
  },
  WithdrawnOpen: {
    label: 'Status: Withdrawn',
    message:
      'Your application was withdrawn due to inactivity. However, you can pick back up where you left off and submit your application. Please complete your application quickly so you can receive an eligibility determination sooner rather than later.',
  },
  WithdrawnClosed: {
    label: 'Status: Withdrawn',
    message:
      "Your application was withdrawn due to inactivity. If you'd like to resume your application, contact your case manager.",
  },
  Unknown: {
    label: 'Unknown',
    message: 'There was an error retrieving the status of your application.',
  },
};

const newQuestions: { [key: string]: ApplicationQuestion } = {
  affiliatedOrganizations: {
    key: 'affiliatedOrganization',
    copy: { title: 'Which organization are you affiliated with?' },
    fields: [
      {
        type: FieldType.Dropdown,
        validation: { required: true },
        key: 'affiliatedOrganization',
        copy: 'Organization you are affiliated with',
        options: [
          { label: '#OaklandUndivided', value: '#oaklandundivided' },
          { label: '10,000 Degrees *', value: '10,000degrees*' },
          { label: 'Braven Bay Area', value: 'bravenbayarea' },
          { label: 'Breakthrough Silicon Valley', value: 'breakthroughsiliconvalley' },
          { label: 'CA Competes', value: 'cacompetes' },
          { label: 'Central Valley Scholars', value: 'centralvalleyscholars' },
          { label: 'ECMC The College Place- Northern California', value: 'ecmcnortherncalifornia' },
          { label: 'Foundation for a College Education', value: 'foundationforacollegeeducation' },
          { label: 'Go Public Schools', value: 'gopublicschools' },
          { label: 'Hayward Promise Neighborhood', value: 'haywardpromiseneighborhood' },
          {
            label: 'Hispanic Foundation of Silicon Valley',
            value: 'hispanicfoundationofsiliconvalley',
          },
          { label: 'Improve Your Tomorrow', value: 'improveyourtomorrow' },
          { label: 'Junior Achievement NorCal', value: 'juniorachievementnorcal' },
          {
            label: 'KIPP Public Schools Northern California',
            value: 'kipppublicschoolsnortherncalifornia',
          },
          {
            label: 'Latino Education Advancement Foundation (LEAF)',
            value: 'latinoeducationadvancementfoundation(leaf)',
          },
          { label: 'LEAD Filipino', value: 'leadfilipino' },
          { label: 'Making Waves Education Foundation', value: 'makingwaveseducationfoundation' },
          { label: 'Mentor California', value: 'mentorcalifornia' },
          {
            label: 'Northern California College Promise Coalition',
            value: 'northcaliforniapromisecoalition',
          },
          { label: 'Oakland Promise *', value: 'oaklandpromise*' },
          { label: 'OneGoal Bay Area', value: 'onegoalbayarea' },
          { label: 'Rancho Cordova Promise', value: 'ranchocordovapromise' },
          {
            label: 'Reinvent Stockton Foundation/Stockton Scholars *',
            value: 'reinventstocktonfoundation/stocktonscholars*',
          },
          { label: 'Richmond Promise *', value: 'richmondpromise*' },
          { label: 'San Jose Promise', value: 'sanjosepromise' },
          { label: 'San Jose Public Library', value: 'sanjosepubliclibrary' },
          { label: 'Milpitas Promise', value: 'milpitaspromise' },
          { label: 'ScholarMatch', value: 'scholarmatch' },
          { label: 'ScholarPrep Nation', value: 'scholarprepnation' },
          {
            label: 'Silicon Valley Education Foundation',
            value: 'siliconvalleyeducationfoundation',
          },
          { label: 'Stockton Scholars', value: 'stocktonscholars' },
          { label: 'Students Rising Above *', value: 'studentsrisingabove*' },
          { label: 'Summit Public Schools', value: 'summitpublicschools' },
          { label: 'The Education Trust--West', value: 'theeducationtrust--west' },
          { label: 'The Peninsula College Fund', value: 'thepeninsulacollegefund' },
          { label: 'The Village Method', value: 'thevillagemethod' },
          { label: 'uAspire', value: 'uaspire' },
        ],
      },
    ],
  },

  // New questions added -What is your zip code? and What school are you attending?
  [CommonQuestionKeys.ZIP_CODE]: {
    key: CommonQuestionKeys.ZIP_CODE,
    copy: { title: 'What is your zip code?' },
    fields: [
      {
        type: FieldType.Text,
        inputType: TextInputType.Text,
        copy: 'Zip Code',
        key: 'zipCode',
      },
    ],
  },

  school: {
    key: 'school',
    copy: { title: 'What school are you attending?' },
    fields: [
      {
        type: FieldType.Text,
        inputType: TextInputType.Text,
        copy: 'School',
        key: 'school',
      },
    ],
  },
};

const personalInfoSection = CONFIG.sections.find(({ key }) => key === 'personalInfo');
const personalInfoQuestionGroup = personalInfoSection.questionGroups.find(
  ({ key }) => key === 'studentInfo',
);

personalInfoQuestionGroup.questions.unshift(newQuestions.school);
personalInfoQuestionGroup.questions.unshift(newQuestions.zipCode);
personalInfoQuestionGroup.questions.unshift(newQuestions.affiliatedOrganizations);

CONFIG.introPages[1] = mergeObjects(
  [
    STANDARD_COMPONENTS.definitions.introPages.ApplicationProcess,
    {
      steps: [
        {
          title: 'Attestation',
          icon: 'raisedHandOutlined',
          description:
            'By completing this application you agree to email communication from Northern California College Promise Coalition (NCCPC) regarding possible future opportunities to benefit you as a student.',
        },
      ],
    },
  ],
  'concat',
) as StepsIntroPage;

CONFIG.overrides = {
  statuses: Object.entries(DEFAULT_STATUSES).map(([status, { message }]) => ({
    status: status as ApplicationStatus,
    message: `${message} Additional resources can be found in the [NCCPC College Financial Literacy Toolkit](https://bit.ly/EM_Resources).`,
  })),
};

// Remove documents section
CONFIG.sections = CONFIG.sections.filter(({ key }) => key !== 'documents');

export default CONFIG;
