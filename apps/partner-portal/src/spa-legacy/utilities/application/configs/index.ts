import assertExhaustive from '@bybeam/platform-lib/utilities/assertExhaustive';
import {
  type ApplicantType,
  ApplicantTypeRole,
  type ApplicationConfig,
  ApplicationConfigurationKey,
  type Program,
  type User,
} from '@bybeam/platform-types';
import baltimoreConfig from './baltimore/config';
import batonConfig from './batonConfig';
import celledT1Config from './calgrows/careerBuilder';
import celledT2Config from './calgrows/learnAndEarn';
import hcaiConfig from './caring4cal/hcaiConfig';
import hcaiT1Config from './caring4cal/hcaiTrack1';
import hcaiT2Config from './caring4cal/hcaiTrack2';
import childcareConfig from './demoEnv/childcare';
import multipartyConfig from './demoEnv/multiparty';
import workforceConfig from './demoEnv/workforce';
import eduAddressConfig from './eduPartners/addressConfig';
import eduConfig from './eduPartners/baseConfig';
import comptonDualConfig from './eduPartners/comptonDualConfig';
import eduEmergency from './eduPartners/dcpsEmergency';
import eduMicrogrant from './eduPartners/dcpsMicrogrant';
import delmarConfig from './eduPartners/delmarConfig';
import eduECMC from './eduPartners/ecmcConfig';
import lcccConfig from './eduPartners/lcccConfig';
import matcConfig from './eduPartners/matcConfig';
import mecConfig from './eduPartners/mecConfig';
import minnesotaConfig from './eduPartners/minnesotaConfig';
import nccpcConfig from './eduPartners/nccpcConfig';
import eduNoDocs from './eduPartners/noDocsConfig';
import oppnetConfig from './eduPartners/oppnetConfig';
import eduPG from './eduPartners/pgConfig';
import fairfaxConfig from './fairfax/config';
import fairfaxEnrollConfig from './fairfax/enrollmentConfig';
import kc1MbbConfig from './kc/1mbbConfig';
import kcBizcareConfig from './kc/bizCareConfig';
import kcEDCConfig from './kc/edcConfig';
import kcKivaConfig from './kc/kivaConfig';
import kcLidoConfig from './kc/lidoConfig';
import kcSMBCapitalConfig from './kc/smbCapitalConfig';
import kcSMBGrantConfig from './kc/smbGrantConfig';
import kcSMBIDAConfig from './kc/smbIDAConfig';
import kcSMBLoanPrepConfig from './kc/smbLoanPrepConfig';
import nhConfig from './nh/config';
import tppNYCConfig from './tppNYCConfig';
import tppPhillyConfig from './tppPhillyConfig';

export function getFirstPartyApplicationConfig(program: Program): ApplicationConfig {
  const firstPartyApplicantType = program.applicantTypes?.find(
    ({ applicantType: { role } }) => role === ApplicantTypeRole.FirstParty,
  );
  return getApplicationConfiguration(program, firstPartyApplicantType.applicantType);
}

export function getApplicationConfiguration(program: Program, user: User): ApplicationConfig;
export function getApplicationConfiguration(
  program: Program,
  applicantType: ApplicantType,
): ApplicationConfig;
export function getApplicationConfiguration(
  program: Program,
  user: User | ApplicantType,
): ApplicationConfig {
  const applicantType =
    user && 'applicantProfile' in user ? user.applicantProfile?.applicantType : user;

  if (!program.applicationConfigurations?.length) return getFallbackAppConfig(program);
  const config = program.applicationConfigurations.find(
    ({ applicantTypeId }) => applicantTypeId === applicantType.id,
  );
  return config.configuration;
}

// TODO: Deprecate this.  Ensure all programs have an applicationConfiguration set via sorcery.
function getFallbackAppConfig(program: Program): ApplicationConfig {
  // Handle programs without an applicationConfiguration set
  // This can happen in staging/test environments or for misconfigured programs
  if (!program.config?.applicationConfiguration) {
    // console.warn is intentional here to help identify misconfigured programs in non-prod environments
    // without breaking the application. In production, these programs typically won't be accessed.
    console.warn(
      `Program "${program.name}" (${program.id}) has no applicationConfiguration set. Using minimal fallback config.`,
    );
    return {
      introPages: [],
      sections: [],
    };
  }

  switch (program.config.applicationConfiguration) {
    // Beam Demo Configs
    case ApplicationConfigurationKey.DEMO_CHILDCARE:
    case ApplicationConfigurationKey.WESTSIDE_COUNTY_V1:
      return childcareConfig;
    case ApplicationConfigurationKey.DEMO_WORKFORCE:
    case ApplicationConfigurationKey.WESTSIDE_COUNTY_V2:
      return workforceConfig;
    case ApplicationConfigurationKey.DEMO_MULTIPARTY:
      return multipartyConfig;

    // Government Partners
    case ApplicationConfigurationKey.BATON:
      return batonConfig;
    case ApplicationConfigurationKey.BALTIMORE:
      return baltimoreConfig;
    case ApplicationConfigurationKey.CELL_ED_T1:
      return celledT1Config;
    case ApplicationConfigurationKey.CELL_ED_T2:
      return celledT2Config;
    case ApplicationConfigurationKey.EDC_KC:
      return kcEDCConfig;
    case ApplicationConfigurationKey.FAIRFAX:
      return fairfaxConfig;
    case ApplicationConfigurationKey.FAIRFAX_ENROLL:
      return fairfaxEnrollConfig;
    case ApplicationConfigurationKey.HCAI:
      return hcaiConfig;
    case ApplicationConfigurationKey.HCAI_T1:
      return hcaiT1Config;
    case ApplicationConfigurationKey.HCAI_T2:
      return hcaiT2Config;
    case ApplicationConfigurationKey.KC_BIZCARE:
      return kcBizcareConfig;
    case ApplicationConfigurationKey.NH:
      return nhConfig;
    case ApplicationConfigurationKey.KC_SMB_CAPITAL:
      return kcSMBCapitalConfig;
    case ApplicationConfigurationKey.KC_SMB_GRANT:
      return kcSMBGrantConfig;
    case ApplicationConfigurationKey.KC_SMB_IDA:
      return kcSMBIDAConfig;
    case ApplicationConfigurationKey.KC_SMB_LOAN_PREP:
      return kcSMBLoanPrepConfig;
    case ApplicationConfigurationKey.KC_KIVA:
      return kcKivaConfig;
    case ApplicationConfigurationKey.KC_1MBB:
      return kc1MbbConfig;
    case ApplicationConfigurationKey.KC_LIDO:
      return kcLidoConfig;
    case ApplicationConfigurationKey.TPP_PHILLY:
      return tppPhillyConfig;
    case ApplicationConfigurationKey.TPP_NYC:
      return tppNYCConfig;

    // EDU/Philanthropy Partners
    case ApplicationConfigurationKey.EDU:
      return eduConfig;
    case ApplicationConfigurationKey.EDU_ADDRESS:
      return eduAddressConfig;
    case ApplicationConfigurationKey.EDU_DELMAR:
      return delmarConfig;
    case ApplicationConfigurationKey.EDU_DCPS_MICROGRANT:
      return eduMicrogrant;
    case ApplicationConfigurationKey.EDU_DCPS_EMERGENCY:
      return eduEmergency;
    case ApplicationConfigurationKey.EDU_ECMC:
      return eduECMC;
    case ApplicationConfigurationKey.EDU_LCCC:
      return lcccConfig;
    case ApplicationConfigurationKey.EDU_MATC:
      return matcConfig;
    case ApplicationConfigurationKey.EDU_NCCPC:
      return nccpcConfig;
    case ApplicationConfigurationKey.EDU_NO_DOCS:
      return eduNoDocs;
    case ApplicationConfigurationKey.EDU_PG:
      return eduPG;
    case ApplicationConfigurationKey.MICHIGAN_EDUCATION_CORPS:
      return mecConfig;
    case ApplicationConfigurationKey.OPPNET:
      return oppnetConfig;
    case ApplicationConfigurationKey.EDU_COMPTON_DUAL:
      return comptonDualConfig;
    case ApplicationConfigurationKey.EDU_MINNESOTA:
      return minnesotaConfig;

    default:
      return assertExhaustive(program.config.applicationConfiguration);
  }
}
