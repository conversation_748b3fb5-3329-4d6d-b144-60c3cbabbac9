import splitArrayKey from '@bybeam/platform-lib/answers/splitArrayKey';
import {
  type ApplicantProfile,
  type ApplicantProfileConfig,
  type ApplicantProfileConfiguration,
  type ApplicationAnswers,
  type ApplicationConfig,
  type ApplicationQuestion,
  ApplicationQuestionKeys,
  type ApplicationSection,
  type Field,
  type IntroPage,
} from '@bybeam/platform-types';

import { getApplicantProfileConfig } from '@/app/_utils/applicantProfile';
import { flatten } from 'flat';
import { SpecialSectionKeys } from './configs/standards/types';
import { getQuestionFields } from './question';

export interface ApplicationConfigDefinitions {
  definitions: {
    introPages?: { [key: string]: IntroPage };
    sections?: { [key: string]: ApplicationSection };
    questions?: { [key: string]: ApplicationQuestion };
  };
}

export function getApplicantSections(config: ApplicationConfig): ApplicationSection[] {
  return config.sections.filter(({ key }) => key !== SpecialSectionKeys.AdminOnly);
}

export function getField(config: ApplicationConfig, key: string): Field {
  return config.sections
    .flatMap(({ questionGroups }) =>
      questionGroups.flatMap(({ questions }) => questions.flatMap(({ fields }) => fields)),
    )
    .find(({ key: fieldKey }) => fieldKey === key);
}

// Returns the question with the given key, if it exists in the config.
// Only checks the questions in config.sections, does not look in config.portalSections
export function getQuestion(config: ApplicationConfig, key: string): ApplicationQuestion {
  return config.sections
    .flatMap(({ questionGroups }) => questionGroups.flatMap(({ questions }) => questions))
    .find(({ key: qKey }) => qKey === key);
}

// Gets flattened list of question keys
export function getAllQuestionKeys(config: ApplicationConfig) {
  return config.sections?.flatMap((section) => getSectionFieldKeys(section));
}

const getSectionFieldKeys = (section: ApplicationSection): string[] =>
  section.questionGroups.flatMap(({ questions }) =>
    questions.flatMap(({ fields }) => getQuestionFields(fields).map(({ key }) => key)),
  );

const keyInList = (list: string[], key: string): boolean =>
  list.some((configKey) => key?.toLowerCase().includes(configKey.toLowerCase()));

const addressKeyRegex = /addressid/gi;

export const sectionHasPrefilledAnswers = (
  section: ApplicationSection,
  applicantProfile: ApplicantProfile,
  partnerConfig: ApplicantProfileConfig,
): boolean => {
  const sectionKeys = getSectionFieldKeys(section);
  const partnerConfigMatch =
    partnerConfig?.profileKeys?.some(({ key }) => keyInList(sectionKeys, key)) ||
    keyInList(sectionKeys, ApplicationQuestionKeys.MailingAddress);

  const applicantProfileMatch = Object.keys(
    // biome-ignore lint/suspicious/noExplicitAny: only considering the keys
    flatten<ApplicationAnswers, Record<string, any>[]>({
      ...(applicantProfile?.answers ?? {}),
      ...(applicantProfile?.mailingAddress
        ? { [ApplicationQuestionKeys.MailingAddress]: applicantProfile.mailingAddress.id }
        : {}),
    }),
  ).some((key) => {
    const { key: cleanedKey } = splitArrayKey(key);
    if (cleanedKey.match(addressKeyRegex))
      return keyInList(
        sectionKeys.map((sectionKey) => `${sectionKey}Id`),
        cleanedKey,
      );
    return keyInList(sectionKeys, cleanedKey);
  });
  return partnerConfigMatch && applicantProfileMatch;
};

export const applicationHasPrefilledAnswers = (
  applicationConfig: ApplicationConfig,
  applicantProfile: ApplicantProfile,
  applicantProfileConfigs: ApplicantProfileConfiguration[],
): boolean => {
  const partnerConfig = getApplicantProfileConfig(applicantProfileConfigs, applicantProfile);
  return !!applicationConfig?.sections?.find((section) =>
    sectionHasPrefilledAnswers(section, applicantProfile, partnerConfig),
  );
};
