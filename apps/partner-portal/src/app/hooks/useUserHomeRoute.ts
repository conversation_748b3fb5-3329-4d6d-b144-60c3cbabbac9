import { getUserHomeRoute } from '@/app/Routes';
import { useNextAuth } from '@/app/providers/NextAuthProvider';

/**
 * Hook that returns the appropriate home route based on the current user's auth state.
 * Used throughout the app as a fallback redirect destination when users
 * shouldn't be on a particular page (feature flags disabled, missing data, etc.)
 */
export function useUserHomeRoute(): string {
  const { user } = useNextAuth();
  return getUserHomeRoute(user);
}
