import '@radix-ui/themes/styles.css';

import { BEAM_SUPPORT_EMAIL } from '@/app/_utils/constants';
import { faro } from '@grafana/faro-web-sdk';
import CheckIcon from '@mui/icons-material/Check';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';

import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Link,
  Separator,
  Theme,
  Tooltip,
} from '@radix-ui/themes';
import { useEffect, useRef, useState } from 'react';

export const ErrorComponent = ({
  error,
}: {
  error: Error & { digest?: string };
}) => {
  const [traceId, setTraceId] = useState('');

  useEffect(() => {
    if (error && faro?.api) {
      // Push the error to Faro for tracking
      faro.api.pushError(error);

      // Get the session ID for debugging purposes
      const sessionId = faro.api.getSession()?.id;
      if (sessionId) {
        setTraceId(sessionId);
      } else {
        // Fallback: use timestamp for debugging if no session ID available
        setTraceId(new Date().toISOString());
      }
    }
  }, [error]);

  const contentCopyIcon = useRef<SVGSVGElement>(null);
  const checkIcon = useRef<SVGSVGElement>(null);

  const debugMessage = traceId ? (
    <>
      &nbsp;and provide the following trace id: <br /> <br />
      <Tooltip content="Click to copy Trace ID to clipboard">
        <Button
          onClick={() => {
            navigator.clipboard.writeText(traceId);
            if (contentCopyIcon.current && checkIcon.current) {
              contentCopyIcon.current.style.display = 'none';
              checkIcon.current.style.display = 'inline-block';
            }
          }}
          size={'3'}
          variant="outline"
        >
          <ContentCopyIcon fontSize="small" ref={contentCopyIcon} />
          <CheckIcon fontSize="small" ref={checkIcon} style={{ display: 'none' }} />
          <strong>{traceId}</strong>&nbsp;
        </Button>
      </Tooltip>
    </>
  ) : (
    ''
  );

  return (
    <Theme>
      <Container size="2" align="center" py="8">
        <Flex direction={'column'} gap="1" align={'center'} justify={'center'} width="auto">
          {/* `NextError` is the default Next.js error page component. Its type
        definition requires a `statusCode` prop. However, since the App Router
        does not expose status codes for errors, we simply pass 0 to render a
        generic error message. */}
          <Box p="4">
            <Heading>Application error: a client-side exception has occurred</Heading>
          </Box>
          <Box p="4">
            If the problem persists, please contact us at{' '}
            <Link href={`mailto:${BEAM_SUPPORT_EMAIL}`}>{BEAM_SUPPORT_EMAIL}</Link>
            {debugMessage}
          </Box>
          <Box p="4">
            <Separator size="4" />
            <Button
              onClick={() => {
                // Refresh the page to try again
                location.reload();
              }}
            >
              Refresh page to try again
            </Button>
          </Box>
        </Flex>
      </Container>
    </Theme>
  );
};
