import {
  type ApplicantNameConsistency,
  nameMatchingConfig,
} from '@/app/components/features/NameMatching';
import CopyToClipboard from '@/app/components/ui/CopyToClipboard/CopyToClipboard';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { getDisplayLabel } from '@/spa-legacy/common/components/DocumentUpload/utils';
import { formatDate } from '@/spa-legacy/common/utilities/format';
import { type Document } from '@bybeam/platform-types';
import { Cross2Icon, EnterFullScreenIcon, CheckIcon, QuestionMarkCircledIcon } from '@radix-ui/react-icons';
import {
  Badge,
  Box,
  Callout,
  DataList,
  Dialog,
  Flex,
  Heading,
  HoverCard,
  IconButton,
  Inset,
  Strong,
  Text,
  Tooltip,
  VisuallyHidden,
} from '@radix-ui/themes';
import { usePostHog } from 'posthog-js/react';
import styles from './DocumentPreview.module.css';

interface DocumentPreviewProps {
  document: Document;
  size?: '1' | '2' | '3' | '4';
  nameMatchingData?: {
    consistency: ApplicantNameConsistency;
    bestMatchedName: string;
  };
  triggerId?: string;
}

enum DocumentType {
  Image = 'image',
  PDF = 'pdf',
  CSV = 'csv',
  Other = 'other',
}

const getDocumentType = (mimetype: string): DocumentType => {
  if (mimetype.match('image.*')) return DocumentType.Image;
  if (mimetype?.match('pdf')) return DocumentType.PDF;
  if (mimetype?.match('csv')) return DocumentType.CSV;
  return DocumentType.Other;
};

const cropFilename = (filename: string) => {
  return filename.length > 20 ? `${filename.slice(0, 10)}[...]${filename.slice(-7)}` : filename;
};

const formatTagName = (name: string) => {
  return name.replace(/_/g, ' ');
};

const formatFieldKey = (name: string) => {
  return name
    .replace(/_/g, ' ')
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const DocumentPreview = ({
  document,
  size = '2',
  nameMatchingData,
  triggerId,
}: DocumentPreviewProps) => {
  const posthog = usePostHog();
  const isDocsAiEnabled = posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.docsAi2024Q4);
  const isExtractedFieldsEnabled = posthog.isFeatureEnabled(
    POSTHOG_FEATURE_FLAGS.documentsExtractedFields,
  );
  const displayName = document.filename;

  // Determine name matching display info
  const nameMatchingInfo = nameMatchingData
    ? (() => {
        const status = nameMatchingData.consistency.status;
        const result = nameMatchingData.consistency.result;

        // Determine which config to show
        type DisplayKey =
          | 'PENDING'
          | 'FAILED'
          | 'EXACT'
          | 'SIMILAR'
          | 'NOT_SIMILAR'
          | 'UNKNOWN'
          | 'COMPLETED';

        let displayKey: DisplayKey = status as DisplayKey;

        if (status === 'COMPLETED' && result?.overallSimilarity) {
          displayKey = result.overallSimilarity as DisplayKey;
        }

        const config = nameMatchingConfig[displayKey];

        // If config is undefined, something went wrong - return null
        if (!config) {
          console.warn('Name matching config not found for displayKey:', displayKey);
          return null;
        }

        return {
          canonicalName: result?.canonicalName,
          bestMatchedName: nameMatchingData.bestMatchedName,
          overallSimilarity: result?.overallSimilarity,
          config,
          displayKey,
        };
      })()
    : null;
  return (
    <Dialog.Root>
      <HoverCard.Root>
        <HoverCard.Trigger>
          <Dialog.Trigger>
            <button type="button" data-trigger-id={triggerId}>
              <Flex gap="1" align="center">
                <Flex height={`${size}rem`} width={`${size}rem`} align="center" justify="center">
                  {getDocumentType(document.mimetype) === DocumentType.Image ? (
                    <img
                      title={document.filename}
                      src={document.previewUrl}
                      className={styles.documentPreview}
                      alt={`Preview of document upload for ${displayName}`}
                    />
                  ) : getDocumentType(document.mimetype) === DocumentType.PDF ? (
                    <object
                      data={`${document.previewUrl}`}
                      className={styles.documentPreview}
                      aria-label={`Preview document upload for ${displayName}`}
                    />
                  ) : null}
                </Flex>
                {cropFilename(document.filename)}
              </Flex>
            </button>
          </Dialog.Trigger>
        </HoverCard.Trigger>
        <HoverCard.Content minWidth="240px" maxWidth="360px" sideOffset={4} alignOffset={16}>
          <Inset>
            <Flex direction="column">
              <Flex p="2" gap="2" justify="between">
                <Text size="1">{document.filename}</Text>
                <Dialog.Trigger>
                  <IconButton
                    variant="ghost"
                    aria-label="View full screen"
                    color="gray"
                    className={styles.fullScreenButton}
                  >
                    <EnterFullScreenIcon />
                  </IconButton>
                </Dialog.Trigger>
              </Flex>
              <Flex
                align="center"
                justify="center"
                minHeight="8rem"
                className={styles.documentHoverImageContainer}
              >
                {getDocumentType(document.mimetype) === DocumentType.Image ? (
                  <img
                    title={document.filename}
                    src={document.previewUrl}
                    className={styles.document}
                    alt={`Document upload for ${displayName}`}
                  />
                ) : getDocumentType(document.mimetype) === DocumentType.PDF ? (
                  <iframe
                    src={document.previewUrl}
                    title={`Document upload for ${displayName}`}
                    className={styles.documentHoverImage}
                  />
                ) : null}
              </Flex>
              <Box p="2">
                <Flex asChild gapX="2" gapY="1" wrap="wrap">
                  <ul>
                    {document.documentTags?.map(({ tag }) => {
                      return (
                        <Flex asChild key={tag.id}>
                          <li>
                            <Tooltip content={tag.description}>
                              <Badge size="1" color="yellow" highContrast>
                                {formatTagName(tag.name)}
                              </Badge>
                            </Tooltip>
                          </li>
                        </Flex>
                      );
                    })}
                  </ul>
                </Flex>
              </Box>
            </Flex>
          </Inset>
        </HoverCard.Content>
      </HoverCard.Root>
      <Dialog.Content size="1" maxWidth="90vw" height="90vh" maxHeight="90vh">
        <VisuallyHidden>
          <Dialog.Title>{document.filename}</Dialog.Title>
        </VisuallyHidden>
        <Flex width="100%" height="100%" minHeight="0">
          {/* Document viewer */}
          <Flex
            height="100%"
            justify="center"
            align="center"
            className={styles.fullScreenDocumentImage}
            minHeight="0"
          >
            {getDocumentType(document.mimetype) === DocumentType.Image ? (
              <img
                title={document.filename}
                src={document.previewUrl}
                className={styles.fullScreenDocument}
                alt={`Document upload for ${displayName}`}
              />
            ) : getDocumentType(document.mimetype) === DocumentType.PDF ? (
              <iframe
                src={document.previewUrl}
                className={styles.fullScreenDocumentPDF}
                title={`Document upload for ${displayName}`}
              />
            ) : null}
          </Flex>

          <Box minWidth="320px" maxWidth="400px" pl="4" pr="2" overflowY="auto">
            <Flex direction="column" gap="4">
              <Flex direction="column" gap="2">
                <Flex justify="between" align="center">
                  <Heading size="5" weight="bold">
                    Document metadata
                  </Heading>
                  <Dialog.Close>
                    <IconButton
                      aria-label="Close document preview"
                      variant="soft"
                      size="1"
                      radius="full"
                      color="gray"
                    >
                      <Cross2Icon />
                    </IconButton>
                  </Dialog.Close>
                </Flex>
                <DataList.Root orientation="vertical" size="2">
                  <DataList.Item>
                    <DataList.Label minWidth="88px">Filename</DataList.Label>
                    <DataList.Value>{document.filename}</DataList.Value>
                  </DataList.Item>
                  <DataList.Item>
                    <DataList.Label minWidth="88px">File type</DataList.Label>
                    <DataList.Value>{document.mimetype}</DataList.Value>
                  </DataList.Item>
                  <DataList.Item>
                    <DataList.Label minWidth="88px">Uploaded</DataList.Label>
                    <DataList.Value>
                      {formatDate(document.createdAt)} by {document.uploader?.name || 'Unknown'}
                    </DataList.Value>
                  </DataList.Item>
                </DataList.Root>
              </Flex>
              {isDocsAiEnabled && (
                <Flex direction="column" gap="2">
                  <Heading size="5" weight="bold">
                    Document intelligence
                  </Heading>
                  <DataList.Root orientation="vertical" size="2">
                    {document.summary?.prediction?.label && (
                      <DataList.Item>
                        <DataList.Label minWidth="88px">Document type</DataList.Label>
                        <DataList.Value>
                          {getDisplayLabel(document.summary?.prediction?.label)}
                        </DataList.Value>
                      </DataList.Item>
                    )}
                    {document.documentTags?.length ? (
                      <DataList.Item align="start">
                        <DataList.Label minWidth="88px">Quality</DataList.Label>
                        <DataList.Value>
                          <Flex asChild gapX="2" gapY="1" wrap="wrap">
                            <ul>
                              {document.documentTags.map(({ tag }) => (
                                <li key={tag.id}>
                                  <Tooltip content={tag.description}>
                                    <Badge size="1" color="yellow" highContrast>
                                      {formatTagName(tag.name)}
                                    </Badge>
                                  </Tooltip>
                                </li>
                              ))}
                            </ul>
                          </Flex>
                        </DataList.Value>
                      </DataList.Item>
                    ) : null}
                    {isExtractedFieldsEnabled && document.documentFields?.length ? (
                      <DataList.Item align="start">
                        <DataList.Label minWidth="88px">
                          <Tooltip
                            content="These fields were identified in the document after it was uploaded."
                            maxWidth="16rem"
                          >
                            <Text>Fields</Text>
                          </Tooltip>
                        </DataList.Label>
                        <DataList.Value>
                          <Flex direction="column" gap="1">
                            {document.documentFields.map((field) => (
                              <CopyToClipboard.Root key={field.id}>
                                <CopyToClipboard.Content>
                                  <Text size="2">
                                    <Strong>{formatFieldKey(field.fieldKey)}:</Strong>{' '}
                                    {field.fieldValue}
                                  </Text>
                                </CopyToClipboard.Content>
                                <CopyToClipboard.Trigger
                                  text={field.fieldValue}
                                  contentType={`DocumentField:${field.fieldKey}`}
                                />
                              </CopyToClipboard.Root>
                            ))}
                          </Flex>
                        </DataList.Value>
                      </DataList.Item>
                    ) : null}
                    {nameMatchingInfo?.config && (
                      <>
                        <DataList.Item>
                          <DataList.Label minWidth="88px">Name match</DataList.Label>
                          <DataList.Value>
                            <Flex align="center" gap="2">
                              <Text color={nameMatchingInfo.config.color}>
                                {nameMatchingInfo.config.icon}
                              </Text>
                              <Text size="2" weight="medium" color={nameMatchingInfo.config.color}>
                                {nameMatchingInfo.config.title}
                              </Text>
                            </Flex>
                          </DataList.Value>
                        </DataList.Item>
                        {nameMatchingInfo.canonicalName && (
                          <DataList.Item>
                            <DataList.Label minWidth="88px">Profile name</DataList.Label>
                            <DataList.Value>{nameMatchingInfo.canonicalName}</DataList.Value>
                          </DataList.Item>
                        )}
                        {nameMatchingInfo.bestMatchedName && (
                          <DataList.Item>
                            <DataList.Label minWidth="88px">DL name</DataList.Label>
                            <DataList.Value>{nameMatchingInfo.bestMatchedName}</DataList.Value>
                          </DataList.Item>
                        )}
                        {nameMatchingInfo?.overallSimilarity === 'EXACT' && (
                          <DataList.Item align="start">
                            <DataList.Label minWidth="88px">Assessment</DataList.Label>
                            <DataList.Value>
                              <Callout.Root color="green" size="1">
                                <Callout.Icon>
                                  <CheckIcon width="16" height="16" />
                                </Callout.Icon>
                                <Callout.Text>
                                  The applicant profile name matches the Driver's License name.
                                </Callout.Text>
                              </Callout.Root>
                            </DataList.Value>
                          </DataList.Item>
                        )}
                        {nameMatchingInfo?.overallSimilarity === 'SIMILAR' && (
                          <DataList.Item align="start">
                            <DataList.Label minWidth="88px">Assessment</DataList.Label>
                            <DataList.Value>
                              <Callout.Root color="amber" size="1">
                                <Callout.Icon>
                                  <QuestionMarkCircledIcon width="16" height="16" />
                                </Callout.Icon>
                                <Callout.Text>
                                  Names are similar but not exact. May be due to nicknames, middle
                                  names, or spelling differences.
                                </Callout.Text>
                              </Callout.Root>
                            </DataList.Value>
                          </DataList.Item>
                        )}
                        {nameMatchingInfo?.overallSimilarity === 'NOT_SIMILAR' && (
                          <DataList.Item align="start">
                            <DataList.Label minWidth="88px">Assessment</DataList.Label>
                            <DataList.Value>
                              <Callout.Root color="red" size="1">
                                <Callout.Icon>
                                  <QuestionMarkCircledIcon width="16" height="16" />
                                </Callout.Icon>
                                <Callout.Text>
                                  Names do not match. Verify applicant identity and request
                                  additional documentation if needed.
                                </Callout.Text>
                              </Callout.Root>
                            </DataList.Value>
                          </DataList.Item>
                        )}
                        {nameMatchingInfo?.overallSimilarity === 'UNKNOWN' && (
                          <DataList.Item align="start">
                            <DataList.Label minWidth="88px">Assessment</DataList.Label>
                            <DataList.Value>
                              <Callout.Root color="gray" size="1">
                                <Callout.Icon>
                                  <QuestionMarkCircledIcon width="16" height="16" />
                                </Callout.Icon>
                                <Callout.Text>
                                  Name similarity could not be determined. Please manually review.
                                </Callout.Text>
                              </Callout.Root>
                            </DataList.Value>
                          </DataList.Item>
                        )}
                      </>
                    )}
                  </DataList.Root>
                </Flex>
              )}
            </Flex>
          </Box>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};

export default DocumentPreview;
