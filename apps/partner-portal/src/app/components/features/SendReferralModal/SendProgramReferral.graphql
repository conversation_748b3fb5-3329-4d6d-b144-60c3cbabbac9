mutation SendProgramReferral($input: CreateReferralInput!, $userId: UUID!) {
  programReferral {
    create(input: $input) {
      metadata {
        status
        message
        errors
        id
      }
      record {
        id
        program { id }
        createdAt
      }
      query {
        users(filter: {id: $userId}) {
          users {
            id
            referrals { id }
          }
        }
      }
    }
  }
}