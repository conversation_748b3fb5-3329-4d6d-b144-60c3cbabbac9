import { programHasPayments } from '@/app/_utils/programs';
import InfoDisplay from '@/spa-legacy/common/components/InfoDisplay';
import useErrorIndicator from '@/spa-legacy/common/hooks/useErrorIndicator';
import useSaveIndicator from '@/spa-legacy/common/hooks/useSaveIndicator';
import { formatCurrency } from '@/spa-legacy/common/utilities/format';
import { sum } from '@/spa-legacy/common/utilities/math';
import { useMutation } from '@apollo/client';
import type { MutationResponse, Program, ProgramReferral, User } from '@bybeam/platform-types';
import { Button, Flex, Strong, Text, Theme } from '@radix-ui/themes';
import type { FormEvent } from 'react';
import SendProgramReferralMutation from './SendProgramReferral.graphql';

interface SendReferralModalProps {
  user: User;
  program: Program;
  onClose: () => void;
}

export default function SendReferralModal({
  user,
  program,
  onClose,
}: SendReferralModalProps): JSX.Element {
  const startingBalance = sum(program.funds.map(({ startingBalance }) => startingBalance));
  const remainingBalance = sum(
    program.funds.map(({ stats: { remainingBalance } }) => remainingBalance),
  );
  const hasLowFunds = programHasPayments(program) && remainingBalance / startingBalance < 0.05;

  const [doMutation, { loading }] = useMutation<
    { programReferral: { create: MutationResponse<ProgramReferral> } },
    { input: { programId: string; userId: string }; userId: string }
  >(SendProgramReferralMutation);

  const showSaveIndicator = useSaveIndicator();
  const showErrorIndicator = useErrorIndicator();
  const sendReferral = async () => {
    const response = await doMutation({
      variables: { input: { programId: program.id, userId: user.id }, userId: user.id },
    });
    if (response.data.programReferral.create.metadata.status === 201) {
      showSaveIndicator();
      onClose();
    } else {
      showErrorIndicator('Something went wrong!', sendReferral);
    }
  };

  const onSubmit = (e: FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    sendReferral();
  };

  return (
    // todo: Replace mui modal with radix modal
    <Theme accentColor="indigo">
      <Flex asChild direction="column" gap="4">
        <form onSubmit={onSubmit}>
          <Text size="3">
            <Strong>{user.name}</Strong> will be referred to <Strong>{program.name}</Strong>.
          </Text>
          <Text>
            <strong>Note:</strong> The applicant will receive a notification letting them know about
            the referral.
          </Text>
          <InfoDisplay
            visible={hasLowFunds}
            message={
              <>
                WARNING: Remaining funds for {program.name} are low at{' '}
                <strong>{formatCurrency(remainingBalance)}</strong>.
              </>
            }
          />
          <Flex gap="3" justify="end">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Send Referral</Button>
          </Flex>
        </form>
      </Flex>
    </Theme>
  );
}
