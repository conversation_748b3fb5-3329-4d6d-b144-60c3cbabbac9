import {
  CheckCircledIcon,
  Cross2Icon,
  CrossCircledIcon,
  QuestionMarkCircledIcon,
} from '@radix-ui/react-icons';
import { Badge, BadgeProps, HoverCard, Spinner, Text } from '@radix-ui/themes';

// Minimal local types mirroring GraphQL ResolvedEntities types to avoid
// importing generated types directly in this POC component.
export type ResolverStatus = 'PENDING' | 'COMPLETED' | 'FAILED';
export type Similarity = 'EXACT' | 'SIMILAR' | 'NOT_SIMILAR' | 'UNKNOWN';

export interface DocumentConsistency {
  documentId: string;
  similarity: Similarity;
  bestMatchedName: string;
}

export interface ApplicantNameConsistencyResult {
  canonicalName: string;
  overallSimilarity: Similarity;
  documents: DocumentConsistency[];
}

export interface ApplicantNameConsistency {
  status: ResolverStatus;
  result?: ApplicantNameConsistencyResult | null;
}

export interface NameMatchingConfig {
  icon: React.ReactNode;
  color: BadgeProps['color'];
  title: string;
  description: string;
}

// Single config that handles both status and similarity
// We show PENDING/FAILED for processing states, or the similarity result when COMPLETED
export const nameMatchingConfig: Record<ResolverStatus | Similarity, NameMatchingConfig> = {
  // Processing states
  PENDING: {
    icon: <Spinner size="1" />,
    color: 'gray',
    title: 'Processing',
    description: "Driver's License name review is currently being processed.",
  },
  FAILED: {
    icon: <Cross2Icon />,
    color: 'red',
    title: 'Failed',
    description: "Driver's License name review could not be completed. Please try again.",
  },
  // Similarity results (shown when status is COMPLETED)
  EXACT: {
    icon: <CheckCircledIcon />,
    color: 'green',
    title: 'Match',
    description: 'The names match.',
  },
  SIMILAR: {
    icon: <QuestionMarkCircledIcon />,
    color: 'amber',
    title: 'Similar',
    description: 'The names are similar but not an exact match.',
  },
  NOT_SIMILAR: {
    icon: <CrossCircledIcon />,
    color: 'red',
    title: 'Not Similar',
    description: 'The names do not match.',
  },
  UNKNOWN: {
    icon: <Cross2Icon />,
    color: 'gray',
    title: 'Unknown',
    description: 'Name similarity could not be determined.',
  },
  // COMPLETED shouldn't be shown directly, but included for completeness
  COMPLETED: {
    icon: <CheckCircledIcon />,
    color: 'green',
    title: 'Completed',
    description: "Driver's License name review has been completed.",
  },
};

interface NameMatchingIndicatorProps {
  consistency: ApplicantNameConsistency;
  variant?: 'full' | 'icon-only';
  onClickDetails?: () => void;
}

export default function NameMatchingIndicator({
  consistency,
  variant = 'full',
  onClickDetails,
}: NameMatchingIndicatorProps) {
  const status = consistency.status as ResolverStatus;

  // Determine which config to show:
  // - If PENDING or FAILED, show that status
  // - If COMPLETED, show the similarity result
  let displayKey: ResolverStatus | Similarity = status;

  if (status === 'COMPLETED') {
    const similarity = consistency.result?.overallSimilarity;
    if (similarity) {
      displayKey = similarity;
    }
  }

  const { icon, color, title, description } = nameMatchingConfig[displayKey];

  const triggerContent =
    variant === 'icon-only' ? (
      <Text color={color} style={{ verticalAlign: 'middle' }} aria-label={title} role="img">
        {icon}
      </Text>
    ) : (
      <Badge color={color} highContrast>
        {icon}
        {variant === 'full' && <span>{title}</span>}
      </Badge>
    );

  const renderHoverCardContent = () => (
    <HoverCard.Content maxWidth="320px">
      <Text size="2">{description}</Text>
      {onClickDetails && status === 'COMPLETED' && (
        <Text size="1" color="gray" mt="2" as="div">
          Click to view details
        </Text>
      )}
    </HoverCard.Content>
  );

  const triggerElement = onClickDetails ? (
    <button
      type="button"
      aria-label={`View Driver's License name review: ${title}`}
      onClick={onClickDetails}
    >
      {triggerContent}
    </button>
  ) : (
    <span aria-label={`Driver's License name review: ${title}`}>{triggerContent}</span>
  );

  return (
    <HoverCard.Root>
      <HoverCard.Trigger>{triggerElement}</HoverCard.Trigger>
      {renderHoverCardContent()}
    </HoverCard.Root>
  );
}
