import DetailSection from '@/spa-legacy/portal/components/DetailSection';
import { CheckIcon, QuestionMarkCircledIcon } from '@radix-ui/react-icons';
import { Badge, Button, Callout, Dialog, Flex, Text } from '@radix-ui/themes';
import { DLItem } from '../../ui/DescriptionList';
import { ApplicantNameConsistency, nameMatchingConfig } from './NameMatchingIndicator';

interface NameMatchingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  consistency: ApplicantNameConsistency;
  documentName?: string;
  bestMatchedName: string;
}

export default function NameMatchingDialog({
  open,
  onOpenChange,
  consistency,
  documentName = "Driver's License",
  bestMatchedName,
}: NameMatchingDialogProps) {
  const result = consistency.result;

  if (!result) {
    return null;
  }

  const { canonicalName, overallSimilarity } = result;
  const similarityConfig = nameMatchingConfig[overallSimilarity];

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Content>
        <Dialog.Title>Driver's License Name Review</Dialog.Title>

        <Dialog.Description mb="4">
          This shows how the applicant's profile name compares to the name extracted from their{' '}
          {documentName}.
        </Dialog.Description>

        <Flex direction="column" gap="4">
          <DetailSection title="Applicant Profile Name">
            <DLItem label="Canonical Name" value={canonicalName} />
          </DetailSection>

          <DetailSection title={`${documentName} Extracted Name`}>
            <DLItem label="Best Matched Name" value={bestMatchedName || 'Not available'} />
          </DetailSection>

          <DetailSection title="Similarity Assessment">
            <DLItem
              label="Overall Similarity"
              value={
                <Badge color={similarityConfig.color} highContrast>
                  {similarityConfig.icon}
                  <span>{similarityConfig.title}</span>
                </Badge>
              }
            />
            <Flex direction="column" gap="1" mt="2">
              <Text size="2" color="gray">
                {similarityConfig.description}
              </Text>
            </Flex>
          </DetailSection>

          {overallSimilarity === 'EXACT' ? (
            <Callout.Root color="green" highContrast>
              <Callout.Icon>
                <CheckIcon />
              </Callout.Icon>
              <Callout.Text>
                The applicant profile name matches the {documentName} extracted name.
              </Callout.Text>
            </Callout.Root>
          ) : overallSimilarity === 'SIMILAR' ? (
            <Callout.Root color="yellow" highContrast>
              <Callout.Icon>
                <QuestionMarkCircledIcon />
              </Callout.Icon>
              <Callout.Text>
                The names are similar but not a match. This may be due to nicknames, middle names,
                or minor spelling differences. Please review both names carefully.
              </Callout.Text>
            </Callout.Root>
          ) : overallSimilarity === 'NOT_SIMILAR' ? (
            <Callout.Root color="red" highContrast>
              <Callout.Icon>
                <QuestionMarkCircledIcon />
              </Callout.Icon>
              <Callout.Text>
                The names do not match. Please verify the applicant's identity and request
                additional documentation if needed.
              </Callout.Text>
            </Callout.Root>
          ) : (
            <Callout.Root color="gray" highContrast>
              <Callout.Icon>
                <QuestionMarkCircledIcon />
              </Callout.Icon>
              <Callout.Text>
                Name similarity could not be determined. Please manually review the names.
              </Callout.Text>
            </Callout.Root>
          )}

          <Flex gap="3" justify="end">
            <Dialog.Close>
              <Button variant="outline">Close</Button>
            </Dialog.Close>
          </Flex>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
}
