import { makeRoute } from '@/app/Routes';
import { PORTAL_ROUTES } from '@/app/Routes';
import { checkFeatureAnyProgram } from '@/app/_utils/checkFeature';
import { useNextAuth } from '@/app/providers/NextAuthProvider';
import Loading from '@/spa-legacy/common/components/Loading';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Color from '@/spa-legacy/common/utilities/Color';
import { formatDate } from '@/spa-legacy/common/utilities/format';
import ApplicationTitle from '@/spa-legacy/portal/components/ApplicationTitle';
import { hasReferral } from '@/spa-legacy/portal/utils/applications';
import { WORKFLOW_ACTIVE_CASE_STATUS } from '@/spa-legacy/portal/utils/workflow';
import { useQuery } from '@apollo/client';
import { type Case, CaseStatus, FeatureName, type PageInfo } from '@bybeam/platform-types';
import { Box, Card, Checkbox, Flex } from '@radix-ui/themes';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';
import styles from './MyAssignments.module.css';
import GetAssignmentsQuery from './MyAssignmentsQuery.graphql';

function CaseCard({ applications, ...case_ }: Case): JSX.Element {
  const application = applications?.[0];
  return (
    <Card asChild>
      <Link
        href={
          makeRoute(PORTAL_ROUTES.CASE_REVIEW, {
            programId: case_.program?.id,
            caseId: case_.id,
          }) as RouterImpl<string>
        }
      >
        <ApplicationTitle
          {...case_}
          hasReferral={application && hasReferral(application)}
          variant="body"
        />
        <Typography variant="label" color={Color.TextSecondary}>
          {application?.submittedAt && formatDate(application.submittedAt)}
        </Typography>
      </Link>
    </Card>
  );
}

export default function CaseList(): JSX.Element {
  const { user } = useNextAuth();
  const { programs = [] } = usePartner();
  const activeStatuses = useMemo(() => {
    const isCore = checkFeatureAnyProgram(programs, FeatureName.WorkflowCore);
    return isCore
      ? WORKFLOW_ACTIVE_CASE_STATUS[FeatureName.WorkflowCore]
      : WORKFLOW_ACTIVE_CASE_STATUS[FeatureName.WorkflowExtended];
  }, [programs]);

  const [variables, setVariables] = useState<{ adminId: string; status?: CaseStatus[] }>({
    adminId: user?.admin?.id as string,
    status: activeStatuses,
  });
  const isRefetching = useRef(false);
  const shouldFetch = useRef(false);
  const page = useRef(0);
  const [listInfo, setListInfo] = useState<{ cases: Case[]; count: number }>({
    cases: [],
    count: 0,
  });
  const { ref, inView } = useInView({
    threshold: 1,
    delay: 500,
  });

  const { data, loading, error, refetch, fetchMore } = useQuery<
    { cases: { cases: Case[]; pageInfo: PageInfo } },
    { adminId: string }
  >(GetAssignmentsQuery, {
    variables: variables,
    fetchPolicy: 'cache-and-network',
  });

  const { showSnackbar } = useSnackbar();
  useEffect(() => {
    if (error) showSnackbar('Something went wrong. Please refresh and try again.');
  }, [error, showSnackbar]);

  useEffect(() => {
    if (!data) return;
    const {
      cases: {
        cases: _cases,
        pageInfo: { count },
      },
    } = data;

    shouldFetch.current = _cases.length < count;
    setListInfo({
      cases: _cases,
      count,
    });
    isRefetching.current = false;
  }, [data]);

  const handleFetchMore = useCallback(async () => {
    if (shouldFetch.current) {
      isRefetching.current = true;
      await fetchMore({
        variables: {
          page: page.current + 1,
          take: 50,
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult) return prev;
          return Object.assign({}, prev, {
            cases: {
              ...fetchMoreResult.cases,
              cases: [...prev.cases.cases, ...fetchMoreResult.cases.cases],
            },
          });
        },
      });
      page.current++;
      isRefetching.current = false;
    }
  }, [fetchMore]);

  const toggleActiveCases = () => {
    const showAllCases = !!variables?.status?.length;
    const newVariables = {
      adminId: variables.adminId,
      ...(!showAllCases && {
        status: activeStatuses,
      }),
    };
    setVariables(newVariables);
  };

  useEffect(() => {
    if (refetch) refetch(variables);
  }, [variables, refetch]);

  useEffect(() => {
    if (inView && !isRefetching.current) {
      handleFetchMore();
    }
  }, [inView, handleFetchMore]);

  if (error)
    return (
      <Typography variant="body" color={Color.Error}>
        Something went wrong
      </Typography>
    );

  return (
    <>
      <div className={styles.myAssignmentsHeading}>
        <div>
          <strong>My assignments</strong>
        </div>
        <strong>{listInfo.count}</strong> {listInfo.count === 1 ? 'case' : 'cases'} assigned to you.
        <div className={styles.checkbox}>
          <Checkbox checked={!!variables?.status?.length} onClick={toggleActiveCases} />
          <span>Active cases only</span>
        </div>
      </div>
      <div>
        {listInfo.cases.length ? (
          <Flex asChild direction="column" px="4" mt="4">
            <ol>
              {listInfo.cases.map((entity, index) => (
                <Box
                  asChild
                  mb="4"
                  key={entity.id}
                  ref={index === listInfo.cases.length - 1 ? ref : null}
                >
                  <li>
                    <CaseCard {...entity} />
                  </li>
                </Box>
              ))}
            </ol>
          </Flex>
        ) : null}
        {loading || isRefetching.current ? <Loading size="XL" fullPage /> : null}
      </div>
    </>
  );
}
