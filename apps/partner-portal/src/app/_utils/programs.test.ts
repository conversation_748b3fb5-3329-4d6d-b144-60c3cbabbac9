import { FIRST_PARTY_APPLICANT_TYPE, PROGRAM } from '@/tests/mocks/mockData';
import { ApplicantTypeRole, ProgramStatus, type User } from '@bybeam/platform-types';
import { canCreateApplication, isProgramReferred } from './programs';

describe('programs', () => {
  describe('isProgramReferred', () => {
    it('should return true if program status is ReferralOnly and user has a referral', () => {
      expect(
        isProgramReferred({ ...PROGRAM, status: ProgramStatus.ReferralOnly }, {
          referrals: [{ program: { id: PROGRAM.id } }],
          applicantProfile: { applicantType: FIRST_PARTY_APPLICANT_TYPE },
        } as User),
      ).toBeTruthy();
    });
    it('should return false if program status is ReferralOnly but user does NOT have a REFERRAL', () => {
      expect(
        isProgramReferred({ ...PROGRAM, status: ProgramStatus.ReferralOnly }, {
          referrals: [{ program: { id: 'otherProgramId' } }],
          applicantProfile: { applicantType: FIRST_PARTY_APPLICANT_TYPE },
        } as User),
      ).toBeFalsy();
    });
  });

  describe('canCreateApplication', () => {
    it('should return true if program status is Open and has required applicant type', () => {
      expect(
        canCreateApplication(PROGRAM, {
          applicantProfile: { applicantType: FIRST_PARTY_APPLICANT_TYPE },
        } as User),
      ).toBeTruthy();
    });
    it('should return true if program status is Close and has required applicant type', () => {
      expect(
        canCreateApplication({ ...PROGRAM, status: ProgramStatus.Closed }, {
          applicantProfile: { applicantType: FIRST_PARTY_APPLICANT_TYPE },
        } as User),
      ).toBeTruthy();
    });
    it('should return true if program status is ReferralOnly and user has a referral and has required applicant type', () => {
      expect(
        canCreateApplication({ ...PROGRAM, status: ProgramStatus.ReferralOnly }, {
          referrals: [{ program: { id: PROGRAM.id } }],
          applicantProfile: { applicantType: FIRST_PARTY_APPLICANT_TYPE },
        } as User),
      ).toBeTruthy();
    });
    it('should return false if program does NOT MATCH the user TYPE', () => {
      expect(
        canCreateApplication(PROGRAM, {
          applicantProfile: {
            applicantType: {
              id: 'mockThirdParty',
              name: 'Third Party',
              role: ApplicantTypeRole.ThirdParty,
            },
          },
        } as User),
      ).toBeFalsy();
    });
    it('should return false if program status is ReferralOnly but user does NOT have a REFERRAL', () => {
      expect(
        canCreateApplication({ ...PROGRAM, status: ProgramStatus.ReferralOnly }, {
          referrals: [{ program: { id: 'otherProgramId' } }],
          applicantProfile: { applicantType: FIRST_PARTY_APPLICANT_TYPE },
        } as User),
      ).toBeFalsy();
    });
  });
});
