import isRoutingNumberValid from './routingNumber';

const validationError = 'Please enter a valid routing number';

describe('Routing Number Validation', () => {
  it('A string containing 9 numeric digits and results in a valid checksum is valid', () => {
    expect(isRoutingNumberValid('*********')).toEqual(undefined);
  });
  it('A string of less than 9 numeric digits is invalid', () => {
    expect(isRoutingNumberValid('9381')).toEqual(validationError);
  });
  it('A string of more than 9 numeric digits is invalid', () => {
    expect(isRoutingNumberValid('***********')).toEqual(validationError);
  });
  it('A string of 9 alphanumeric characters is invalid', () => {
    expect(isRoutingNumberValid('3N8JXP12H')).toEqual(validationError);
  });
  it('A string of nine 1s is not a valid checksum', () => {
    expect(isRoutingNumberValid('*********')).toEqual(validationError);
  });
  it('A string of ********* is not a valid checksum', () => {
    expect(isRoutingNumberValid('*********')).toEqual(validationError);
  });
});
