import {
  ApplicantProfile,
  ApplicantProfileConfig,
  ApplicantProfileConfiguration,
  Nullable,
} from '@bybeam/platform-types';

export const getApplicantProfileConfig = (
  applicantProfileConfigs: ApplicantProfileConfiguration[],
  applicantProfile: ApplicantProfile,
): Nullable<ApplicantProfileConfig> =>
  applicantProfileConfigs?.find(
    (profileConfig) => profileConfig.applicantTypeId === applicantProfile?.applicantType?.id,
  )?.config;
