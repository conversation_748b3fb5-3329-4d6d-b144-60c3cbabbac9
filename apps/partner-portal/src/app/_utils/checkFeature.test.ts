import { FEATURE, PARTNER, PROGRAM } from '@/tests/mocks/mockData';
import {
  ApplicationConfigurationKey,
  type Feature,
  FeatureName,
  type Program,
  type ProgramConfig,
  ProgramStatus,
} from '@bybeam/platform-types';
import {
  checkFeatureAllPrograms,
  checkFeatureAnyProgram,
  checkFeaturesAnyProgram,
  checkProgramHasAnyFeature,
} from './checkFeature';

const now = new Date();
const mockId = '123';
const mockZelleFeature = {
  name: FeatureName.PaymentsZelle,
} as Feature;
function mockProgram(mockFeature: Feature, isEnabled: boolean, hasFeature: boolean): Program {
  const feature = {
    id: mockId,
    programId: mockId,
    featureId: mockId,
    feature: mockFeature,
    enabled: isEnabled,
    createdAt: now,
    updatedAt: now,
  };
  return {
    id: mockId,
    name: 'Test Program',
    status: ProgramStatus.Open,
    partnerId: mockId,
    partner: PARTNER,
    features: hasFeature ? [feature] : [],
    config: { applicationConfiguration: ApplicationConfigurationKey.NH } as ProgramConfig,
  };
}
function mockPrograms(hasProgramWithFeatureEnabled: boolean): Program[] {
  const programWithFeatureEnabled = mockProgram(mockZelleFeature, true, true);
  const programWithFeatureDisabled = mockProgram(mockZelleFeature, false, true);
  return hasProgramWithFeatureEnabled
    ? [programWithFeatureEnabled, programWithFeatureDisabled]
    : [programWithFeatureDisabled, programWithFeatureDisabled];
}

describe('Checking Programs for a Feature', () => {
  describe('Check an array of programs for a feature', () => {
    it('If the feature is enabled in any program, it returns true', () => {
      expect(checkFeatureAnyProgram(mockPrograms(true), FEATURE.name)).toEqual(true);
    });

    it('If the feature is not enabled in any programs, it returns false', () => {
      expect(checkFeatureAnyProgram(mockPrograms(false), FEATURE.name)).toEqual(false);
    });
  });

  describe('Check an array of features for any program', () => {
    it('If all features are enabled in one program, it returns true', () => {
      const program = {
        ...PROGRAM,
        features: [
          { feature: { name: FeatureName.PaymentsPartnerIssued }, enabled: true },
          { feature: { name: FeatureName.PaymentsDirectDeposit }, enabled: true },
        ],
      } as Program;
      const program2 = {
        ...PROGRAM,
        features: [
          { feature: { name: FeatureName.PaymentsPartnerIssued }, enabled: false },
          { feature: { name: FeatureName.PaymentsDirectDeposit }, enabled: true },
        ],
      } as Program;
      expect(checkFeaturesAnyProgram([program], [FeatureName.PaymentsPartnerIssued])).toEqual(true);
      expect(
        checkFeaturesAnyProgram(
          [program],
          [FeatureName.PaymentsPartnerIssued, FeatureName.PaymentsDirectDeposit],
        ),
      ).toEqual(true);
      expect(
        checkFeaturesAnyProgram(
          [program, program2],
          [FeatureName.PaymentsPartnerIssued, FeatureName.PaymentsDirectDeposit],
        ),
      ).toEqual(true);
    });

    it('If not all feature are enabled in one program, it returns false', () => {
      const program = {
        ...PROGRAM,
        features: [
          { feature: { name: FeatureName.PaymentsPartnerIssued }, enabled: false },
          { feature: { name: FeatureName.PaymentsDirectDeposit }, enabled: true },
        ],
      } as Program;
      expect(
        checkFeaturesAnyProgram(
          [program],
          [FeatureName.PaymentsPartnerIssued, FeatureName.PaymentsDirectDeposit],
        ),
      ).toEqual(false);
    });
  });

  describe('Check that a program has any features enabled from an array', () => {
    it('If at least one feature is enabled for the program, it returns true', () => {
      const program = {
        ...PROGRAM,
        features: [{ feature: { name: FeatureName.PaymentsPartnerIssued }, enabled: true }],
      } as Program;
      expect(
        checkProgramHasAnyFeature(program, [
          FeatureName.PaymentsClaimFunds,
          FeatureName.PaymentsPartnerIssued,
        ]),
      ).toEqual(true);
    });
    it('If none of the features are enabled for the program, it returns false', () => {
      const program = {
        ...PROGRAM,
        features: [{ feature: { name: FeatureName.PaymentsExternalTracking }, enabled: true }],
      } as Program;
      expect(
        checkProgramHasAnyFeature(program, [
          FeatureName.PaymentsClaimFunds,
          FeatureName.PaymentsPartnerIssued,
        ]),
      ).toEqual(false);
    });
  });

  describe('check that all program has a feature', () => {
    it('should return true when all programs have the specified feature enabled', () => {
      const programs = [
        {
          id: 'mockProgramA',
          applicantTypes: [{ applicantType: 'FIRST_PARTY_APPLICANT_TYPE' }],
          features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        },
        {
          id: 'mockProgramB',
          applicantTypes: [{ applicantType: 'FIRST_PARTY_APPLICANT_TYPE' }],
          features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        },
      ] as unknown as Program[];

      expect(checkFeatureAllPrograms(programs, FeatureName.PaymentsClaimFunds)).toBeTruthy();
    });

    it('should return false when at least one program does not have the specified feature', () => {
      const programs = [
        {
          id: 'mockProgramA',
          applicantTypes: [{ applicantType: 'FIRST_PARTY_APPLICANT_TYPE' }],
          features: [{ enabled: true, feature: { name: FeatureName.PaymentsClaimFunds } }],
        },
        {
          id: 'mockProgramB',
          applicantTypes: [{ applicantType: 'FIRST_PARTY_APPLICANT_TYPE' }],
          features: [],
        },
      ] as unknown as Program[];

      expect(checkFeatureAllPrograms(programs, FeatureName.PaymentsClaimFunds)).toBeFalsy();
    });
  });
});
