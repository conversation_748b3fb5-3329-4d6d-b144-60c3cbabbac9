import { checkFeature, checkFeatures } from '@bybeam/platform-lib/features/check';
import type { FeatureName, Partner, Program } from '@bybeam/platform-types';

export function checkPartnerHasFeature(partner: Partner, featureName: FeatureName): boolean {
  return checkFeature(partner?.features, featureName);
}

export function checkFeatureAnyProgram(programs: Program[], featureName: FeatureName): boolean {
  return !!programs.find((program) => checkFeature(program.features, featureName));
}

export function checkFeaturesAnyProgram(programs: Program[], features: FeatureName[]): boolean {
  return !!programs.find((program) => checkFeatures(program?.features, features));
}

export function checkProgramHasAnyFeature(program: Program, features: FeatureName[]): boolean {
  return !!features.find((feature) => checkFeature(program.features, feature));
}

export function checkFeatureAllPrograms(programs: Program[], featureName: FeatureName): boolean {
  return programs.every((program) => checkFeature(program.features, featureName));
}
