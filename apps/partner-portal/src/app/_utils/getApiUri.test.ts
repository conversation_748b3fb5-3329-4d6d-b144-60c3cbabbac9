import { getApiUri } from './getApiUri';

describe('Test Dynamic Graphql API URI generation', () => {
  process.env.NEXT_PUBLIC_API_URL = 'https://api.edquity.co/rental-assistance';
  it('defaults to legacy env variable', () => {
    expect(getApiUri('rentalassistance.edquity.co')).toEqual(
      'https://api.edquity.co/rental-assistance',
    );
  });
  it('generates correct environment URI for bybeam.co', () => {
    expect(getApiUri('app.testenv.bybeam.co')).toEqual('//api.testenv.bybeam.co/rental-assistance');
  });
  it('generates correct prod URI URI for bybeam.co', () => {
    expect(getApiUri('app.bybeam.co')).toEqual('//api.bybeam.co/rental-assistance');
  });
});
