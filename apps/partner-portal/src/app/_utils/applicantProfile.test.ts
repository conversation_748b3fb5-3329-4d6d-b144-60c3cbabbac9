import {
  type ApplicantProfile,
  type ApplicantProfileConfig,
  type ApplicantProfileConfiguration,
  FieldType,
} from '@bybeam/platform-types';
import { getApplicantProfileConfig } from './applicantProfile';

describe('applicantProfile utils', () => {
  const mockConfig: ApplicantProfileConfig = {
    profileKeys: [
      {
        type: FieldType.Date,
        key: 'dateOfBirth',
        label: 'Date of Birth',
      },
    ],
  };
  describe('getApplicantProfileConfig', () => {
    it('if find any config based on applicantTypeId -> returns the config', () => {
      // Action
      const result = getApplicantProfileConfig(
        [
          { applicantTypeId: 'mockApplicantTypeId', config: mockConfig },
        ] as ApplicantProfileConfiguration[],
        { applicantType: { id: 'mockApplicantTypeId' } } as ApplicantProfile,
      );

      // Expectation
      expect(result).toEqual(mockConfig);
    });

    it("if doesn't find the config -> returns undefined", () => {
      // Action
      const result = getApplicantProfileConfig(
        [
          { applicantTypeId: 'mockApplicantTypeId', config: mockConfig },
        ] as ApplicantProfileConfiguration[],
        { applicantType: { id: 'differentTypeId' } } as ApplicantProfile,
      );

      // Expectation
      expect(result).toBeUndefined();
    });

    it('if config is an empty array -> returns undefined', () => {
      // Action
      const result = getApplicantProfileConfig(
        [] as ApplicantProfileConfiguration[],
        { applicantType: { id: 'mockApplicantTypeId' } } as ApplicantProfile,
      );

      // Expectation
      expect(result).toBeUndefined();
    });

    it('if applicant type ID is empty string -> returns undefined', () => {
      // Action
      const result = getApplicantProfileConfig(
        [
          { applicantTypeId: 'mockApplicantTypeId', config: mockConfig },
        ] as ApplicantProfileConfiguration[],
        { applicantType: undefined } as ApplicantProfile,
      );

      // Expectation
      expect(result).toBeUndefined();
    });
  });
});
