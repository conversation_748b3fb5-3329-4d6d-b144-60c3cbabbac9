import isAccountNumberValid from '@/app/_utils/accountNumber';

const validationError = 'Please enter a valid account number';

describe('Account Number Validation', () => {
  it('A string of 10 characters is valid', () => {
    expect(isAccountNumberValid('**********')).toEqual(undefined);
  });
  it('A string of less than 5 characters is invalid', () => {
    expect(isAccountNumberValid('5678')).toEqual(validationError);
  });
  it('A string of more than 17 characters is invalid', () => {
    expect(isAccountNumberValid('**********585858585')).toEqual(validationError);
  });
  it('A string with symbols is invalid', () => {
    expect(isAccountNumberValid('-003132')).toEqual(validationError);
  });
});
