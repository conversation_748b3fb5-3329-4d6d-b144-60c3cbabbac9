import { FeatureName, type Program, ProgramStatus, type User } from '@bybeam/platform-types';
import { checkProgramHasAnyFeature } from './checkFeature';

export const isProgramReferred = (program: Program, user: User): boolean =>
  program.status === ProgramStatus.ReferralOnly &&
  user.referrals?.some(({ program: referralProgram }) => referralProgram.id === program.id);

export function hasApplicantType(program: Program, user: User): boolean {
  return program.applicantTypes?.some(
    ({ applicantType }) => applicantType.id === user.applicantProfile?.applicantType?.id,
  );
}

export function canCreateApplication(program: Program, user: User): boolean {
  return (
    ([ProgramStatus.Open, ProgramStatus.Closed].includes(program.status) ||
      isProgramReferred(program, user)) &&
    hasApplicantType(program, user)
  );
}

export function programHasPayments(program: Program): boolean {
  return checkProgramHasAnyFeature(program, [
    FeatureName.PaymentsClaimFunds,
    FeatureName.PaymentsExternalTracking,
    FeatureName.PaymentsPartnerIssued,
  ]);
}
