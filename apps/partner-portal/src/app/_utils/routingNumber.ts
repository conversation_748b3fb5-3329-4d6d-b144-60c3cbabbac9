/*
The MIT License (MIT)

Copyright (c) 2020 The Guild

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.
*/

/**
 * @explanation Multiplies the first digit by 3, the second digit by 7 and the third digit by 1.
 *
 * Then, multiplies the fourth digit by 3, the fifth digit by 7 and the sixth digit by 1.
 *
 * Then, multiplies the seventh digit by 3, the eighth digit by 7 and the ninth digit by 1.
 *
 * Adds up all of the products, and the result should be evenly divisible by 10 with no remainders.
 * @remarks Pulled from https://github.com/Urigo/graphql-scalars
 */
const isValidCheckSum = (routingNumber: string): boolean => {
  const weight = [3, 7, 1];
  const accumulator = (acc: number, curr: number): number => acc + curr;
  const digits = routingNumber.split('').map((digit) => Number.parseInt(digit, 10));
  const checkDigit = digits.pop();
  const sum = digits.map((digit, index) => digit * weight[index % 3]).reduce(accumulator, 0);
  return (sum + checkDigit) % 10 === 0;
};

const hasNineDigits = /^\d{9}$/;

export default function isRoutingNumberValid(val: string): string | undefined {
  return hasNineDigits.test(val) && isValidCheckSum(val)
    ? undefined
    : 'Please enter a valid routing number';
}
