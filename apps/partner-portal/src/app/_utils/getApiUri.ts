import { unstable_noStore as noStore } from 'next/cache';

export const getApiUri = (hostname = '') => {
  noStore();
  let _hostname = hostname;
  if (typeof window !== 'undefined' && !_hostname) {
    _hostname = window.location.hostname;
  }
  if (_hostname.includes('bybeam.co')) {
    return `//${_hostname.replace('app', 'api')}/rental-assistance`;
  }
  if (typeof process !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL;
  }
  return '';
};
