'use client';
import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import { isEmail } from '@/spa-legacy/common/utilities/email';
import { <PERSON>ton, Flex, Link, TextField } from '@radix-ui/themes';
import { FirebaseError } from 'firebase/app';
import {
  MultiFactorError,
  getAuth,
  isSignInWithEmailLink,
  signInWithEmailLink,
} from 'firebase/auth';
import { Route } from 'next';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { SignInMachineContext } from '../machine/signInMachineContext';
import { FormFieldError } from './components/FormFieldError';
import { friendlyFirebaseError } from './friendlyFirebaseMessage';

interface FormInputs {
  email: string;
}

export const EmailLinkSignInReturn = () => {
  const auth = getAuth();
  const { partnerId } = useParams();
  const email = localStorage.getItem('emailForSignIn') ?? '';
  const actor = SignInMachineContext.useActorRef();
  const state = SignInMachineContext.useSelector((state) => state);
  const [loading, setLoading] = useState(true);
  const [hasError, setHasError] = useState<boolean>();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<FormInputs>({
    defaultValues: {
      email,
    },
  });

  const trySignIn = useCallback(
    (email: string) => {
      signInWithEmailLink(auth, email, window.location.href)
        .then(async (result) => {
          localStorage.removeItem('emailForSignIn');
          actor.send({
            type: 'user.signed.in',
            token: await result.user.getIdToken(),
            gcipUid: result.user.uid,
            authenticationMechanism: 'EMAIL_TOKEN',
          });
          setLoading(false);
        })
        .catch((error) => {
          if (error instanceof FirebaseError && error.code === 'auth/multi-factor-auth-required') {
            actor.send({ type: 'mfa.challenge', mfaError: error as MultiFactorError });
          } else {
            setLoading(false);
            setHasError(true);
            setError('root.error', { message: friendlyFirebaseError(error) });
          }
        });
    },
    [auth, actor.send, setError],
  );

  useEffect(() => {
    if (isSignInWithEmailLink(auth, window.location.href) && email) {
      trySignIn(email);
    } else if (!isSignInWithEmailLink(auth, window.location.href)) {
      router.replace(makeRoute(PORTAL_ROUTES.AUTH_SIGNIN) as Route);
    }
  }, [auth, email, trySignIn]);

  const onSubmit = (data: FormInputs) => {
    if (isEmail(data.email)) {
      trySignIn(data.email);
    } else {
      setError('email', { message: 'Invalid email address' });
    }
  };

  if (!email) {
    return (
      <form onSubmit={handleSubmit(onSubmit)} style={{ width: '100%' }}>
        <Flex direction="column" gap="3" aria-live="polite">
          <FormFieldError message={errors?.root?.error?.message} />
          To ensure your security, please confirm the email address where you received the magic
          link.
          <label>
            Confirm Email Address
            <TextField.Root
              disabled={state.hasTag('loading')}
              {...register('email', { required: 'Email address is required' })}
              placeholder={'Enter your email address'}
              size="3"
              autoFocus={true}
              autoComplete="off"
            />
          </label>
          <FormFieldError message={errors?.email?.message} />
          <Button size="3" type="submit">
            Continue
          </Button>
        </Flex>
      </form>
    );
  }
  if (loading) {
    return <>Signing In...</>;
  }
  if (hasError) {
    return (
      <form onSubmit={handleSubmit(onSubmit)} style={{ width: '100%' }}>
        <Flex direction="column" gap="3" aria-live="polite">
          <FormFieldError message={errors?.root?.error?.message} />
          <Button size="3" asChild>
            {/* This does not use the Next.js Link component on purpose, as we just want a hard normal-browser refresh to reset everything */}
            <Link href={`/partner/${partnerId}/auth/signin`}>Back to Sign In</Link>
          </Button>
        </Flex>
      </form>
    );
  }
};
