import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { Box, Callout, Link } from '@radix-ui/themes';

function ApplicantDirection() {
  return (
    <>
      <Box pt="4">
        <Box pt="6">
          <Callout.Root variant="soft" color="gold">
            <Callout.Icon>
              <InfoOutlinedIcon />
            </Callout.Icon>
            <Callout.Text>
              <strong>Not a case manager?</strong> Go to the{' '}
              <Link href={makeRoute(PORTAL_ROUTES.AUTH_SIGNIN)}>Applicant Portal</Link> to access
              your account.
            </Callout.Text>
          </Callout.Root>
        </Box>
      </Box>
    </>
  );
}

export default ApplicantDirection;
