'use client';
import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import { CanAccessProvider } from '@/app/providers/CanAccessProvider';
import { useNextAuth } from '@/app/providers/NextAuthProvider';
import ThemeProvider from '@/spa-legacy/common/components/ThemeProvider';
import { PageTitleProvider } from '@/spa-legacy/common/hooks/contexts/usePageTitle';
import { SnackbarProvider } from '@/spa-legacy/common/hooks/useSnackbar';
import { Flex } from '@radix-ui/themes';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import NavDrawer from './components/NavDrawer/NavDrawer';
import styles from './layout.module.css';

export default function PortalLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { isAuthenticated, loading } = useNextAuth();
  const requestUriEncoded = encodeURIComponent(window.location.pathname + window.location.search);
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      // @ts-expect-error experimental typedRoutes checks fail
      router.replace(makeRoute(PORTAL_ROUTES.AUTH_SIGNIN, { redirect_uri: requestUriEncoded }));
    }
  }, [isAuthenticated, loading, requestUriEncoded, router]);
  if (loading) {
    return <LoadingComponent message="Loading..." />;
  }
  if (isAuthenticated) {
    return (
      <ThemeProvider>
        <SnackbarProvider>
          <PageTitleProvider>
            <CanAccessProvider>
              <Flex width="100%" height="100vh" position="relative">
                <NavDrawer />
                <main className={styles.appContent}>{children}</main>
              </Flex>
            </CanAccessProvider>
          </PageTitleProvider>
        </SnackbarProvider>
      </ThemeProvider>
    );
  }
}
