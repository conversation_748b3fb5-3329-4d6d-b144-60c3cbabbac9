import { canCreateApplication } from '@/app/_utils/programs';
import useForm, {
  defaultRequiredErrorMessage,
  fieldUpdate,
  requiredField,
  ValidationMode,
} from '@/app/hooks/useForm';
import Dropdown from '@/spa-legacy/common/components/Dropdown';
import Message from '@/spa-legacy/common/components/Message';
import Modal from '@/spa-legacy/common/components/Modal';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useCreateApplication from '@/spa-legacy/portal/hooks/useCreateApplication';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import sort from '@bybeam/platform-lib/utilities/sort';
import { FeatureName, ProgramStatus, type User } from '@bybeam/platform-types';
import { Button, Flex, Text, Theme } from '@radix-ui/themes';
import { type FormEvent, useMemo } from 'react';

interface AddApplicationModalProps {
  onClose: () => void;
  user: User;
}

export default function AddApplicationModal({ user, onClose }: AddApplicationModalProps) {
  const { programs } = usePartner();
  const {
    formData: { programIds },
    trySubmit,
    dispatch,
    errors,
  } = useForm(
    { programIds: [] },
    { programIds: requiredField(undefined, defaultRequiredErrorMessage('Program Name')) },
    ValidationMode.OnSubmit,
  );
  const { doMutation, loading } = useCreateApplication();

  const onSubmit = (e: FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    trySubmit(async (): Promise<void> => {
      const success = await doMutation(user.id, programIds);
      if (success) onClose();
    });
  };

  const programItems = useMemo(
    () =>
      sort(
        programs.filter(
          (program) =>
            checkFeature(program?.features, FeatureName.WorkflowCreateApplications) &&
            canCreateApplication(program, user),
        ),
        { accessor: (program) => program.name },
      ).map(({ id, name, status }) => ({
        value: id,
        label: name,
        secondaryLabel: status === ProgramStatus.Closed && 'Closed Program',
      })),
    [programs, user],
  );

  const selectedClosedPrograms = programIds
    ?.map((id) => programs.find((program) => program.id === id))
    ?.filter((program) => program.status === ProgramStatus.Closed);

  return (
    <Modal size="S" title="Add New Application" onClickClose={onClose} isOpen>
      {/* todo: Replace mui modal with radix modal */}
      <Theme accentColor="indigo">
        <form onSubmit={onSubmit} noValidate>
          <Flex direction="column" gap="2" mb="6">
            <Text>
              Please select the program(s) you&apos;d like to create an application for{' '}
              <strong>{user.name}</strong>.
            </Text>
            <Dropdown
              id="add-application-programs"
              label="Program Name"
              required
              onChange={(value: string[]) => dispatch(fieldUpdate('programIds', value))}
              value={programIds}
              items={programItems}
              multiple
              fullWidth
            />
          </Flex>
          {errors.programIds && (
            <Message variant="error">You must select a program to continue.</Message>
          )}

          {!!selectedClosedPrograms?.length && (
            <Message variant="warning">
              You are adding a new application to {selectedClosedPrograms.length} closed program(s).
            </Message>
          )}
          <Flex gap="3" justify="end">
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" loading={loading}>
              Add New Application
            </Button>
          </Flex>
        </form>
      </Theme>
    </Modal>
  );
}
