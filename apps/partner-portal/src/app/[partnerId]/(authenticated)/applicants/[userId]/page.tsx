'use client';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import Page from '@/app/components/ui/Page/Page';
import SidesheetToggle from '@/app/components/ui/Page/Parts/SidesheetToggle';
import { useSidesheet } from '@/app/providers/SidesheetProvider';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import Notes from '@/spa-legacy/portal/components/Notes';
import { hasReferral } from '@/spa-legacy/portal/utils/applications';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkFeatureAnyProgram } from '@/spa-legacy/utilities/checkFeature';
import { useQuery } from '@apollo/client';
import { FeatureName } from '@bybeam/platform-types';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Box, Flex, Heading, Separator, Tabs, Text, Tooltip } from '@radix-ui/themes';
import Link from 'next/link';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useCallback } from 'react';
import GetApplicantData from './GetApplicantData.graphql';
import ApplicantOverview from './_components/ApplicantOverview';
import ApplicationsTable from './_components/ApplicationsTable';
import PaymentsTable from './_components/PaymentsTable';
import ProgramsTable from './_components/ProgramsTable';
import styles from './page.module.css';

export default function ApplicantViewPage() {
  const { userId } = useParams<{ userId: string }>();
  const { isSidesheetOpen } = useSidesheet({ open: true });
  const { programs: partnerPrograms } = usePartner();
  const router = useRouter();
  const searchParams = useSearchParams();
  const selectedTab = searchParams.get('tab') || 'programs';

  const setSelectedTab = useCallback(
    (tab: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set('tab', tab);
      router.push(`?${params.toString()}`, { scroll: false });
    },
    [router, searchParams],
  );

  const partnerIssuedPaymentsEnabled = checkFeatureAnyProgram(
    partnerPrograms,
    FeatureName.PaymentsPartnerIssued,
  );

  const { data, loading, error } = useQuery(GetApplicantData, {
    variables: { filter: { id: userId }, bankAccount: partnerIssuedPaymentsEnabled },
    fetchPolicy: 'cache-and-network',
  });

  const user = data?.users?.users?.[0];
  const programs = data?.programs?.programs ?? [];

  // Check if user has any referrals
  const hasReferrals = user?.applications?.some(hasReferral) ?? false;

  const notes = user
    ? [
        ...(user.applicantProfile?.notes ?? []).map((note) => ({
          ...note,
          customLabel: 'Applicant Profile',
        })),
        ...(user.applications?.flatMap((app) => {
          const programName = app.case?.program?.name;
          return (app.case?.notes ?? []).map((note) => ({
            ...note,
            customLabel:
              programName && programName.length > 64
                ? `${programName.substring(0, 64)}...`
                : programName,
          }));
        }) ?? []),
      ]
    : [];

  const hasError = !loading && (error || !user);

  return (
    <Page>
      <Page.Header>
        <Page.Breadcrumbs>
          <Page.Breadcrumbs.Crumb>
            <Link href={{ pathname: makeRoute(PORTAL_ROUTES.APPLICANTS) }}>Applicants</Link>
          </Page.Breadcrumbs.Crumb>
          <Page.Breadcrumbs.Crumb>
            {loading ? (
              <LoadingComponent />
            ) : (
              <Flex display="inline-flex" gap="2" align="center">
                <span>
                  {user?.displayId && `${user.displayId} • `}
                  {user?.name || 'Applicant Profile'}
                </span>
                {hasReferrals && (
                  <Tooltip content="Referred">
                    <InfoCircledIcon />
                  </Tooltip>
                )}
              </Flex>
            )}
          </Page.Breadcrumbs.Crumb>
        </Page.Breadcrumbs>
        <SidesheetToggle />
      </Page.Header>
      <Page.Content>
        {loading ? (
          <LoadingComponent />
        ) : (
          <Flex height="100%">
            <Box flexGrow="1" overflowY="auto" p="0" className={styles.mainContent}>
              {hasError ? (
                <Flex direction="column" align="center" justify="center" p="8" gap="4">
                  <Heading size="5">
                    {error ? 'Error Loading Applicant' : 'Applicant Not Found'}
                  </Heading>
                  <Text color="gray">
                    {error
                      ? 'There was an error loading this applicant. Please try again.'
                      : "The applicant you're looking for could not be found or you may not have permission to view this profile."}
                  </Text>
                </Flex>
              ) : (
                <Flex direction="column" gap="2" flexGrow="1" width="100%">
                  <Box className={styles.stickyTabs}>
                    <Tabs.Root value={selectedTab} onValueChange={setSelectedTab}>
                      <Tabs.List>
                        <Tabs.Trigger value="programs">Programs</Tabs.Trigger>
                        <Tabs.Trigger value="applications">Applications</Tabs.Trigger>
                        <Tabs.Trigger value="payments">Payments</Tabs.Trigger>
                      </Tabs.List>
                    </Tabs.Root>
                  </Box>
                  <Box width="100%" maxWidth="1200px" mx="auto" pt="8" px="4">
                    {/* TODO: Replace with nextjs route segments if we decide to keep tabs here  */}
                    {selectedTab === 'programs' && (
                      <ProgramsTable user={user} programs={programs} />
                    )}
                    {selectedTab === 'applications' && <ApplicationsTable user={user} />}
                    {selectedTab === 'payments' && <PaymentsTable user={user} />}
                  </Box>
                  {user?.applicantProfile?.id && (
                    <Box maxWidth="1200px" width="100%" mx="auto" px="4">
                      <Separator size="4" my="6" />
                      <Heading size="4" as="h2" mb="4">
                        Activity
                      </Heading>
                      <Notes
                        relation={{ id: user.applicantProfile.id, type: 'profile' }}
                        notes={notes}
                        loading={loading}
                        error={false}
                      />
                    </Box>
                  )}
                </Flex>
              )}
            </Box>
            {isSidesheetOpen && (
              <aside className={styles.sidesheet}>
                {hasError ? (
                  <Flex direction="column" align="center" justify="center" p="8" gap="2">
                    <Heading size="3">{error ? 'Error' : 'No Applicant Data'}</Heading>
                    <Text color="gray" align="center">
                      {error ? 'Unable to load applicant information.' : 'Applicant not found.'}
                    </Text>
                  </Flex>
                ) : (
                  <ApplicantOverview user={user} />
                )}
              </aside>
            )}
          </Flex>
        )}
      </Page.Content>
    </Page>
  );
}
