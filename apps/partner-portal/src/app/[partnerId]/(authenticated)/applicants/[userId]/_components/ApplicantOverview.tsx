import { getApplicantProfileConfig } from '@/app/_utils/applicantProfile';
import { checkFeatureAnyProgram } from '@/app/_utils/checkFeature';
import { canCreateApplication } from '@/app/_utils/programs';
import CanAccess from '@/app/components/features/CanAccess';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import EditApplicantModal from '@/spa-legacy/portal/components/EditApplicantModal';
import { getFormattedProfileAnswer } from '@/spa-legacy/portal/utils/applicantProfile';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName, type User } from '@bybeam/platform-types';
import { Pencil1Icon, PlusIcon } from '@radix-ui/react-icons';
import { Box, Button, DataList, Flex, Heading, Separator, Text } from '@radix-ui/themes';
import { useState } from 'react';
import AddApplicationModal from './AddApplicationModal';
import ApplicantDetails, {
  knownProfileKeys,
  type PartnerConfigProfileKey,
} from './ApplicantDetails';

interface ApplicantOverviewProps {
  user: User;
}

export default function ApplicantOverview({ user }: ApplicantOverviewProps) {
  const { applicantProfileConfigs, programs } = usePartner();
  const [addApplicationModalOpen, setAddApplicationModalOpen] = useState(false);

  const config = getApplicantProfileConfig(applicantProfileConfigs, user.applicantProfile);
  const extendedWorkflowEnabled = checkFeatureAnyProgram(programs, FeatureName.WorkflowExtended);
  const partnerIssuedPaymentsEnabled = checkFeatureAnyProgram(
    programs,
    FeatureName.PaymentsPartnerIssued,
  );
  const applicantPaymentEnabled = checkFeatureAnyProgram(programs, FeatureName.PaymentsApplicants);
  const directDepositEnabled = checkFeatureAnyProgram(programs, FeatureName.PaymentsDirectDeposit);

  const hasCreateApplicationEnabled = checkFeatureAnyProgram(
    programs,
    FeatureName.WorkflowCreateApplications,
  );

  const eligiblePrograms = programs?.filter(
    (program) =>
      checkFeature(program?.features, FeatureName.WorkflowCreateApplications) &&
      user &&
      canCreateApplication(program, user),
  );

  const additionalFields = config?.profileKeys
    ? [...config.profileKeys].filter(
        (profileKey) => !knownProfileKeys.includes(profileKey.key as PartnerConfigProfileKey),
      )
    : null;

  return (
    <Flex direction="column">
      {addApplicationModalOpen && user && (
        <AddApplicationModal onClose={(): void => setAddApplicationModalOpen(false)} user={user} />
      )}
      {hasCreateApplicationEnabled && (
        <Flex p="4" align="center" justify="center">
          <CanAccess>
            <Button
              onClick={(): void => setAddApplicationModalOpen(true)}
              disabled={!eligiblePrograms?.length}
              size="2"
            >
              <PlusIcon />
              Add New Application
            </Button>
          </CanAccess>
        </Flex>
      )}
      <Separator size="4" />
      <Box p="4">
        <Flex justify="between" align="center" mb="3">
          <Heading size="3" as="h2">
            Applicant Overview
          </Heading>
          <CanAccess>
            <EditApplicantModal
              dialogTrigger={
                <Button variant="outline" size="1">
                  <Pencil1Icon />
                  Edit
                </Button>
              }
              applicant={user}
            />
          </CanAccess>
        </Flex>
        <ApplicantDetails
          user={user}
          features={{
            extendedWorkflowEnabled,
            partnerIssuedPaymentsEnabled,
            applicantPaymentEnabled,
            directDepositEnabled,
          }}
        />
      </Box>
      {additionalFields && additionalFields.length > 0 && (
        <>
          <Separator size="4" />
          <Box p="4">
            <Heading size="3" as="h2" mb="3">
              Additional Information
            </Heading>
            <DataList.Root>
              {additionalFields.map((field) => (
                <DataList.Item key={field.key}>
                  <DataList.Label>{field?.label}</DataList.Label>
                  <DataList.Value>
                    <Text truncate>
                      {getFormattedProfileAnswer(user, config, programs, field.key) ?? '-'}
                    </Text>
                  </DataList.Value>
                </DataList.Item>
              ))}
            </DataList.Root>
          </Box>
        </>
      )}
    </Flex>
  );
}
