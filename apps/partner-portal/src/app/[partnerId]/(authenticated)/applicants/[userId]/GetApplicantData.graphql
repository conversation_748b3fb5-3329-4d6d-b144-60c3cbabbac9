#import "../../../../../spa-legacy/portal/graphql/fragments/ApplicationVerificationFragment.graphql"
#import "../../../../../spa-legacy/graphql/fragments/ProgramApplicantTypeFragment.graphql"
#import "../../../../../spa-legacy/graphql/fragments/FeatureSettingFragment.graphql"
#import "../../../../../spa-legacy/graphql/fragments/BankAccountFragment.graphql"
#import "../../../../../spa-legacy/portal/graphql/fragments/ApplicantProfileFragment.graphql"
#import "../../../../../spa-legacy/portal/graphql/fragments/NoteFragment.graphql"

query GetApplicantData($filter: UserFilter!, $bankAccount: Boolean = false) {
  users(filter: $filter) {
    users {
      id
      displayId
      legacyId
      name
      phone
      email
      taxId
      newEmail
      bankAccount @include(if: $bankAccount) {
        ...BankAccountFragment
      }
      createdAt
      applicantProfile {
        ...ApplicantProfileFragment
        applicantType {
          id
        }
        notes {
          ...NoteFragment
        }
      }
      
      applications {
        id
        displayId
        createdAt
        verification {
          ...ApplicationVerificationFragment
        }
        case {
          id
          displayId
          status
          decisionReachedAt
          priority
          notes {
            ...NoteFragment
          }
          fulfillments {
            id
            scheduleType
            paymentPattern {
              id
              totalAmount
            }
            fund {
              id
              name
            }
            payments {
              id
              amount
              initiatedAt
              scheduledFor
              completedAt
              method
              status
              payeeType
              payee {
                __typename
                ... on Vendor {
                  id
                  name
                }
                ... on User {
                  id
                  name
                }
              }
            }
          }
          program {
            id
            name
            status
            features {
              ...FeatureSettingFragment
            }  
          }
        }
        referral {
          id
        }
      }

      referrals {
        id
        program { id }
        createdAt 
      }
    }
  }
  programs{
    programs {
      id
      name
      status
      applicantTypes {
        ...ProgramApplicantTypeFragment
      }
      funds {
        id
        startingBalance
        stats {
          remainingBalance
        }
      }
      features {
        ...FeatureSettingFragment
      }
    }
  }
}
