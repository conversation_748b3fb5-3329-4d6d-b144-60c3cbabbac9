import SendReferralModal from '@/app/components/features/SendReferralModal';
import VerificationStatusComponent from '@/app/components/features/Verification/VerificationStatus';
import { useCanAccess } from '@/app/providers/CanAccessProvider';
import Icon from '@/spa-legacy/common/components/Icon';
import useModal from '@/spa-legacy/common/components/Modal/useModal';
import Color from '@/spa-legacy/common/utilities/Color';
import { DateFormat, formatCurrency, formatDate } from '@/spa-legacy/common/utilities/format';
import { sum } from '@/spa-legacy/common/utilities/math';
import { formatCaseStatus } from '@/spa-legacy/common/utilities/statuses';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import { hasReferral } from '@/spa-legacy/portal/utils/applications';
import { getVerification } from '@/spa-legacy/portal/utils/verification';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkFeatureAnyProgram } from '@/spa-legacy/utilities/checkFeature';
import { hasApplicantType, programHasPayments } from '@/spa-legacy/utilities/programs';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import sort from '@bybeam/platform-lib/utilities/sort';
import {
  EXPEDITED_PRIORITIES,
  FeatureName,
  type Program,
  ProgramStatus,
  type User,
} from '@bybeam/platform-types';
import { Button, Flex, Link, Table, Text, Tooltip } from '@radix-ui/themes';
import dayjs from 'dayjs';
import NextLink from 'next/link';

interface ProgramsTableProps {
  user: User;
  programs: Program[];
}

export default function ProgramsTable({ user, programs }: ProgramsTableProps) {
  const { canAccess } = useCanAccess();
  const { ModalComponent, openModal, closeModal } = useModal();

  const openReferralModal = (program: Program) => {
    openModal({
      title: 'Send Referral',
      children: <SendReferralModal user={user} program={program} onClose={closeModal} />,
    });
  };

  const showReferralColumns = checkFeatureAnyProgram(programs, FeatureName.ProgramsReferral);

  // Sort applications and build program status
  const sortedApplications = sort(user.applications ?? [], {
    accessor: (app) => dayjs(app.createdAt),
    ascending: false,
  });

  const programsWithStatus = programs
    .filter((program) => hasApplicantType(program, user))
    .map((program) => {
      const latestApplication = sortedApplications.find(
        (app) => app.case?.program.id === program.id,
      );
      const remainingFunds = sum(
        (program.funds ?? []).map((fund) => fund.stats?.remainingBalance ?? 0),
      );
      const referral = (user.referrals ?? []).find((ref) => ref.program.id === program.id);

      const enableReferral =
        program.status !== ProgramStatus.Closed &&
        checkFeature(program?.features, FeatureName.ProgramsReferral) &&
        (remainingFunds > 0 || !programHasPayments(program)) &&
        !latestApplication &&
        !referral &&
        canAccess;

      return {
        program,
        latestApplication,
        remainingFunds: programHasPayments(program) ? remainingFunds : undefined,
        referral,
        enableReferral,
      };
    });

  return (
    <>
      {ModalComponent}
      <Table.Root variant="surface">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell>Program Name</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Program Status</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Case Status</Table.ColumnHeaderCell>
            {showReferralColumns && (
              <>
                <Table.ColumnHeaderCell>Remaining Funds</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Referral</Table.ColumnHeaderCell>
              </>
            )}
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {programsWithStatus.length === 0 ? (
            <Table.Row>
              <Table.Cell colSpan={showReferralColumns ? 5 : 3}>
                <Text color="gray">No data available</Text>
              </Table.Cell>
            </Table.Row>
          ) : (
            programsWithStatus.map(
              ({ program, latestApplication, remainingFunds, referral, enableReferral }) => {
                const verification = getVerification(latestApplication);
                const priority = latestApplication?.case?.priority;
                const caseStatus = latestApplication?.case?.status;

                return (
                  <Table.Row key={program.id}>
                    <Table.Cell>
                      <Flex align="center" gap="1">
                        {program.name}
                        {typeof verification?.confidence === 'number' && (
                          <VerificationStatusComponent
                            confidence={verification.confidence}
                            variant="icon-only"
                            showDialog={false}
                          />
                        )}
                        {!!priority && EXPEDITED_PRIORITIES.has(priority) && (
                          <Tooltip content="This application is expedited.">
                            <Icon size="MD" color={Color.Error} type="flag" />
                          </Tooltip>
                        )}
                        {hasReferral(latestApplication) && (
                          <Tooltip content="Referred">
                            <Icon size="MD" color={Color.Text} type="referral" />
                          </Tooltip>
                        )}
                      </Flex>
                    </Table.Cell>
                    <Table.Cell>{program.status}</Table.Cell>
                    <Table.Cell>
                      {caseStatus && latestApplication?.case ? (
                        <Link asChild>
                          <NextLink
                            href={{
                              pathname: makeRoute(PORTAL_ROUTES.CASE_REVIEW, {
                                programId: program.id,
                                caseId: latestApplication.case.id,
                              }),
                            }}
                          >
                            {formatCaseStatus(caseStatus)}
                          </NextLink>
                        </Link>
                      ) : (
                        'Not Applied'
                      )}
                    </Table.Cell>
                    {showReferralColumns && (
                      <>
                        <Table.Cell>
                          {remainingFunds !== undefined ? formatCurrency(remainingFunds) : '-'}
                        </Table.Cell>
                        <Table.Cell>
                          {enableReferral ? (
                            <Button
                              type="button"
                              variant="ghost"
                              onClick={() => openReferralModal(program)}
                            >
                              Send Referral
                            </Button>
                          ) : referral?.createdAt ? (
                            `Sent: ${formatDate(referral.createdAt, DateFormat.Slashes)}`
                          ) : (
                            '-'
                          )}
                        </Table.Cell>
                      </>
                    )}
                  </Table.Row>
                );
              },
            )
          )}
        </Table.Body>
      </Table.Root>
    </>
  );
}
