import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import VerificationStatusComponent from '@/app/components/features/Verification/VerificationStatus';
import Icon from '@/spa-legacy/common/components/Icon';
import Color from '@/spa-legacy/common/utilities/Color';
import { DateFormat, formatDate } from '@/spa-legacy/common/utilities/format';
import { formatCaseStatus } from '@/spa-legacy/common/utilities/statuses';
import { hasReferral } from '@/spa-legacy/portal/utils/applications';
import { getVerification } from '@/spa-legacy/portal/utils/verification';
import { CaseStatus, EXPEDITED_PRIORITIES, type Program, type User } from '@bybeam/platform-types';
import { Flex, Link, Table, Text, Tooltip } from '@radix-ui/themes';
import dayjs from 'dayjs';
import NextLink from 'next/link';

interface ApplicationsTableProps {
  user: User;
}

export default function ApplicationsTable({ user }: ApplicationsTableProps) {
  const applications = user.applications ?? [];

  return (
    <Table.Root variant="surface">
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeaderCell>Application ID</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Program Name</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Case ID</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Case Status</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Decision Date</Table.ColumnHeaderCell>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {applications.length === 0 ? (
          <Table.Row>
            <Table.Cell colSpan={5}>
              <Text color="gray">No data available</Text>
            </Table.Cell>
          </Table.Row>
        ) : (
          applications.map((application) => {
            const program = application.case?.program as Program;
            const verification = getVerification(application);
            const priority = application.case?.priority;
            const caseStatus = application.case?.status as CaseStatus;

            return (
              <Table.Row key={application.id}>
                <Table.Cell>
                  {application.case?.id ? (
                    <Link asChild>
                      <NextLink
                        href={{
                          pathname: makeRoute(PORTAL_ROUTES.APPLICATION_REVIEW_LEGACY, {
                            caseId: application.case.id,
                            applicationId: application.id,
                          }),
                        }}
                      >
                        {application.displayId}
                      </NextLink>
                    </Link>
                  ) : (
                    application.displayId
                  )}
                </Table.Cell>
                <Table.Cell>
                  <Flex align="center" gap="1">
                    {program?.name}
                    {typeof verification?.confidence === 'number' && (
                      <VerificationStatusComponent
                        confidence={verification.confidence}
                        variant="icon-only"
                        showDialog={false}
                      />
                    )}
                    {!!priority && EXPEDITED_PRIORITIES.has(priority) && (
                      <Tooltip content="This application is expedited.">
                        <Icon size="MD" color={Color.Error} type="flag" />
                      </Tooltip>
                    )}
                    {hasReferral(application) && (
                      <Tooltip content="Referred">
                        <Icon size="MD" color={Color.Text} type="referral" />
                      </Tooltip>
                    )}
                  </Flex>
                </Table.Cell>
                <Table.Cell>
                  {application.case?.id && program?.id ? (
                    <Link asChild>
                      <NextLink
                        href={{
                          pathname: makeRoute(PORTAL_ROUTES.CASE_REVIEW, {
                            programId: program.id,
                            caseId: application.case.id,
                          }),
                        }}
                      >
                        {application.case.displayId}
                      </NextLink>
                    </Link>
                  ) : (
                    application.case?.displayId
                  )}
                </Table.Cell>
                <Table.Cell>{formatCaseStatus(caseStatus)}</Table.Cell>
                <Table.Cell>
                  {application.case?.decisionReachedAt
                    ? formatDate(
                        dayjs(application.case.decisionReachedAt).toDate(),
                        DateFormat.Slashes,
                      )
                    : '-'}
                </Table.Cell>
              </Table.Row>
            );
          })
        )}
      </Table.Body>
    </Table.Root>
  );
}
