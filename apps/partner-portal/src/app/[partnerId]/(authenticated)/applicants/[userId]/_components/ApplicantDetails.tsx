import Tooltip from '@/spa-legacy/common/components/Tooltip';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { formatAddress, formatIfPhone, mask } from '@/spa-legacy/common/utilities/format';
import { AccountTypesDisplay } from '@/spa-legacy/common/utilities/payment';
import DisplayEmail from '@/spa-legacy/portal/components/DisplayEmail';
import {
  getFormattedProfileAnswer,
  getPartnerConfig,
  getPartnerConfigLabel,
} from '@/spa-legacy/portal/utils/applicantProfile';
import { getApplicantProfileConfig } from '@/spa-legacy/utilities/applicantProfile';
import type { User } from '@bybeam/platform-types';
import { ExclamationTriangleIcon } from '@radix-ui/react-icons';
import { DataList, Flex, Text } from '@radix-ui/themes';

export enum PartnerConfigProfileKey {
  studentId = 'studentId',
  dateOfBirth = 'dateOfBirth',
}

export const knownProfileKeys = [
  PartnerConfigProfileKey.studentId,
  PartnerConfigProfileKey.dateOfBirth,
];

export interface ApplicantDetailsProps {
  user: User;
  features: {
    extendedWorkflowEnabled: boolean;
    partnerIssuedPaymentsEnabled: boolean;
    applicantPaymentEnabled: boolean;
    directDepositEnabled: boolean;
  };
}

export default function ApplicantDetails({ user, features }: ApplicantDetailsProps) {
  const { applicantProfileConfigs, programs } = usePartner();
  const partnerConfig = getApplicantProfileConfig(applicantProfileConfigs, user?.applicantProfile);
  return (
    <DataList.Root>
      <DataList.Item>
        <DataList.Label>Name</DataList.Label>
        <DataList.Value>
          <Text truncate>{user.name}</Text>
        </DataList.Value>
      </DataList.Item>
      {features.extendedWorkflowEnabled && (
        <DataList.Item>
          <DataList.Label>Legacy ID</DataList.Label>
          <DataList.Value>
            <Text truncate>{user.legacyId ?? '-'}</Text>
          </DataList.Value>
        </DataList.Item>
      )}
      <DataList.Item>
        <DataList.Label>User ID</DataList.Label>
        <DataList.Value>
          <Text truncate>{user.displayId}</Text>
        </DataList.Value>
      </DataList.Item>
      <DataList.Item>
        <DataList.Label>Phone</DataList.Label>
        <DataList.Value>
          <Text truncate>{user.phone ? formatIfPhone(user.phone) : '-'}</Text>
        </DataList.Value>
      </DataList.Item>
      <DataList.Item>
        <DataList.Label>Email</DataList.Label>
        <DataList.Value>
          <Flex gap="2" align="center" minWidth="0">
            <Flex minWidth="0">
              <DisplayEmail email={user.email} />
            </Flex>
            {user.newEmail && (
              <Tooltip
                title={`A verification email has been sent to ${user.newEmail}. An applicant must verify the new email before it displays.`}
              >
                <Text color="red">
                  <ExclamationTriangleIcon />
                </Text>
              </Tooltip>
            )}
          </Flex>
        </DataList.Value>
      </DataList.Item>
      {features.extendedWorkflowEnabled && (
        <DataList.Item>
          <DataList.Label>Secondary Email</DataList.Label>
          <DataList.Value>
            <Flex gap="2" minWidth="0">
              {user.applicantProfile?.secondaryEmail ? (
                <Flex minWidth="0">
                  <DisplayEmail email={user.applicantProfile?.secondaryEmail} />
                </Flex>
              ) : (
                '-'
              )}
            </Flex>
          </DataList.Value>
        </DataList.Item>
      )}
      <DataList.Item>
        <DataList.Label>Mailing Address</DataList.Label>
        <DataList.Value>
          <Text truncate>
            {user.applicantProfile?.mailingAddress
              ? formatAddress(user.applicantProfile?.mailingAddress)
              : '-'}
          </Text>
        </DataList.Value>
      </DataList.Item>
      {features.partnerIssuedPaymentsEnabled && features.applicantPaymentEnabled && (
        <DataList.Item>
          <DataList.Label>Tax ID/SSN</DataList.Label>
          <DataList.Value>
            <Text truncate>{user.taxId ? mask(user.taxId) : '-'}</Text>
          </DataList.Value>
        </DataList.Item>
      )}
      {features.partnerIssuedPaymentsEnabled &&
        features.applicantPaymentEnabled &&
        features.directDepositEnabled && (
          <>
            <DataList.Item>
              <DataList.Label>Account Type</DataList.Label>
              <DataList.Value>
                <Text truncate>{AccountTypesDisplay[user.bankAccount?.accountType] ?? '-'}</Text>
              </DataList.Value>
            </DataList.Item>
            <DataList.Item>
              <DataList.Label>Routing Number</DataList.Label>
              <DataList.Value>
                <Text truncate>{user.bankAccount?.routingNumber ?? '-'}</Text>
              </DataList.Value>
            </DataList.Item>
            <DataList.Item>
              <DataList.Label>Account Number</DataList.Label>
              <DataList.Value>
                <Text truncate>{user.bankAccount?.accountNumber ?? '-'}</Text>
              </DataList.Value>
            </DataList.Item>
          </>
        )}
      {user.applicantProfile &&
        partnerConfig &&
        knownProfileKeys
          .filter((profileKey) => !!getPartnerConfig(partnerConfig, profileKey))
          .map((profileKey) => (
            <DataList.Item key={profileKey as string}>
              <DataList.Label>{getPartnerConfigLabel(partnerConfig, profileKey)}</DataList.Label>
              <DataList.Value>
                <Text truncate>
                  {getFormattedProfileAnswer(user, partnerConfig, programs, profileKey) ?? '-'}
                </Text>
              </DataList.Value>
            </DataList.Item>
          ))}
    </DataList.Root>
  );
}
