import VerificationStatusComponent from '@/app/components/features/Verification/VerificationStatus';
import Icon from '@/spa-legacy/common/components/Icon';
import Color from '@/spa-legacy/common/utilities/Color';
import { DateFormat, formatCurrency, formatDate } from '@/spa-legacy/common/utilities/format';
import {
  PaymentMethodDisplay,
  PaymentStatusDisplay,
  getExternalPaymentStatusDisplay,
} from '@/spa-legacy/common/utilities/payment';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import { hasReferral } from '@/spa-legacy/portal/utils/applications';
import { getVerification } from '@/spa-legacy/portal/utils/verification';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import {
  EXPEDITED_PRIORITIES,
  FeatureName,
  PaymentStatus,
  type Program,
  ScheduleType,
  type User,
} from '@bybeam/platform-types';
import { Box, Flex, Link, Table, Text, Tooltip } from '@radix-ui/themes';
import dayjs from 'dayjs';
import NextLink from 'next/link';

interface PaymentsTableProps {
  user: User;
}

export default function PaymentsTable({ user }: PaymentsTableProps) {
  // Flatten all payments from all applications
  const payments = (user.applications ?? []).flatMap((application) => {
    const fulfillments = application?.case?.fulfillments || [];
    return fulfillments.flatMap((fulfillment) => {
      const paymentsForFulfillment = fulfillment?.payments || [];
      return paymentsForFulfillment
        .map((payment) => {
          const program = application.case?.program as Program;
          const hasExternalTracking = checkFeature(
            program?.features,
            FeatureName.PaymentsExternalTracking,
          );

          // Hide pending payments unless external tracking enabled
          if (!hasExternalTracking && payment.status === PaymentStatus.Pending) {
            return null;
          }

          return { application, fulfillment, payment, program, hasExternalTracking };
        })
        .filter(Boolean);
    });
  });

  return (
    <Table.Root variant="surface">
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeaderCell>Case ID</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Program Name</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Fund</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Date</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Amount</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Payment Method</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Payment Status</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Payee Name</Table.ColumnHeaderCell>
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {payments.length === 0 ? (
          <Table.Row>
            <Table.Cell colSpan={8}>
              <Text color="gray">No data available</Text>
            </Table.Cell>
          </Table.Row>
        ) : (
          payments.map(
            ({ application, fulfillment, payment, program, hasExternalTracking }, index) => {
              const verification = getVerification(application);
              const priority = application.case?.priority;

              // Payment date logic
              let paymentDate: dayjs.Dayjs | undefined;
              if (fulfillment.scheduleType === ScheduleType.Recurring) {
                const date = payment?.initiatedAt || payment?.scheduledFor;
                paymentDate = date ? dayjs(date) : undefined;
              } else {
                paymentDate = payment?.completedAt ? dayjs(payment.completedAt) : undefined;
              }

              // Payment status logic
              let paymentStatus: string;
              if (hasExternalTracking) {
                paymentStatus = getExternalPaymentStatusDisplay(payment?.status) ?? '-';
              } else if (fulfillment.scheduleType === ScheduleType.Recurring) {
                paymentStatus = payment.initiatedAt
                  ? PaymentStatusDisplay[payment.status]
                  : 'Scheduled';
              } else {
                paymentStatus = PaymentStatusDisplay[payment?.status] ?? '-';
              }

              return (
                <Table.Row key={payment.id}>
                  <Table.Cell>
                    {application.case?.id && program?.id ? (
                      <Link asChild>
                        <NextLink
                          href={{
                            pathname: makeRoute(PORTAL_ROUTES.CASE_REVIEW, {
                              programId: program.id,
                              caseId: application.case.id,
                            }),
                          }}
                        >
                          {application.case.displayId}
                        </NextLink>
                      </Link>
                    ) : (
                      application.case?.displayId
                    )}
                  </Table.Cell>
                  <Table.Cell>
                    <Flex align="center" gap="1">
                      {program?.name}
                      {typeof verification?.confidence === 'number' && (
                        <VerificationStatusComponent
                          confidence={verification.confidence}
                          variant="icon-only"
                          showDialog={false}
                        />
                      )}
                      {!!priority && EXPEDITED_PRIORITIES.has(priority) && (
                        <Tooltip content="This application is expedited.">
                          <Icon size="MD" color={Color.Error} type="flag" />
                        </Tooltip>
                      )}
                      {hasReferral(application) && (
                        <Tooltip content="Referred">
                          <Icon size="MD" color={Color.Text} type="referral" />
                        </Tooltip>
                      )}
                    </Flex>
                  </Table.Cell>
                  <Table.Cell>
                    <Box minWidth="5rem" maxWidth="6rem">
                      <Text truncate>{fulfillment.fund?.name ?? '-'}</Text>
                    </Box>
                  </Table.Cell>
                  <Table.Cell>
                    {paymentDate ? formatDate(paymentDate.toDate(), DateFormat.Slashes) : '-'}
                  </Table.Cell>
                  <Table.Cell>{formatCurrency(payment.amount)}</Table.Cell>
                  <Table.Cell>{PaymentMethodDisplay[payment.method] ?? '-'}</Table.Cell>
                  <Table.Cell>{paymentStatus}</Table.Cell>
                  <Table.Cell>{payment.payee?.name ?? '-'}</Table.Cell>
                </Table.Row>
              );
            },
          )
        )}
      </Table.Body>
    </Table.Root>
  );
}
