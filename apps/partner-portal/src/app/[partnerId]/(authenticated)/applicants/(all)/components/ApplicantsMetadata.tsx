import { checkFeatureAnyProgram } from '@/app/_utils/checkFeature';
import { DL, DLItem } from '@/app/components/ui/DescriptionList';
import Loading from '@/spa-legacy/common/components/Loading';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import usePollingQuery from '@/spa-legacy/common/hooks/usePollingQuery';
import { formatCurrency, formatNumber } from '@/spa-legacy/common/utilities/format';
import {
  type AggregateApplicantsReferred,
  type AggregatePayments,
  FeatureName,
  type Page,
  type User,
} from '@bybeam/platform-types';
import GetApplicantsOverviewQuery from '../graphql/GetApplicantsOverviewQuery.graphql';
import styles from './ApplicantsMetadata.module.css';

interface ApplicantsOverview {
  aggregatePayment: AggregatePayments;
  applicantsReferred: AggregateApplicantsReferred;
  users: Page<User>;
}

export default function SummaryMetrics(): JSX.Element {
  const partner = usePartner();
  const isProgramsReferralEnabled = checkFeatureAnyProgram(
    partner.programs,
    FeatureName.ProgramsReferral,
  );

  const { data, loading, error, lastUpdate } = usePollingQuery<ApplicantsOverview>(
    GetApplicantsOverviewQuery,
    {
      variables: { skipReferral: !isProgramsReferralEnabled },
    },
  );

  if (loading) return <Loading size="XXL" />;
  if (error) return <div>Uh oh! An error occurred.</div>;

  return (
    <div className={styles.activity}>
      <div className={styles.activityTitle}>
        <strong>Activity</strong>
      </div>
      <DL>
        <DLItem label="Total Applicants" value={formatNumber(data.users.pageInfo.count)} />
        <DLItem label="Total Paid" value={formatCurrency(data.aggregatePayment.sum)} />
        <DLItem label="Payments Sent" value={formatNumber(data.aggregatePayment.count)} />
        <DLItem
          label="Average Payment"
          value={formatCurrency(data.aggregatePayment.sum / (data.aggregatePayment.count || 1))}
        />
        {isProgramsReferralEnabled && (
          <DLItem
            label="Referred Applicants"
            value={formatNumber(data.applicantsReferred?.count)}
          />
        )}
      </DL>
    </div>
  );
}
