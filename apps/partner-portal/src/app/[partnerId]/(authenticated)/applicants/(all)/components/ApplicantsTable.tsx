'use client';

import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import Link, { type CustomLinkProps } from '@/spa-legacy/common/components/Link';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { formatCurrency, formatIfPhone } from '@/spa-legacy/common/utilities/format';
import { useQuery } from '@apollo/client';
import {
  type OffsetPagination,
  type PageInfo,
  type Sort,
  SortDirection,
  type User,
  type UserFilter,
  UserSortColumn,
} from '@bybeam/platform-types';
import {
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  Cross2Icon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
} from '@radix-ui/react-icons';
import {
  Box,
  Button,
  Flex,
  IconButton,
  Select,
  Strong,
  Table,
  Text,
  TextField,
  Tooltip,
} from '@radix-ui/themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import GetApplicantsQuery from '../graphql/GetApplicantsQuery.graphql';
import ApplicantFilterMenu from './ApplicantFilterMenu';
import styles from './ApplicantsTable.module.css';

export default function ApplicantsTable() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const posthog = usePostHog();
  const isDuplicateDetectionEnabled =
    posthog?.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.duplicateDetectionUI) ?? false;

  // Search input state with debounced URL update
  const searchFromUrl = searchParams.get('search') || '';
  const [searchInput, setSearchInput] = useState(searchFromUrl);
  const debounceTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // URL parameter validation helpers
  const parseNonNegativeInt = (value: string | null, fallback: number): number => {
    const n = Number(value);
    return Number.isFinite(n) && n >= 0 ? n : fallback;
  };

  const allowedPageSizes = [10, 25, 50, 100] as const;

  // Read pagination and sort state from URL with validation
  const page = parseNonNegativeInt(searchParams.get('page'), 0);
  const rawPageSize = parseNonNegativeInt(searchParams.get('pageSize'), 25);
  const pageSize = (allowedPageSizes as readonly number[]).includes(rawPageSize) ? rawPageSize : 25;

  const sortByParam = searchParams.get('sortBy');
  const sortBy = sortByParam === 'totalPaid' ? 'totalPaid' : 'name';
  const sortDirParam = searchParams.get('sortDir');
  const sortDir = sortDirParam === 'desc' ? 'desc' : 'asc';

  // URL parameter helper to reduce duplication
  const updateAndPushParams = useCallback(
    (updates: Record<string, string>) => {
      const params = new URLSearchParams(searchParams);
      for (const [k, v] of Object.entries(updates)) {
        params.set(k, v);
      }
      router.push(`${pathname}?${params.toString()}` as Parameters<typeof router.push>[0]);
    },
    [searchParams, pathname, router],
  );

  // Sync search input with URL changes (e.g., browser back/forward)
  useEffect(() => {
    setSearchInput(searchFromUrl);
  }, [searchFromUrl]);

  // Debounced search update
  const updateSearch = useCallback(
    (value: string) => {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set new timeout
      debounceTimeoutRef.current = setTimeout(() => {
        if (value.trim()) {
          updateAndPushParams({ search: value.trim(), page: '0' });
          posthog.capture('applicant:search_submitted');
        } else {
          const params = new URLSearchParams(searchParams);
          params.delete('search');
          params.set('page', '0');
          router.push(`${pathname}?${params.toString()}` as Parameters<typeof router.push>[0]);
          posthog.capture('applicant:search_cleared');
        }
      }, 300);
    },
    [searchParams, pathname, router, posthog, updateAndPushParams],
  );

  // Handle search input changes
  useEffect(() => {
    if (searchInput !== searchFromUrl) {
      updateSearch(searchInput);
    }
  }, [searchInput, searchFromUrl, updateSearch]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Read filters directly from URL params for the GraphQL query
  const filters = useMemo<UserFilter>(() => {
    const parseBoolean = (value: string | null) =>
      value === 'true' ? true : value === 'false' ? false : undefined;

    const baseFilters = {
      search: searchFromUrl || undefined,
      hasPhone: parseBoolean(searchParams.get('hasPhone')),
      hasMailingAddress: parseBoolean(searchParams.get('hasMailingAddress')),
    };

    // Only include hasPotentialDuplicates filter if feature flag is enabled
    if (isDuplicateDetectionEnabled) {
      return {
        ...baseFilters,
        hasPotentialDuplicates: parseBoolean(searchParams.get('hasPotentialDuplicates')),
      };
    }

    return baseFilters;
  }, [searchParams, searchFromUrl, isDuplicateDetectionEnabled]);

  // Reset page when filters change
  useEffect(() => {
    const shouldResetPage =
      page !== 0 &&
      (filters.hasPhone !== undefined ||
        filters.hasMailingAddress !== undefined ||
        (isDuplicateDetectionEnabled && filters.hasPotentialDuplicates !== undefined));

    if (shouldResetPage) {
      updateAndPushParams({ page: '0' });
    }
  }, [
    filters.hasPhone,
    filters.hasMailingAddress,
    filters.hasPotentialDuplicates,
    page,
    updateAndPushParams,
    isDuplicateDetectionEnabled,
  ]);

  const { data, loading, error } = useQuery<
    { users: { pageInfo: PageInfo; users: User[] } },
    {
      filter: UserFilter;
      pagination: OffsetPagination;
      sort?: Sort<UserSortColumn>;
      includeDuplicates: boolean;
    }
  >(GetApplicantsQuery, {
    variables: {
      filter: filters,
      pagination: { page, take: pageSize },
      includeDuplicates: isDuplicateDetectionEnabled,
      ...(sortBy && {
        sort: {
          column: sortBy === 'name' ? UserSortColumn.Name : UserSortColumn.TotalPaid,
          direction: sortDir === 'desc' ? SortDirection.Descending : SortDirection.Ascending,
        },
      }),
    },
    fetchPolicy: 'cache-and-network',
  });

  const { showSnackbar } = useSnackbar();
  useEffect(() => {
    if (error) showSnackbar('Uh oh! An error occurred! Please refresh and try again.');
  }, [error, showSnackbar]);

  // Use GraphQL data directly - no intermediate state
  const users = data?.users.users ?? [];
  const totalCount = data?.users.pageInfo.count ?? 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Handle sort toggle
  const handleSort = (column: 'name' | 'totalPaid') => {
    const newSortDir = sortBy === column && sortDir === 'asc' ? 'desc' : 'asc';
    updateAndPushParams({ sortBy: column, sortDir: newSortDir });
  };

  // Reusable sort button component
  const SortButton = ({ column, label }: { column: 'name' | 'totalPaid'; label: string }) => {
    const isActive = sortBy === column;
    const sortState = isActive ? (sortDir === 'asc' ? 'ascending' : 'descending') : 'none';

    return (
      <Button
        variant="ghost"
        onClick={() => handleSort(column)}
        className={styles.sortButton}
        color="gray"
        highContrast
        aria-sort={sortState}
        aria-label={`Sort by ${label} ${isActive ? (sortDir === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
      >
        <Flex align="center" gap="1">
          <Strong>{label}</Strong>
          {isActive &&
            (sortDir === 'asc' ? (
              <ChevronUpIcon aria-hidden="true" />
            ) : (
              <ChevronDownIcon aria-hidden="true" />
            ))}
        </Flex>
      </Button>
    );
  };

  return (
    <Flex direction="column" className={styles.container} px="2">
      <Box pt="5">
        <Flex gap="3" align="center" mb="2">
          <TextField.Root
            placeholder="Search applicants..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            size="2"
          >
            <TextField.Slot>
              <MagnifyingGlassIcon height="16" width="16" />
            </TextField.Slot>
            {searchInput && (
              <TextField.Slot>
                <IconButton
                  size="1"
                  variant="ghost"
                  color="gray"
                  onClick={() => setSearchInput('')}
                  aria-label="Clear search"
                >
                  <Cross2Icon height="14" width="14" />
                </IconButton>
              </TextField.Slot>
            )}
          </TextField.Root>
          <ApplicantFilterMenu loading={loading} />
        </Flex>
        <Flex gap="3" align="center" justify="between" mb="2">
          <Text weight="medium" ml="2">
            {totalCount} {totalCount === 1 ? 'result' : 'results'}
          </Text>
          <Flex gap="3" align="center" asChild>
            <nav aria-label="Table pagination">
              <Flex gap="2" align="center">
                <Text size="2" id="page-size-label">
                  Page size:
                </Text>
                <Select.Root
                  value={String(pageSize)}
                  onValueChange={(value) => updateAndPushParams({ pageSize: value, page: '0' })}
                  aria-labelledby="page-size-label"
                >
                  <Select.Trigger />
                  <Select.Content>
                    {[10, 25, 50, 100].map((size) => (
                      <Select.Item key={size} value={String(size)}>
                        {size}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Root>
              </Flex>
              <Flex gap="2" align="center">
                <IconButton
                  disabled={page === 0}
                  onClick={() => updateAndPushParams({ page: String(page - 1) })}
                  variant="soft"
                  radius="full"
                  aria-label="Go to previous page"
                >
                  <ChevronLeftIcon />
                </IconButton>
                <Text size="2" aria-live="polite" aria-atomic="true">
                  {totalCount > 0
                    ? `${page * pageSize + 1}-${Math.min((page + 1) * pageSize, totalCount)} of ${totalCount}`
                    : '0 applicants'}
                </Text>
                <IconButton
                  disabled={page >= totalPages - 1 || totalPages === 0}
                  onClick={() => updateAndPushParams({ page: String(page + 1) })}
                  variant="soft"
                  radius="full"
                  aria-label="Go to next page"
                >
                  <ChevronRightIcon />
                </IconButton>
              </Flex>
            </nav>
          </Flex>
        </Flex>
      </Box>
      <Box className={styles.tableWrapper} minHeight="0" mb="2">
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell minWidth="12rem">
                <SortButton column="name" label="Name" />
              </Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell minWidth="7rem">Applicant ID</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell minWidth="12rem">Mailing Address</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Email</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell minWidth="8rem">Phone</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell minWidth="6rem">
                <SortButton column="totalPaid" label="Total Paid" />
              </Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {loading && users.length === 0 ? (
              <Table.Row>
                <Table.Cell colSpan={6}>
                  <div className={styles.emptyState}>
                    <LoadingComponent />
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : users.length === 0 ? (
              <Table.Row>
                <Table.Cell colSpan={6}>
                  <div className={styles.emptyState}>No applicants found</div>
                </Table.Cell>
              </Table.Row>
            ) : (
              users.map((user) => (
                <Table.Row key={user.id}>
                  <Table.Cell>
                    <Flex align="center" gap="2">
                      {isDuplicateDetectionEnabled && user.hasPotentialDuplicates && (
                        <Tooltip content="Potential duplicate account">
                          <ExclamationTriangleIcon color="orange" />
                        </Tooltip>
                      )}
                      <Link
                        to={
                          makeRoute(PORTAL_ROUTES.APPLICANT_PROFILE, {
                            userId: user.id,
                          }) as CustomLinkProps['to']
                        }
                      >
                        {user.name}
                      </Link>
                    </Flex>
                  </Table.Cell>
                  <Table.Cell>{user.displayId}</Table.Cell>
                  <Table.Cell>
                    {user.applicantProfile?.mailingAddress ? (
                      <Flex direction="column">
                        <Text size="2">
                          {user.applicantProfile.mailingAddress.addressLine1}{' '}
                          {user.applicantProfile.mailingAddress.addressLine2}
                        </Text>
                        <Text size="2">
                          {user.applicantProfile.mailingAddress.city},{' '}
                          {user.applicantProfile.mailingAddress.state}{' '}
                          {user.applicantProfile.mailingAddress.zip}
                        </Text>
                      </Flex>
                    ) : (
                      '-'
                    )}
                  </Table.Cell>
                  <Table.Cell>{user.email}</Table.Cell>
                  <Table.Cell>{formatIfPhone(user.phone)}</Table.Cell>
                  <Table.Cell>{formatCurrency(user.aggregatePayments?.sum ?? 0)}</Table.Cell>
                </Table.Row>
              ))
            )}
          </Table.Body>
        </Table.Root>
      </Box>
    </Flex>
  );
}
