import { SortDirection, UserSortColumn } from '@bybeam/platform-types';
import type { Meta, StoryObj } from '@storybook/nextjs';
import { expect } from 'storybook/test';
import GetApplicantsQuery from '../graphql/GetApplicantsQuery.graphql';
import ApplicantsTable from './ApplicantsTable';
import {
  APPLICANTS_LOADING_MOCK,
  APPLICANTS_PAGINATED_MOCK,
  APPLICANTS_SORTED_MOCK,
  APPLICANTS_WITH_DUPLICATES_MOCK,
  APPLICANTS_WITH_SEARCH_MOCK,
  EMPTY_APPLICANTS_MOCK,
  GET_APPLICANTS_MOCK,
} from './ApplicantsTable.mock';

const meta: Meta<typeof ApplicantsTable> = {
  component: ApplicantsTable,
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
      navigation: {
        segments: [['partnerId', 'mock-partner-id']],
      },
    },
    postHogClient: {
      capture: () => {},
      isFeatureEnabled: () => true,
      onFeatureFlags: (cb: () => void) => {
        try {
          cb();
        } catch {}
        return () => {};
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ApplicantsTable>;

export const Default: Story = {
  parameters: {
    apolloClient: {
      mocks: [GET_APPLICANTS_MOCK],
    },
    nextjs: {
      navigation: {
        query: { pageSize: '10' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Wait for the table to load
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Basic smoke test - just verify the table renders with data
    const rows = canvasElement.querySelectorAll('tbody tr');
    expect(rows.length).toBeGreaterThan(0);
    expect(canvasElement.textContent).toContain('Alice Johnson');
  },
};

export const Empty: Story = {
  parameters: {
    apolloClient: {
      mocks: [EMPTY_APPLICANTS_MOCK],
    },
    nextjs: {
      navigation: {
        query: { pageSize: '10' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Wait for the table to load
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Smoke test - verify empty state
    const rows = canvasElement.querySelectorAll('tbody tr');
    expect(rows).toHaveLength(1);
    expect(rows[0]).toHaveTextContent('No applicants found');
  },
};

export const Loading: Story = {
  parameters: {
    apolloClient: {
      mocks: [APPLICANTS_LOADING_MOCK],
    },
    nextjs: {
      navigation: {
        query: { pageSize: '10' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Smoke test - verify loading state
    const rows = canvasElement.querySelectorAll('tbody tr');
    expect(rows).toHaveLength(1);
    expect(rows[0]).toHaveTextContent('Loading...');
  },
};

export const WithSearch: Story = {
  parameters: {
    apolloClient: {
      mocks: [APPLICANTS_WITH_SEARCH_MOCK],
    },
    nextjs: {
      navigation: {
        query: { search: 'alice', pageSize: '10' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Wait for the table to load
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Smoke test - verify search works
    expect(canvasElement.textContent).toContain('Alice Johnson');
  },
};

export const Paginated: Story = {
  parameters: {
    apolloClient: {
      mocks: [APPLICANTS_PAGINATED_MOCK],
    },
    nextjs: {
      navigation: {
        query: { page: '1', pageSize: '10' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Wait for the table to load
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Smoke test - verify pagination works
    expect(canvasElement.textContent).toContain('Frank Wilson');
  },
};

export const Sorted: Story = {
  parameters: {
    apolloClient: {
      mocks: [APPLICANTS_SORTED_MOCK],
    },
    nextjs: {
      navigation: {
        query: { sortBy: 'totalPaid', sortDir: 'desc', pageSize: '10' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Wait for the table to load
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Smoke test - verify sorting works (James Taylor should be first)
    expect(canvasElement.textContent).toContain('James Taylor');
  },
};

export const WithPageSize50: Story = {
  parameters: {
    apolloClient: {
      mocks: [
        {
          request: {
            query: GetApplicantsQuery,
            variables: {
              filter: {},
              pagination: { page: 0, take: 50 },
              sort: {
                column: UserSortColumn.Name,
                direction: SortDirection.Ascending,
              },
              includeDuplicates: true,
            },
          },
          result: GET_APPLICANTS_MOCK.result,
        },
      ],
    },
    nextjs: {
      navigation: {
        query: { pageSize: '50' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Wait for the table to load
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Smoke test - verify page size works
    const rows = canvasElement.querySelectorAll('tbody tr');
    expect(rows.length).toBeGreaterThan(0);
  },
};

export const WithDuplicateDetection: Story = {
  parameters: {
    apolloClient: {
      mocks: [APPLICANTS_WITH_DUPLICATES_MOCK],
    },
    nextjs: {
      navigation: {
        query: { pageSize: '10' },
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Wait for the table to load
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Smoke test - verify duplicate detection feature works
    expect(canvasElement.textContent).toContain('Alice Johnson');
    expect(canvasElement.textContent).toContain('Bob Smith');
  },
};
