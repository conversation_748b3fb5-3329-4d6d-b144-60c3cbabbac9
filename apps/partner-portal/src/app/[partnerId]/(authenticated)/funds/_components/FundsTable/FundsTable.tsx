import { makeRoute } from '@/app/Routes';
import { PORTAL_ROUTES } from '@/app/Routes';
import CanAccess from '@/app/components/features/CanAccess';
import { formatCurrency } from '@/spa-legacy/common/utilities/format';
import { QueryResult } from '@apollo/client';
import { Fund, Page } from '@bybeam/platform-types';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Badge, Flex, HoverCard, Link as RadixLink, Table, Text, Tooltip } from '@radix-ui/themes';
import Link from 'next/link';
import { FundBalanceDefinitions } from '../../_utils/fundStats';
import FundStatusIcon from '../FundStatusIcon';
import RemoveFundDialog from '../RemoveFundDialog/RemoveFundDialog';
import styles from './FundsTable.module.css';

export default function FundsTable({ error, data }: QueryResult<{ funds: Page<Fund> }>) {
  const { nodes: funds } = data?.funds ?? {};

  if (error) return <Text color="red">Something went wrong!</Text>;
  return (
    <Table.Root variant="surface">
      <Table.Header>
        <Table.Row>
          <Table.ColumnHeaderCell>Fund name</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Funding source</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>Programs</Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>
            <Flex gap="1" align="center">
              {FundBalanceDefinitions.startingBalance.label}
              <Tooltip content={FundBalanceDefinitions.startingBalance.description}>
                <InfoCircledIcon />
              </Tooltip>
            </Flex>
          </Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>
            <Flex gap="1" align="center">
              {FundBalanceDefinitions.obligatedBalance.label}
              <Tooltip content={FundBalanceDefinitions.obligatedBalance.description}>
                <InfoCircledIcon />
              </Tooltip>
            </Flex>
          </Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>
            <Flex gap="1" align="center">
              {FundBalanceDefinitions.awardedBalance.label}
              <Tooltip content={FundBalanceDefinitions.awardedBalance.description}>
                <InfoCircledIcon />
              </Tooltip>
            </Flex>
          </Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell>
            <Flex gap="1" align="center">
              {FundBalanceDefinitions.remainingBalance.label}
              <Tooltip content={FundBalanceDefinitions.remainingBalance.description}>
                <InfoCircledIcon />
              </Tooltip>
            </Flex>
          </Table.ColumnHeaderCell>
          <Table.ColumnHeaderCell />
        </Table.Row>
      </Table.Header>
      <Table.Body>
        {funds?.map((fund) => (
          <Table.Row key={fund.id}>
            <Table.RowHeaderCell>
              <RadixLink asChild>
                <Link
                  href={{
                    pathname: makeRoute(PORTAL_ROUTES.FUND_DETAIL, { fundId: fund.id }),
                  }}
                >
                  <Text weight="bold">{fund.name}</Text>
                </Link>
              </RadixLink>
            </Table.RowHeaderCell>
            <Table.Cell>
              <FundStatusIcon fund={fund} />
            </Table.Cell>
            <Table.RowHeaderCell>
              <Flex gap="1">
                {!!fund.config?.programId && <Badge color="blue">JPMC</Badge>}
                {!!fund.config?.usioKey && <Badge color="purple">USIO</Badge>}
              </Flex>
            </Table.RowHeaderCell>
            <Table.Cell>
              {fund.programs?.length ? (
                <HoverCard.Root>
                  <HoverCard.Trigger>
                    <Flex gap="1" overflow="hidden" maxWidth="15rem">
                      {fund.programs.map((program) => (
                        <div key={program.id} className={styles.badgeContainer}>
                          <Badge key={program.id} className={styles.badge}>
                            <Text truncate>{program.name}</Text>
                          </Badge>
                        </div>
                      ))}
                    </Flex>
                  </HoverCard.Trigger>
                  <HoverCard.Content size="1" sideOffset={4} alignOffset={4}>
                    <Flex gap="1" maxWidth="15rem" wrap="wrap">
                      {fund.programs.map((program) => (
                        <Badge key={program.id} className={styles.badge}>
                          <Text wrap="balance">{program.name}</Text>
                        </Badge>
                      ))}
                    </Flex>
                  </HoverCard.Content>
                </HoverCard.Root>
              ) : (
                <></>
              )}
            </Table.Cell>
            <Table.Cell>{formatCurrency(fund.startingBalance, true)}</Table.Cell>
            <Table.Cell>{formatCurrency(fund.stats?.obligatedBalance ?? 0, true)}</Table.Cell>
            <Table.Cell>{formatCurrency(fund.stats?.awardedBalance ?? 0, true)}</Table.Cell>
            <Table.Cell>{formatCurrency(fund.stats?.remainingBalance ?? 0, true)}</Table.Cell>
            <Table.Cell>
              <CanAccess>
                <RemoveFundDialog fund={fund} />
              </CanAccess>
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table.Root>
  );
}
