'use client';
import Page from '@/app/components/ui/Page/Page';
import { useUserHomeRoute } from '@/app/hooks/useUserHomeRoute';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { Text } from '@radix-ui/themes';
import { Route } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { useEffect } from 'react';
import { FundDetails } from './_components';

export default function FundViewPage() {
  const postHog = usePostHog();
  const defaultRoute = useUserHomeRoute();
  const router = useRouter();
  const fundsEnabled = postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.fundsPage);

  // Perform navigation in an effect to avoid side effects during render
  useEffect(() => {
    if (!fundsEnabled) {
      router.replace(defaultRoute as Route);
    }
  }, [fundsEnabled, defaultRoute, router]);

  if (!fundsEnabled)
    return (
      <Page>
        <Page.Content>
          <Text role="status" aria-live="polite">
            Redirecting…
          </Text>
        </Page.Content>
      </Page>
    );

  return (
    <Page>
      <Page.Header>
        <Page.Breadcrumbs>
          <Page.Breadcrumbs.Crumb>
            <Link href={{ pathname: '.' }}>Funds</Link>
          </Page.Breadcrumbs.Crumb>
          <Page.Breadcrumbs.Crumb>Fund Details</Page.Breadcrumbs.Crumb>
        </Page.Breadcrumbs>
      </Page.Header>
      <Page.Content>
        <FundDetails />
      </Page.Content>
    </Page>
  );
}
