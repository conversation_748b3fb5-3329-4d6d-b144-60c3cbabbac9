'use client';
import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import CanAccess from '@/app/components/features/CanAccess';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import { formatCurrency } from '@/spa-legacy/common/utilities/format';
import { useQuery } from '@apollo/client';
import { Fund } from '@bybeam/platform-types';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import {
  Badge,
  Box,
  Card,
  DataList,
  Flex,
  Heading,
  Link as RadixLink,
  Table,
  Text,
  Tooltip,
} from '@radix-ui/themes';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import FundStatusIcon from '../../../_components/FundStatusIcon';
import { FundBalanceDefinitions } from '../../../_utils/fundStats';
import UpdateFundDialog from '../UpdateFund/UpdateFundDialog';
import GetFundQuery from './GetFundQuery.graphql';

export default function FundDetails(): JSX.Element {
  const { fundId } = useParams<{ fundId: string }>();

  const { data, loading, error } = useQuery<{ funds: { nodes: Fund[] } }>(GetFundQuery, {
    variables: { fundId },
  });

  const fund = data?.funds?.nodes?.[0];

  if (loading) return <LoadingComponent />;
  if (error) return <Text color="red">Something went wrong! Please refresh and try again.</Text>;
  if (!fund) return <Text color="red">Fund details can not be found.</Text>;

  return (
    <Flex py="4" px="5" gap="4" direction="column">
      <Flex justify="between">
        <Heading as="h1">{fund.name}</Heading>
        <CanAccess>
          <UpdateFundDialog fund={fund} />
        </CanAccess>
      </Flex>
      <Card size="2">
        <Heading size="4" as="h2" mb="2">
          Fund overview
        </Heading>
        <DataList.Root orientation="vertical">
          <DataList.Item>
            <DataList.Label>
              <Flex gap="1" align="center">
                {FundBalanceDefinitions.startingBalance.label}
                <Tooltip content={FundBalanceDefinitions.startingBalance.description}>
                  <InfoCircledIcon />
                </Tooltip>
              </Flex>
            </DataList.Label>
            <DataList.Value>{formatCurrency(fund.startingBalance ?? 0, true)}</DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>
              <Flex gap="1" align="center">
                Maximum individual award amount
                <Tooltip content="Maximum allowed per individual disbursement from this fund.">
                  <InfoCircledIcon />
                </Tooltip>
              </Flex>
            </DataList.Label>
            <DataList.Value>
              {fund.awardAmountMax != null ? formatCurrency(fund.awardAmountMax, true) : 'No limit'}
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>
              <Flex gap="1" align="center">
                {FundBalanceDefinitions.obligatedBalance.label}
                <Tooltip content={FundBalanceDefinitions.obligatedBalance.description}>
                  <InfoCircledIcon />
                </Tooltip>
              </Flex>
            </DataList.Label>
            <DataList.Value>
              {formatCurrency(fund.stats?.obligatedBalance ?? 0, true)}
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>
              <Flex gap="1" align="center">
                {FundBalanceDefinitions.awardedBalance.label}
                <Tooltip content={FundBalanceDefinitions.awardedBalance.description}>
                  <InfoCircledIcon />
                </Tooltip>
              </Flex>
            </DataList.Label>
            <DataList.Value>{formatCurrency(fund.stats?.awardedBalance ?? 0, true)}</DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>
              <Flex gap="1" align="center">
                {FundBalanceDefinitions.remainingBalance.label}
                <Tooltip content={FundBalanceDefinitions.remainingBalance.description}>
                  <InfoCircledIcon />
                </Tooltip>
              </Flex>
            </DataList.Label>
            <DataList.Value>
              {formatCurrency(fund.stats?.remainingBalance ?? 0, true)}
            </DataList.Value>
          </DataList.Item>
        </DataList.Root>
      </Card>
      <Card size="2">
        <Flex justify="start" align="center" gap="2" mb="2">
          <Heading size="4" as="h2">
            Configuration
          </Heading>
          <FundStatusIcon fund={fund} />
        </Flex>
        <DataList.Root orientation="vertical">
          <DataList.Item>
            <DataList.Label>Funding source</DataList.Label>
            <DataList.Value>
              {fund.config ? (
                <Flex gap="1">
                  {!!fund.config?.programId && <Badge color="blue">JPMC</Badge>}
                  {!!fund.config?.usioKey && <Badge color="purple">USIO</Badge>}
                </Flex>
              ) : (
                '-'
              )}
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>JPMC program ID</DataList.Label>
            <DataList.Value>{fund.config?.programId ?? '-'}</DataList.Value>
          </DataList.Item>
          {fund.config?.includeCheckMemo != null && (
            <DataList.Item>
              <DataList.Label>JPMC check memo enabled</DataList.Label>
              <DataList.Value>{fund.config.includeCheckMemo ? 'Yes' : 'No'}</DataList.Value>
            </DataList.Item>
          )}
          {!!fund.config?.usioKey && (
            <>
              <DataList.Item>
                <DataList.Label>USIO key</DataList.Label>
                <DataList.Value>
                  <Tooltip content={fund.config.usioKey}>
                    <Box maxWidth="16rem" asChild>
                      <Text truncate>{fund.config?.usioKey ?? '-'}</Text>
                    </Box>
                  </Tooltip>
                </DataList.Value>
              </DataList.Item>
              <DataList.Item>
                <DataList.Label>USIO virtual distributer ID</DataList.Label>
                <DataList.Value>{fund.config.virtualDistributorId ?? '-'}</DataList.Value>
              </DataList.Item>
              <DataList.Item>
                <DataList.Label>USIO physical distributer ID</DataList.Label>
                <DataList.Value>{fund.config.physicalDistributorId ?? '-'}</DataList.Value>
              </DataList.Item>
              {!!fund.config.usioDesignId && (
                <DataList.Item>
                  <DataList.Label>USIO design ID</DataList.Label>
                  <DataList.Value>{fund.config.usioDesignId}</DataList.Value>
                </DataList.Item>
              )}
              <DataList.Item>
                <DataList.Label>Skip ACH transfer</DataList.Label>
                <DataList.Value>{fund.config?.skipACHTransfer ? 'Yes' : 'No'}</DataList.Value>
              </DataList.Item>
            </>
          )}
        </DataList.Root>
      </Card>

      <Heading size="4" as="h2">
        Programs
      </Heading>
      {fund.programs?.length ? (
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Program name</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Awarded funds</Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {fund.programs.map((program) => (
              <Table.Row key={program.name}>
                <Table.RowHeaderCell>
                  <RadixLink>
                    <Link
                      href={{
                        pathname: makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, {
                          programId: program.id,
                        }),
                      }}
                    >
                      <Text weight="bold">{program.name}</Text>
                    </Link>
                  </RadixLink>
                </Table.RowHeaderCell>
                <Table.Cell>
                  {formatCurrency(
                    program.stats?.programFundStats?.find(({ fundId }) => fundId === fund.id)
                      ?.awardedBalance ?? 0,
                    true,
                  )}
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      ) : (
        <Text>Fund does not belong to any programs.</Text>
      )}
    </Flex>
  );
}
