'use client';
import { checkProgramHasAnyFeature } from '@/app/_utils/checkFeature';
import CanAccess from '@/app/components/features/CanAccess';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import Page from '@/app/components/ui/Page/Page';
import { useUserHomeRoute } from '@/app/hooks/useUserHomeRoute';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useCustomPageTitle from '@/spa-legacy/common/hooks/useCustomPageTitle';
import { useQuery } from '@apollo/client';
import { FeatureName, Fund, Page as PageType, SortDirection } from '@bybeam/platform-types';
import { Container, Flex, Text } from '@radix-ui/themes';
import { Route } from 'next';
import { useRouter } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { Suspense, useEffect, useMemo } from 'react';
import { CreateFundDialog, FundsTable } from './_components';
import GetPartnerFundsQuery from './page.graphql';

export default function FundsPage() {
  useCustomPageTitle('Funds');
  const defaultRoute = useUserHomeRoute();

  const postHog = usePostHog();
  const isFundPageEnabled = useMemo(
    () => postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.fundsPage),
    [postHog],
  );
  const router = useRouter();

  const { programs = [] } = usePartner();
  const hasProgramsWithPayment = programs?.some((program) =>
    checkProgramHasAnyFeature(program, [
      FeatureName.PaymentsPartnerIssued,
      FeatureName.PaymentsExternalTracking,
      FeatureName.PaymentsClaimFunds,
    ]),
  );

  const fundsQuery = useQuery<{ funds: PageType<Fund> }>(GetPartnerFundsQuery, {
    variables: {
      sort: {
        column: 'Name',
        direction: SortDirection.Ascending,
      },
    },
    skip: !isFundPageEnabled || !hasProgramsWithPayment,
  });

  // Redirect via effect to avoid side effects during render
  useEffect(() => {
    if (!isFundPageEnabled) {
      router.replace(defaultRoute as Route);
    }
  }, [isFundPageEnabled, defaultRoute, router]);

  if (!isFundPageEnabled)
    return (
      <Page>
        <Page.Content>
          <Text role="status" aria-live="polite">
            Redirecting…
          </Text>
        </Page.Content>
      </Page>
    );
  if (fundsQuery.loading) return <LoadingComponent />;

  return (
    <Page>
      <Page.Header>
        <Page.Breadcrumbs>
          <Page.Breadcrumbs.Crumb>Funds</Page.Breadcrumbs.Crumb>
        </Page.Breadcrumbs>
      </Page.Header>
      <Page.Content>
        <Container px="4" py="2" align="left" maxWidth="100%">
          {hasProgramsWithPayment ? (
            <Suspense fallback={<LoadingComponent />}>
              <FundsTable {...fundsQuery} />
            </Suspense>
          ) : (
            <Text>There are no funds configured for your organization.</Text>
          )}
          <Flex justify="start" mt="2">
            <CanAccess>
              <CreateFundDialog />
            </CanAccess>
          </Flex>
        </Container>
      </Page.Content>
    </Page>
  );
}
