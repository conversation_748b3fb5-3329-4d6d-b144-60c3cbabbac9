'use client';
import ProgramListTable from '@/app/[partnerId]/(authenticated)/settings/components/ProgramListTable';
import { BEAM_SUPPORT_EMAIL } from '@/app/_utils/constants';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import PartnerTagManager from '@/spa-legacy/portal/pages/Settings/components/PartnerTagManager';
import { EnvelopeClosedIcon } from '@radix-ui/react-icons';
import { Button, Flex, Heading, Text } from '@radix-ui/themes';
import { useFeatureFlagEnabled } from 'posthog-js/react';
import TagAutomationsManager from './components/TagAutomations/TagAutomationsManager';

export default function SettingsPage() {
  // Use a reactive PostHog hook so the UI re-renders when flags finish loading
  const isTagAutomationsEnabled = useFeatureFlagEnabled(POSTHOG_FEATURE_FLAGS.tagAutomations);
  return (
    <Flex direction="column" gap="6">
      <ProgramListTable />
      <PartnerTagManager />
      {isTagAutomationsEnabled ? <TagAutomationsManager /> : null}
      <Flex direction="column" gap="1" mt="4">
        <Heading as="h2">Contact us</Heading>
        <Flex direction="column" maxWidth="24rem" gap="2">
          <Text>
            Technical problem that needs our attention? Send us a message and we'll take a look.
          </Text>
          <Button asChild>
            <a href={`mailto:${BEAM_SUPPORT_EMAIL}`}>
              <EnvelopeClosedIcon /> Email us
            </a>
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
}
