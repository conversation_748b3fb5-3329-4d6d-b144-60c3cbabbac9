'use client';
import { makeRoute } from '@/app/Routes';
import { PORTAL_ROUTES } from '@/app/Routes';
import { FundBalanceDefinitions } from '@/app/[partnerId]/(authenticated)/funds/_utils/fundStats';
import CanAccess from '@/app/components/features/CanAccess';
import Loading from '@/app/components/ui/Loading/LoadingComponent';
import { formatCurrency } from '@/spa-legacy/common/utilities/format';
import { useQuery } from '@apollo/client';
import { Program } from '@bybeam/platform-types';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Flex, Heading, Link as RadixLink, Strong, Table, Text, Tooltip } from '@radix-ui/themes';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { AddProgramFundDialog, RemoveProgramFundDialog } from './_components';
import GetProgramFundsQuery from './page.graphql';

export default function ProgramFunds() {
  const { programId } = useParams<{ programId: string }>();

  const { data, loading, error } = useQuery<
    { programs: { programs: Program[] } },
    { programId: string }
  >(GetProgramFundsQuery, { variables: { programId } });

  const program = data?.programs?.programs?.[0];
  const funds = program?.funds ?? [];

  if (loading) return <Loading />;
  if (error) return <Text color="red">Something went wrong!</Text>;

  return (
    <Flex direction="column" px="4" py="2" gap="2" width="fit-content">
      <Heading size="4">Funds</Heading>
      {program && funds?.length ? (
        <>
          <Text>
            The following funds are currently enabled for <Strong>{program?.name}</Strong> program.
          </Text>
          <Table.Root variant="surface">
            <Table.Header>
              <Table.Row>
                <Table.ColumnHeaderCell>Fund</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>
                  <Flex gap="1" align="center">
                    {FundBalanceDefinitions.startingBalance.label}
                    <Tooltip content={FundBalanceDefinitions.startingBalance.description}>
                      <InfoCircledIcon />
                    </Tooltip>
                  </Flex>
                </Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>
                  <Flex gap="1" align="center">
                    {FundBalanceDefinitions.obligatedBalance.label}
                    <Tooltip content={FundBalanceDefinitions.obligatedBalance.description}>
                      <InfoCircledIcon />
                    </Tooltip>
                  </Flex>
                </Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>
                  <Flex gap="1" align="center">
                    {FundBalanceDefinitions.awardedBalance.label}
                    <Tooltip content="The total sum that has been disbursed to recipients for the program.">
                      <InfoCircledIcon />
                    </Tooltip>
                  </Flex>
                </Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>
                  <Flex gap="1" align="center">
                    {FundBalanceDefinitions.remainingBalance.label}
                    <Tooltip content={FundBalanceDefinitions.remainingBalance.description}>
                      <InfoCircledIcon />
                    </Tooltip>
                  </Flex>
                </Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell />
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {funds.map((fund) => (
                <Table.Row key={fund.id}>
                  <Table.RowHeaderCell>
                    <RadixLink asChild>
                      <Link
                        href={{
                          pathname: makeRoute(PORTAL_ROUTES.FUND_DETAIL, { fundId: fund.id }),
                        }}
                      >
                        <Text weight="bold">{fund.name}</Text>
                      </Link>
                    </RadixLink>
                  </Table.RowHeaderCell>
                  <Table.Cell>{formatCurrency(fund.startingBalance, true)}</Table.Cell>
                  <Table.Cell>{formatCurrency(fund.stats?.obligatedBalance ?? 0, true)}</Table.Cell>
                  <Table.Cell>
                    {formatCurrency(
                      program?.stats?.programFundStats?.find(({ fundId }) => fundId === fund.id)
                        ?.awardedBalance ?? 0,
                      true,
                    )}
                  </Table.Cell>
                  <Table.Cell>{formatCurrency(fund.stats?.remainingBalance ?? 0, true)}</Table.Cell>
                  <Table.Cell>
                    <CanAccess>
                      <RemoveProgramFundDialog
                        programId={program.id}
                        programFundStats={program.stats?.programFundStats}
                        fund={fund}
                      />
                    </CanAccess>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table.Root>
        </>
      ) : (
        <Text>
          No funds have been set for <Strong>{program?.name}</Strong> program.
        </Text>
      )}
      <Flex justify="start">
        <CanAccess>
          <AddProgramFundDialog programId={program?.id as string} existingProgramFunds={funds} />
        </CanAccess>
      </Flex>
    </Flex>
  );
}
