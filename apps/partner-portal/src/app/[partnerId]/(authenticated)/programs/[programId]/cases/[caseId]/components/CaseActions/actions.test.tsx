import { mockCase, mockProgram } from '@/tests/mocks/mockData';
import { CaseStatus, FeatureName } from '@bybeam/platform-types';
import { canChangeStatus } from './Actions';

describe('canChangeStatus', () => {
  describe('Program has PaymentsPartnerIssued enabled', () => {
    test.each([
      CaseStatus.InProgress,
      CaseStatus.Incomplete,
      CaseStatus.ReadyForReview,
      CaseStatus.InReview,
      CaseStatus.Denied,
      CaseStatus.Archived,
      CaseStatus.Withdrawn,
    ])('can change status if case status is %s', (status) => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsPartnerIssued]),
          mockCase('mockCaseId', 'mockName', status),
        ),
      ).toBeTruthy();
    });
    it('cannot change status if case status is Approved', () => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsPartnerIssued]),
          mockCase('mockCaseId', 'mockName', CaseStatus.Approved),
        ),
      ).toBeFalsy();
    });
    it('cannot change status if case status is PaymentSent', () => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsPartnerIssued]),
          mockCase('mockCaseId', 'mockName', CaseStatus.PaymentSent),
        ),
      ).toBeFalsy();
    });
  });
  describe('Program has PaymentsClaimFunds enabled', () => {
    test.each([
      CaseStatus.InProgress,
      CaseStatus.Incomplete,
      CaseStatus.ReadyForReview,
      CaseStatus.InReview,
      CaseStatus.Denied,
      CaseStatus.Archived,
      CaseStatus.Withdrawn,
    ])('can change status if case status is %s', async (status) => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsClaimFunds]),
          mockCase('mockCaseId', 'mockName', status),
        ),
      ).toBeTruthy();
    });
    it('cannot change status if case status is Approved', () => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsClaimFunds]),
          mockCase('mockCaseId', 'mockName', CaseStatus.Approved),
        ),
      ).toBeFalsy();
    });
    it('cannot change status if case status is PaymentSent', () => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsClaimFunds]),
          mockCase('mockCaseId', 'mockName', CaseStatus.PaymentSent),
        ),
      ).toBeFalsy();
    });
  });
  describe('Program has PaymentsExternalTracking enabled', () => {
    test.each([
      CaseStatus.InProgress,
      CaseStatus.Incomplete,
      CaseStatus.ReadyForReview,
      CaseStatus.InReview,
      CaseStatus.Denied,
      CaseStatus.Archived,
      CaseStatus.Withdrawn,
      CaseStatus.Approved,
    ])('can change status if case status is %s', (status) => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsExternalTracking]),
          mockCase('mockCaseId', 'mockName', status),
        ),
      ).toBeTruthy();
    });
    it('cannot change status if case status is PaymentSent', () => {
      expect(
        canChangeStatus(
          mockProgram([FeatureName.PaymentsExternalTracking]),
          mockCase('mockCaseId', 'mockName', CaseStatus.PaymentSent),
        ),
      ).toBeFalsy();
    });
  });
  describe('Program has no payments', () => {
    test.each([
      CaseStatus.InProgress,
      CaseStatus.Incomplete,
      CaseStatus.ReadyForReview,
      CaseStatus.InReview,
      CaseStatus.Denied,
      CaseStatus.Archived,
      CaseStatus.Withdrawn,
      CaseStatus.Approved,
      CaseStatus.PaymentSent,
    ])('can change status if case status is %s', (status) => {
      expect(
        canChangeStatus(mockProgram([]), mockCase('mockCaseId', 'mockName', status)),
      ).toBeTruthy();
    });
  });
});
