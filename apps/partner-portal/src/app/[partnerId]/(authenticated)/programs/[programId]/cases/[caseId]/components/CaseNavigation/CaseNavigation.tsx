'use client';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { hasMultiParty } from '@/spa-legacy/common/utilities/applicantType';
import { programHasPayments } from '@/spa-legacy/utilities/programs';
import { Box, Flex, TabNav } from '@radix-ui/themes';
import Link from 'next/link';
import { usePathname, useSelectedLayoutSegment } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { useMemo } from 'react';
import styles from './CaseNavigation.module.css';

const ROUTES = {
  caseOverview: (indexPath: string) => indexPath,
  caseApplication: (indexPath: string, applicationId: string) =>
    `${indexPath}/applications/${applicationId}`,
  casePayments: (indexPath: string) => `${indexPath}/payments`,
  caseParticipants: (indexPath: string) => `${indexPath}/participants`,
};

interface ICaseNavigation {
  applicationId?: string;
}

const CaseNavigation = ({ applicationId }: ICaseNavigation) => {
  const posthog = usePostHog();
  const pathname = usePathname();
  const selectedSegment = useSelectedLayoutSegment();

  const program = useProgram();
  const showPayments = program && programHasPayments(program);
  const showMultiparty = program && hasMultiParty(program);

  const indexPath = useMemo(() => {
    // Ensure the index path only contains information up to the currently selected segment
    const segmentIndex = pathname.indexOf(`/${selectedSegment}`);
    return segmentIndex !== -1 ? pathname.slice(0, segmentIndex) : pathname;
  }, [pathname, selectedSegment]);

  return (
    <Box className={styles.caseNavigation}>
      <Flex overflowX="auto" width="100%" asChild>
        <TabNav.Root>
          <TabNav.Link asChild active={pathname === ROUTES.caseOverview(indexPath)}>
            <Link href={{ pathname: ROUTES.caseOverview(indexPath) }}>Case Overview</Link>
          </TabNav.Link>
          {applicationId ? (
            <TabNav.Link
              asChild
              active={pathname === ROUTES.caseApplication(indexPath, applicationId)}
            >
              <Link href={{ pathname: ROUTES.caseApplication(indexPath, applicationId) }}>
                Application
              </Link>
            </TabNav.Link>
          ) : null}
          {showMultiparty && posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.participantsTab) ? (
            <TabNav.Link asChild active={pathname === ROUTES.caseParticipants(indexPath)}>
              <Link href={{ pathname: `${indexPath}/participants` }}>Participants</Link>
            </TabNav.Link>
          ) : null}
          {showPayments && posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.casePaymentsPage) ? (
            <TabNav.Link asChild active={pathname === ROUTES.casePayments(indexPath)}>
              <Link href={{ pathname: `${indexPath}/payments` }}>Payments</Link>
            </TabNav.Link>
          ) : null}
        </TabNav.Root>
      </Flex>
    </Box>
  );
};

export default CaseNavigation;
