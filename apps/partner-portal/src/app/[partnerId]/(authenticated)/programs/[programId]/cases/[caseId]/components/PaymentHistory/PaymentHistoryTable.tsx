import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import Link from '@/spa-legacy/common/components/Link';
import Typography from '@/spa-legacy/common/components/Typography';
import { DateFormat, formatCurrency, formatDate } from '@/spa-legacy/common/utilities/format';
import {
  PaymentStatusDisplay,
  getExternalPaymentStatusDisplay,
} from '@/spa-legacy/common/utilities/payment';
import { formatMonths } from '@/spa-legacy/portal/utils/months';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import {
  type Application,
  BenefitType,
  FeatureName,
  type Month,
  type Payment,
  type Program,
  type User,
  type Vendor,
} from '@bybeam/platform-types';
import cx from 'classnames';
import type { ReactNode } from 'react';

export interface PaymentHistoryRow {
  id: string;
  applicationId: {
    displayId: string;
    link: string;
  };
  checkDate: string;
  status: string;
  payee: {
    name: string;
    link?: string;
  };
  type: string;
  amount: string;
  months: Month[];
}

function isVendorPayee(payee: User | Vendor): payee is Vendor {
  return 'type' in payee;
}

const getPaymentRequestType = (
  { fulfillment }: Payment,
  benefit?: BenefitType,
): string | undefined => {
  if (benefit === BenefitType.Other) return fulfillment?.fulfillmentMeta?.expenseType;
  if (benefit === BenefitType.Utility) return fulfillment?.fulfillmentMeta?.utilityType;

  return undefined;
};

function getCheckDate(payment: Payment): string {
  if (payment?.completedAt) return formatDate(payment.completedAt, DateFormat.ShortSlashes, true);
  if (payment?.initiatedAt) return formatDate(payment.initiatedAt, DateFormat.ShortSlashes, true);
  return undefined;
}

function mapRows({
  applications,
  benefit,
}: {
  applications: Application[];
  benefit?: BenefitType;
}): PaymentHistoryRow[] {
  return applications.flatMap(
    ({ displayId, case: { id: caseId, fulfillments, program } }): PaymentHistoryRow[] =>
      fulfillments
        .filter(({ fulfillmentMeta }) => !benefit || benefit === fulfillmentMeta?.benefit)
        .map(
          ({ id, payments: [payment], fund, fulfillmentMeta }): PaymentHistoryRow => ({
            id,
            applicationId: {
              displayId,
              link: makeRoute(PORTAL_ROUTES.CASE_REVIEW, { programId: program.id, caseId }),
            },
            checkDate: getCheckDate(payment),
            status: checkFeature(program?.features, FeatureName.PaymentsExternalTracking)
              ? getExternalPaymentStatusDisplay(payment.status)
              : PaymentStatusDisplay[payment.status],
            payee: {
              name: payment.payee.name,
              link: isVendorPayee(payment.payee)
                ? // TODO: Link to the correct vendor page (EN-1180)
                  `/portal/vendors/${payment.payee.id}`
                : undefined,
            },
            type: getPaymentRequestType(payment, benefit),
            amount: `${fund.name}${
              fulfillmentMeta?.type ? ` ${fulfillmentMeta.type.substring(0, 3)}` : ''
            }: ${formatCurrency(payment?.amount ?? 0, true)}`,
            months: fulfillmentMeta?.months,
          }),
        ),
  );
}

const COLUMNS = (
  benefitType: BenefitType,
  hasWorkflowCore: boolean,
): {
  key: string;
  title: string;
  Cell: ({ applicationId: { displayId, link } }: PaymentHistoryRow) => JSX.Element;
}[] => [
  {
    key: 'applicationId',
    title: 'Application ID',
    Cell: function ApplicationIdCell({
      applicationId: { displayId, link },
    }: PaymentHistoryRow): JSX.Element {
      return <Link to={link}>{displayId} </Link>;
    },
  },
  {
    key: 'checkDate',
    title: !hasWorkflowCore ? 'Service Date' : 'Payment Date',
    Cell: function CheckDateCell({ checkDate }: PaymentHistoryRow): JSX.Element {
      return <>{checkDate}</>;
    },
  },
  {
    key: 'status',
    title: 'Status',
    Cell: function StatusCell({ status }: PaymentHistoryRow): JSX.Element {
      return <>{status}</>;
    },
  },
  {
    key: 'payee',
    title: 'Payee',
    Cell: function PayeeCell({ payee: { name, link } }: PaymentHistoryRow): JSX.Element {
      return link ? <Link to={link}>{name}</Link> : <>{name}</>;
    },
  },
  ...(!hasWorkflowCore && benefitType !== BenefitType.Rental
    ? [
        {
          key: 'type',
          title: 'Type',
          Cell: function TypeCell({ type }: PaymentHistoryRow): JSX.Element {
            return <>{type}</>;
          },
        },
      ]
    : []),
  {
    key: 'amount',
    title: 'Approved Amount',
    Cell: function AmountCell({ amount }: PaymentHistoryRow): JSX.Element {
      return <>{amount}</>;
    },
  },
];

interface PaymentHistoryTableProps {
  applications: Application[];
  program: Program;
  benefit?: BenefitType;
}

export default function PaymentHistoryTable({
  benefit,
  applications,
  program,
}: PaymentHistoryTableProps): JSX.Element {
  const rows = mapRows({ applications, benefit });

  if (rows.length === 0)
    return (
      <div className="pt-5 pb-4">
        <Typography variant="body">No Payments.</Typography>
      </div>
    );
  const hasExternalTracking = checkFeature(program?.features, FeatureName.PaymentsExternalTracking);
  const hasWorkflowExtended = checkFeature(program?.features, FeatureName.WorkflowExtended);
  const hasWorkflowCore = checkFeature(program?.features, FeatureName.WorkflowCore);

  const columns = COLUMNS(benefit, hasWorkflowCore);

  if (hasExternalTracking && hasWorkflowExtended) {
    columns.push({
      key: 'months',
      title: 'Months Covered',
      Cell: function MonthsCell({ months }: PaymentHistoryRow): JSX.Element {
        return <>{months?.length && formatMonths(months)}</>;
      },
    });
  }

  return (
    <table className="w-full mt-5">
      <thead>
        <tr
          className={cx(
            'grid gap-x-2 md:gap-x-4',
            {
              'grid-cols-5': columns.length === 5,
            },
            {
              'grid-cols-6': columns.length === 6,
            },
            {
              'grid-cols-7': columns.length === 7,
            },
          )}
        >
          {columns.map(
            ({ key, title }): JSX.Element => (
              <th className="text-left break-all" key={key}>
                <Typography variant="label" tag="span">
                  {title}
                </Typography>
              </th>
            ),
          )}
        </tr>
      </thead>
      <tbody>
        {rows.map(
          (row): ReactNode => (
            <tr
              key={row.id}
              className={cx(
                'grid gap-x-2 md:gap-x-4',
                {
                  'grid-cols-5': columns.length === 5,
                },
                {
                  'grid-cols-6': columns.length === 6,
                },
                {
                  'grid-cols-7': columns.length === 7,
                },
              )}
            >
              {columns.map(
                ({ key, Cell }): JSX.Element => (
                  <td className="pt-4 pb-2 break-all" key={`${key}-${row.id}`}>
                    <Typography variant="body" tag="span">
                      <Cell {...row} />
                    </Typography>
                  </td>
                ),
              )}
            </tr>
          ),
        )}
      </tbody>
    </table>
  );
}
