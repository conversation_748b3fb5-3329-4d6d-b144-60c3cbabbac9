'use client';
import { programHasPayments } from '@/app/_utils/programs';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import PaymentModal from '@/spa-legacy/portal/components/PaymentModal';
import { CaseContext } from '@/spa-legacy/portal/hooks/useCase';
import { useQuery } from '@apollo/client';
import { checkFeature, checkFeatures } from '@bybeam/platform-lib/features/check';
import { Case, FeatureName, Fulfillment } from '@bybeam/platform-types';
import { Box, Flex } from '@radix-ui/themes';
import { useParams } from 'next/navigation';
import { ReactElement, useState } from 'react';
import PaymentHistorySection from '../components/PaymentHistory';
import RequestSummary from '../components/RequestSummary';
import GetCasePaymentsQuery from './CasePayments.graphql';
import PaymentActions from './components/PaymentActions';

export default function CasePayments(): ReactElement {
  const { caseId } = useParams<{ programId: string; caseId: string }>();
  const [selectedFulfillment, setSelectedFulfillment] = useState<Fulfillment>();
  const program = useProgram();

  const partnerIssuedPaymentsEnabled = checkFeature(
    program?.features,
    FeatureName.PaymentsPartnerIssued,
  );

  const checkPrintEnabled = checkFeatures(program?.features, [
    FeatureName.PaymentsExternalTracking,
    FeatureName.PaymentsDownloadCheckPrint,
  ]);

  const { data, loading, error } = useQuery<
    { cases: { cases: Case[] } },
    { id: string; bankAccount: boolean; fundConfig: boolean }
  >(GetCasePaymentsQuery, {
    variables: {
      id: caseId,
      bankAccount: partnerIssuedPaymentsEnabled,
      fundConfig: checkPrintEnabled,
    },
  });

  const case_ = data?.cases?.cases?.[0];

  if (error) {
    return <>Something went wrong.</>;
  }

  if (loading) {
    return <LoadingComponent />;
  }

  if (!case_) {
    return <>Failed to load details about this case.</>;
  }

  if (!program) {
    return <>Failed to load details about this program.</>;
  }

  return (
    <CaseContext.Provider value={case_}>
      {!!selectedFulfillment && (
        <PaymentModal
          onClose={() => {
            setSelectedFulfillment(undefined);
          }}
          fulfillment={selectedFulfillment}
          case={case_}
        />
      )}
      <Flex direction="column" px="4" py="3">
        <Flex justify="end" align="center" gap="2" wrap="wrap" mb="2">
          <PaymentActions
            case={case_}
            program={program}
            initiatePayment={(fulfillment: Fulfillment) => setSelectedFulfillment(fulfillment)}
          />
        </Flex>
        <Box>
          <Flex direction="column" gap="2">
            {programHasPayments(program) ? (
              <Flex direction="column" gap="2">
                <RequestSummary />
                <PaymentHistorySection {...case_} program={program} />
              </Flex>
            ) : (
              'Payments are not enabled for this program'
            )}
          </Flex>
        </Box>
      </Flex>
    </CaseContext.Provider>
  );
}
