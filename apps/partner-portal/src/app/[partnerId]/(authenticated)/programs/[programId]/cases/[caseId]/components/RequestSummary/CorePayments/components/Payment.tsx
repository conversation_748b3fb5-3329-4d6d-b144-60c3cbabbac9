import { BEAM_SUPPORT_EMAIL } from '@/app/_utils/constants';
import { isFulfillmentInitiated, isFulfillmentValid } from '@/app/_utils/payments';
import { Permission } from '@/app/_utils/roles';
import CanAccess from '@/app/components/features/CanAccess';
import useForm, {
  type SimpleFieldUpdateAction,
  type ValidationErrors,
  ValidationMode,
  fieldUpdate,
  resetForm,
} from '@/app/hooks/useForm';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { useCanAccess } from '@/app/providers/CanAccessProvider';
import ErrorDisplay from '@/spa-legacy/common/components/ErrorDisplay';
import EmailLink from '@/spa-legacy/common/components/Link/EmailLink';
import useFulfillment from '@/spa-legacy/common/hooks/contexts/useFulfillment';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { centsToDollars } from '@/spa-legacy/common/utilities/currency';
import { hasAuthorizedPayment } from '@/spa-legacy/common/utilities/payment';
import PaymentModal from '@/spa-legacy/portal/components/PaymentModal';
import PaymentField from '@/spa-legacy/portal/components/RequestSummary/PaymentField';
import useSavePayment from '@/spa-legacy/portal/components/RequestSummary/hooks/useSavePayment';
import getRequestSummaryFields from '@/spa-legacy/portal/components/RequestSummary/utils/fields';
import {
  buildFormValidation,
  convertPaymentToFormData,
  validateFundBalances,
} from '@/spa-legacy/portal/components/RequestSummary/utils/formData';
import { isPaymentEditable } from '@/spa-legacy/portal/components/RequestSummary/utils/payment';
import type { PaymentFormData } from '@/spa-legacy/portal/components/RequestSummary/utils/types';
import useCase from '@/spa-legacy/portal/hooks/useCase';
import { downloadFile } from '@/spa-legacy/portal/utils/client';
import { getFirstPartyApplicationConfig } from '@/spa-legacy/utilities/application/configs';
import { useMutation } from '@apollo/client';
import { checkFeature, checkFeatures } from '@bybeam/platform-lib/features/check';
import {
  Case,
  CaseStatus,
  FeatureName,
  Fulfillment,
  type MutationResponse,
  PaymentStatus,
  type Payment as PaymentType,
  Program,
  type RemovePaymentInput,
  ScheduleType,
} from '@bybeam/platform-types';
import {
  CheckCircledIcon,
  Cross2Icon,
  DotsVerticalIcon,
  DownloadIcon,
  InfoCircledIcon,
  Pencil1Icon,
  TrashIcon,
} from '@radix-ui/react-icons';
import {
  Button,
  Callout,
  Dialog,
  DropdownMenu,
  Flex,
  Grid,
  Heading,
  IconButton,
} from '@radix-ui/themes';
import { usePostHog } from 'posthog-js/react';
import { type FormEvent, useEffect, useRef, useState } from 'react';
import styles from '../CorePayments.module.css';
import RemovePayment from '../RemovePaymentMutation.graphql';
import generateCheckPrintFile from '../utils/generateCheckPrintFile';

interface PaymentProps {
  formData: PaymentFormData;
  onRemove: () => void;
  onUpdate: (data: PaymentFormData) => void;
  onUpdateField: (action: SimpleFieldUpdateAction<PaymentFormData>) => void;
  totalCount: number;
}

const canDisplayKebabMenu = (program: Program, payment: PaymentType | undefined) => {
  if (
    checkFeatures(program?.features, [
      FeatureName.PaymentsMultiPayment,
      FeatureName.PaymentsExternalTracking,
    ])
  )
    return true;
  if (
    checkFeatures(program?.features, [
      FeatureName.PaymentsMultiPayment,
      FeatureName.PaymentsPartnerIssued,
    ])
  ) {
    return !payment || payment.status === PaymentStatus.Pending;
  }
  return false;
};

const canInitiate = (case_: Case, fulfillment: Fulfillment | undefined) => {
  if (!fulfillment || !case_) return false;
  return (
    [CaseStatus.InReview, CaseStatus.ReadyForReview, CaseStatus.Approved].includes(case_?.status) &&
    (isFulfillmentValid(fulfillment, true) || hasAuthorizedPayment(fulfillment))
  );
};

/**
 * TODO: use a standard changeset library.
 */
export default function Payment({
  formData: paymentData,
  onRemove,
  onUpdate,
  onUpdateField,
  totalCount,
}: PaymentProps): JSX.Element {
  const [isEditing, setEditing] = useState<boolean>(paymentData?.id?.includes('new') ?? false);
  const [originalFormData] = useState<PaymentFormData>(paymentData);
  const [openInitiateModal, setOpenInitiateModal] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const closeModal = (): void => setOpenInitiateModal(false);
  const postHog = usePostHog();
  const program = useProgram() as Program;
  const case_ = useCase() as Case;
  const fulfillment = useFulfillment();
  const { showSnackbar } = useSnackbar();
  const savedPayment = fulfillment?.payments?.[0];
  // A payment created in the UI but not yet persisted is given an id that starts with "new-".
  // Using this marker is a reliable way to know whether the current payment is brand-new,
  // even before the fulfillment (and thus `savedPayment`) have finished loading.
  const isNewPayment = paymentData?.id?.includes('new');
  const initialRender = useRef(true); // tracks first run of the fund-watching effect
  /**
   * Keeps track of the fund that was selected *when the component first mounted*.
   * For existing payments we only want to prefill when the fund is changed by the user,
   * not on the initial data load.
   */
  const originalFundRef = useRef<string | undefined>(paymentData.fund);
  /**
   * Stores the originally-saved approved amount so it can be restored if the
   * user navigates back to the original fund while editing.
   */
  const originalApprovedAmountRef = useRef<string>(paymentData.approvedAmount);
  const applicationConfig = getFirstPartyApplicationConfig(program);
  const { canAccess: hasFiscalPermission } = useCanAccess({
    resource: { objectType: 'PROGRAM', objectId: program?.id },
    permission: Permission.Fiscal,
  });
  const canEditPayment = isPaymentEditable(program, savedPayment);

  const {
    formData,
    trySubmit,
    dispatch,
    errors: rawErrors,
    counters,
  } = useForm<PaymentFormData>(
    paymentData,
    buildFormValidation(
      program,
      case_,
      false /* skipBalanceValidation */,
      !postHog.isFeatureEnabled(
        POSTHOG_FEATURE_FLAGS.paymentMailingAddress,
      ) /** skipAddressValidation */,
    ),
    ValidationMode.RequiredOnSubmit,
  );

  const fields = getRequestSummaryFields({
    program,
    fulfillment,
    applicationConfig,
    formData,
    case: case_,
    isMailingAddressFeatureEnabled: postHog.isFeatureEnabled(
      POSTHOG_FEATURE_FLAGS.paymentMailingAddress,
    ),
  });

  /** On Submit */
  const { doMutation, loading } = useSavePayment(case_.id);
  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    trySubmit(async () => {
      if (Object.keys(fundErrors).length > 0) return;
      const { success, record } = await doMutation(formData);
      if (success) {
        setEditing(false);
        onUpdate(
          convertPaymentToFormData(program, case_, {
            ...record.fulfillment,
            payments: [record],
          } as Fulfillment),
        );
      }
    });
  };

  /** On Remove */
  const [removePayment] = useMutation<
    { payment: { remove: MutationResponse<PaymentType> } },
    { input: RemovePaymentInput; caseId: string }
  >(RemovePayment);
  const onRemovePayment = async () => {
    if (savedPayment) {
      await removePayment({
        variables: {
          input: { id: savedPayment.id },
          caseId: case_.id,
        },
      });
    }
    onRemove();
  };

  const fundErrors = validateFundBalances([formData], program, case_);
  const errors = canEditPayment ? rawErrors : {};
  const isInitiated = !!fulfillment && isFulfillmentInitiated(fulfillment);

  const displayControlButtons =
    canEditPayment &&
    (checkFeature(program?.features, FeatureName.PaymentsMultiPayment)
      ? !fulfillment || isEditing
      : true);

  const displayInitiateButton =
    hasFiscalPermission &&
    checkFeatures(program?.features, [
      FeatureName.PaymentsMultiPayment,
      FeatureName.PaymentsPartnerIssued,
    ]);

  const displayCheckDownloadButton =
    hasFiscalPermission &&
    !!savedPayment &&
    checkFeatures(program?.features, [
      FeatureName.PaymentsExternalTracking,
      FeatureName.PaymentsDownloadCheckPrint,
    ]);

  const onDownloadCheckPrint = () => {
    try {
      if (!savedPayment) return;
      downloadFile(
        generateCheckPrintFile(case_, fulfillment, savedPayment),
        `check-print-${paymentData.checkNumber}.csv`,
      );
    } catch (err) {
      console.error(err);
      showSnackbar(
        `Failed to download check print. ${err instanceof Error ? err.message : 'Unknown error.'}`,
      );
    }
  };

  /**
   * Prefill Approved Amount whenever the selected fund changes.
   *
   * The value is pulled from the first application’s answers using the
   * fund-specific `defaultPaymentFieldKey` (when available).
   * • Runs every time `formData.fund` changes (no check on `approvedAmount`).
   * • Safely exits if the answer cannot be resolved or is not numeric.
   * • Read-only display fields remain unchanged because they derive their value
   *   directly from the application answer map.
   */
  useEffect(() => {
    // Exit early if no fund is selected.
    if (!formData.fund) return;

    /*
     * On the very first render:
     *   – EXISTING payment → record the loaded fund and exit (no prefill)
     *   – NEW payment      → allow normal prefill behaviour
     */
    if (initialRender.current) {
      initialRender.current = false;
      if (!isNewPayment) {
        originalFundRef.current = formData.fund;
        return;
      }
    }

    /*
     * For EXISTING payments:
     *   – If the user switches back to the fund that was originally attached to
     *     the payment, restore the payment’s saved approved amount instead of
     *     leaving whatever value may have been prefixed from another fund.
     *   – Otherwise continue with default-field-based prefill behaviour.
     */
    if (!isNewPayment && formData.fund === originalFundRef.current) {
      if (formData.approvedAmount !== originalApprovedAmountRef.current) {
        dispatch(fieldUpdate('approvedAmount', originalApprovedAmountRef.current));
        onUpdateField(fieldUpdate('approvedAmount', originalApprovedAmountRef.current));
      }
      return;
    }

    const selectedFund = program.funds.find(({ id }) => id === formData.fund);
    if (!selectedFund?.defaultPaymentFieldKey) return;

    // Pull the amount (in **cents**) from the first application’s answers map.
    const amountAnswer = case_?.applications?.[0]?.answers?.[selectedFund.defaultPaymentFieldKey];

    if (typeof amountAnswer !== 'number' || Number.isNaN(amountAnswer)) return;

    const amountString = centsToDollars(amountAnswer ?? 0);

    // Update both local form state and parent container.
    dispatch(fieldUpdate('approvedAmount', amountString));
    onUpdateField(fieldUpdate('approvedAmount', amountString));
  }, [formData.fund]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <>
      <Flex
        direction="column"
        px="4"
        py="4"
        className={styles.CorePaymentsContainer}
        key={formData.id}
      >
        <Flex justify="between" align="center">
          <Heading size="3">Payment</Heading>
          {canDisplayKebabMenu(program, fulfillment?.payments?.[0]) && (
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <IconButton variant="ghost">
                  <DotsVerticalIcon />
                </IconButton>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                {!!fulfillment && (
                  <DropdownMenu.Item
                    disabled={loading || isEditing}
                    onSelect={() => setEditing(true)}
                  >
                    <Pencil1Icon /> Edit
                  </DropdownMenu.Item>
                )}
                <DropdownMenu.Item
                  disabled={loading || totalCount === 1}
                  onSelect={() => setOpenDeleteDialog(true)}
                >
                  <TrashIcon /> Remove
                </DropdownMenu.Item>
                {displayCheckDownloadButton && (
                  <DropdownMenu.Item onSelect={onDownloadCheckPrint}>
                    <DownloadIcon /> Download Check Print File
                  </DropdownMenu.Item>
                )}
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          )}
        </Flex>
        <form key={formData.id} onSubmit={onSubmit} noValidate>
          <fieldset>
            <Grid columns="4" gap="4" align="start" py="4">
              {fields.map((field) => (
                <PaymentField
                  key={field}
                  field={field}
                  formData={formData}
                  errors={{
                    ...(errors ?? ({} as ValidationErrors<PaymentFormData>)),
                    ...(!!formData.fund && fundErrors[formData.fund] && { approvedAmount: true }),
                  }}
                  dispatch={(action: SimpleFieldUpdateAction<PaymentFormData>) => {
                    dispatch(fieldUpdate(action.payload.field, action.payload.value));
                    onUpdateField(action);
                  }}
                  isEditing={isEditing}
                  case={case_}
                  program={program}
                  fulfillment={fulfillment}
                />
              ))}
              {(displayInitiateButton || displayControlButtons) && !isInitiated && (
                <Flex
                  justify="end"
                  align="center"
                  gap="4"
                  wrap="wrap"
                  style={{
                    gridColumnEnd: 5,
                    minHeight: '3rem',
                  }}
                >
                  {displayControlButtons ? (
                    <CanAccess resource={{ objectId: case_?.id, objectType: 'CASE' }}>
                      {isEditing ? (
                        <>
                          {!!fulfillment && (
                            <Button
                              id="cancel-payment-button"
                              data-cy="cancel-payment-button"
                              loading={loading}
                              variant="outline"
                              type="button"
                              onClick={(): void => {
                                dispatch(resetForm(originalFormData));
                                onUpdate(originalFormData);
                                setEditing(false);
                              }}
                            >
                              Cancel
                            </Button>
                          )}
                          <Button
                            id="save-payment-button"
                            type="submit"
                            data-cy="save-payment-button"
                            disabled={loading}
                          >
                            {fulfillment ? 'Save changes' : 'Save payment'}
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            id="edit-payment-button"
                            variant="outline"
                            type="button"
                            onClick={(): void => setEditing(true)}
                            loading={loading}
                            data-cy="edit-payment-button"
                          >
                            Edit
                          </Button>
                          {displayCheckDownloadButton && (
                            <Button
                              id="download-check-button"
                              type="button"
                              onClick={onDownloadCheckPrint}
                              data-cy="download-check-button"
                            >
                              Download Check Print File
                            </Button>
                          )}
                        </>
                      )}
                    </CanAccess>
                  ) : (
                    displayInitiateButton && (
                      <Button
                        id="initiate-payment-button"
                        type="button"
                        onClick={(): void => setOpenInitiateModal(true)}
                        data-cy="initiate-payment-button"
                        key="edit"
                        disabled={!canInitiate(case_, fulfillment)}
                      >
                        Initiate Payment
                      </Button>
                    )
                  )}
                </Flex>
              )}
            </Grid>
            <Flex direction="column" gap="4" pt="2">
              {checkFeature(program?.features, FeatureName.PaymentsPartnerIssued) &&
                isInitiated &&
                (savedPayment?.status === PaymentStatus.Failed ? (
                  <Callout.Root highContrast color="red" style={{ width: 'fit-content' }}>
                    <Callout.Icon>
                      <InfoCircledIcon />
                    </Callout.Icon>
                    <Callout.Text>
                      Unfortunately, we were unable to initiate this payment. Please contact us at{' '}
                      <EmailLink>{BEAM_SUPPORT_EMAIL}</EmailLink> and we will work with you to
                      resolve this issue as quickly as possible.
                    </Callout.Text>
                  </Callout.Root>
                ) : (
                  <Callout.Root highContrast color="green" style={{ width: 'fit-content' }}>
                    <Callout.Icon>
                      <CheckCircledIcon />
                    </Callout.Icon>
                    <Callout.Text>
                      Payment{' '}
                      {fulfillment.scheduleType === ScheduleType.OneTime
                        ? 'initiated'
                        : 'scheduled'}
                    </Callout.Text>
                  </Callout.Root>
                ))}
              {Object.entries(fundErrors).map(([fund, message]) => (
                <ErrorDisplay key={fund} message={message} visible={true} />
              ))}
              <ErrorDisplay
                errors={counters.missingRequiredFields}
                visible={counters.missingRequiredFields > 0}
              />
            </Flex>
          </fieldset>
        </form>
        {openInitiateModal && !!fulfillment && (
          <PaymentModal onClose={closeModal} fulfillment={fulfillment} case={case_} />
        )}
      </Flex>
      <Dialog.Root open={openDeleteDialog} onOpenChange={() => setOpenDeleteDialog(false)}>
        <Dialog.Content size="3" maxWidth="35rem">
          <Flex justify="end">
            <Dialog.Close>
              <IconButton variant="ghost" aria-label="close dialog">
                <Cross2Icon />
              </IconButton>
            </Dialog.Close>
          </Flex>
          <Dialog.Title mb="4">Remove Payment?</Dialog.Title>
          <Dialog.Description>
            This will remove the payment you've added. This action cannot be undone.
          </Dialog.Description>
          <Flex mt="4" gap="3" justify="end">
            <Dialog.Close>
              <Button variant="outline">Cancel</Button>
            </Dialog.Close>
            <Button
              onClick={() => {
                setOpenDeleteDialog(false);
                onRemovePayment();
              }}
            >
              Remove Payment
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
