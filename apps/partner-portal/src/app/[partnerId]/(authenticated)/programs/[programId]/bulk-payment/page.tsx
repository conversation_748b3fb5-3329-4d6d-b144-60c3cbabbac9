'use client';
import Page from '@/app/components/ui/Page/Page';
import { useUserHomeRoute } from '@/app/hooks/useUserHomeRoute';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import useCustomPageTitle from '@/spa-legacy/common/hooks/useCustomPageTitle';
import BulkPaymentDetails from '@/spa-legacy/portal/pages/BulkPayment/components/BulkPaymentDetails';
import { Route } from 'next';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
export default function ProgramViewPage() {
  const program = useProgram();
  useCustomPageTitle(`${program?.name} Applications`);
  const defaultRoute = useUserHomeRoute();
  const router = useRouter();

  // Perform redirect as a side effect to avoid navigation during render.
  useEffect(() => {
    if (!program) {
      router.replace(defaultRoute as Route);
    }
  }, [program, defaultRoute, router]);

  // While redirecting, render an accessible status so no UI is committed pre-navigation.
  if (!program) {
    return (
      <span role="status" aria-live="polite">
        Redirecting…
      </span>
    );
  }

  return (
    <Page>
      <Page.Header>
        <Page.Breadcrumbs>
          <Page.Breadcrumbs.Crumb>
            <Link href={{ pathname: './' }}>{program?.name}</Link>
          </Page.Breadcrumbs.Crumb>
          <Page.Breadcrumbs.Crumb>Bulk Payment</Page.Breadcrumbs.Crumb>
        </Page.Breadcrumbs>
      </Page.Header>
      <BulkPaymentDetails />
    </Page>
  );
}
