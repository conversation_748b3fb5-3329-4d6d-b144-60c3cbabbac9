import { checkPartnerHasFeature } from '@/app/_utils/checkFeature';
import { programHasPayments } from '@/app/_utils/programs';
import { DL, DLItem } from '@/app/components/ui/DescriptionList';
import Modal from '@/spa-legacy/common/components/Modal';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import usePollingQuery from '@/spa-legacy/common/hooks/usePollingQuery';
import { formatCurrency, formatNumber } from '@/spa-legacy/common/utilities/format';
import { FundMetric, getFundDisplayMap } from '@/spa-legacy/portal/components/FundSummary';
import GetProgramOverviewQuery from '@/spa-legacy/portal/graphql/queries/GetProgramOverviewQuery.graphql';
import { fundDisplay } from '@/spa-legacy/portal/utils/programs';
import {
  ProgramWorkflow,
  getProgramWorkflow,
  getTextByWorkflow,
  labelKeys,
} from '@/spa-legacy/portal/utils/workflow';
import assertExhaustive from '@bybeam/platform-lib/utilities/assertExhaustive';
import { FeatureName, PaymentStatus, Program } from '@bybeam/platform-types';
import { InfoCircledIcon } from '@radix-ui/react-icons';
import { Button, Tooltip } from '@radix-ui/themes';
import { usePostHog } from 'posthog-js/react';
import { useState } from 'react';
import BulkFileActions from './BulkFileActions';
import styles from './ProgramStats.module.css';

interface Aggregate {
  count: number;
}

interface ProgramOverview {
  applicationsStarted: Aggregate;
  applicationsSubmitted: Aggregate;
  paymentsSent: Aggregate;
  programs: { programs: Program[] };
}
enum Summary {
  Started = 'Started',
  Submitted = 'Submitted',
  Reviewed = 'Reviewed',
  Certified = 'Certified',
  PaymentSent = 'PaymentSent',
  Approved = 'Approved',
  Denied = 'Denied',
}

const WORKFLOW_SUMMARY: { [feature in ProgramWorkflow]: Summary[] } = {
  [FeatureName.WorkflowCore]: [
    Summary.Started,
    Summary.Submitted,
    Summary.Reviewed,
    Summary.PaymentSent,
    Summary.Approved,
    Summary.Denied,
  ],
  [FeatureName.WorkflowExtended]: [
    Summary.Started,
    Summary.Submitted,
    Summary.Reviewed,
    Summary.Certified,
    Summary.PaymentSent,
    Summary.Denied,
  ],
};

function WorkflowMetric({
  metric,
  workflow,
  data,
}: {
  metric: Summary;
  workflow: ProgramWorkflow;
  data: ProgramOverview;
}): JSX.Element {
  const program = data?.programs.programs[0];
  switch (metric) {
    case Summary.Started:
      return (
        <DLItem
          label="Applications Started"
          value={formatNumber(data?.applicationsStarted.count ?? 0)}
        />
      );
    case Summary.Submitted:
      return (
        <DLItem
          label="Applications Submitted"
          value={formatNumber(data?.applicationsSubmitted.count ?? 0)}
        />
      );
    case Summary.Reviewed:
      return (
        <DLItem
          label={getTextByWorkflow(workflow, labelKeys.inReviewDashboard)}
          value={formatNumber(program?.workflowSummary?.casesReviewed ?? 0)}
        />
      );
    case Summary.Certified:
      return (
        <DLItem
          label="Certified"
          value={formatNumber(program?.workflowSummary?.casesCertified ?? 0)}
        />
      );
    case Summary.PaymentSent:
      return <DLItem label="Payments Sent" value={formatNumber(data?.paymentsSent?.count ?? 0)} />;
    case Summary.Approved:
      return (
        <DLItem
          label="Approved"
          value={formatNumber(program?.workflowSummary?.casesApproved ?? 0)}
        />
      );
    case Summary.Denied:
      return (
        <DLItem label="Denied" value={formatNumber(program?.workflowSummary?.casesDenied ?? 0)} />
      );
    default:
      return assertExhaustive(metric);
  }
}

const ProgramStats: React.FC = () => {
  const program = useProgram();
  const partner = usePartner();
  const posthog = usePostHog();

  if (!program) {
    return <>Program details unavailable</>;
  }

  const [isBulkActionModalOpen, setIsBulkActionModalOpen] = useState(false);

  const programWorkflow = getProgramWorkflow(program);
  const {
    data: queryData,
    loading,
    error,
  } = usePollingQuery<ProgramOverview>(GetProgramOverviewQuery, {
    variables: { programId: program?.id, status: [PaymentStatus.Success, PaymentStatus.Initiated] },
    skip: !program,
  });

  const fundDisplayMap = getFundDisplayMap(programWorkflow);
  return (
    <div>
      {programHasPayments(program) && (
        <div className="p-4 border-b border-tableBorder">
          <div className="text-textSecondary mb-2">
            <strong>Program Funding</strong>
          </div>
          <ul className={styles.programFunds}>
            {fundDisplay(queryData?.programs?.programs?.[0])?.map(({ id, name, fundData }) => (
              <li key={id} className={styles.fundDetails}>
                <div>{name}</div>
                <DL>
                  {Object.entries(fundData).map(([key, value]) => {
                    const metric = {
                      ...fundDisplayMap.get(key as FundMetric),
                      value: formatCurrency(value) ?? 'N/A',
                    };
                    return (
                      <DLItem
                        key={metric.label}
                        label={
                          <span className={styles.statLabel}>
                            {metric.label}{' '}
                            {metric.details ? (
                              <Tooltip content={metric.details}>
                                <InfoCircledIcon />
                              </Tooltip>
                            ) : null}
                          </span>
                        }
                        value={metric.value}
                      />
                    );
                  })}
                </DL>
              </li>
            ))}
          </ul>
        </div>
      )}
      <div className="px-4 py-4 border-b border-tableBorder">
        <div className="text-textSecondary mb-2">
          <strong>Last 7 Days</strong>
        </div>
        <DL>
          {WORKFLOW_SUMMARY[programWorkflow].map((key) => (
            <WorkflowMetric key={key} metric={key} data={queryData} workflow={programWorkflow} />
          ))}
        </DL>
      </div>
      {checkPartnerHasFeature(partner, FeatureName.BulkFileActions) && (
        <div className="p-4">
          <div className="mb-2">
            <div className="text-textSecondary">
              <strong>Bulk file actions</strong>
            </div>
            <small>Use a file to update the status of multiple cases at once.</small>
          </div>
          <Button
            variant="outline"
            onClick={() => {
              setIsBulkActionModalOpen(true);
            }}
          >
            Get started
          </Button>
          {isBulkActionModalOpen ? (
            <Modal
              size="M"
              title={'Change case statuses with a file'}
              isOpen={true}
              onClickClose={() => {
                setIsBulkActionModalOpen(false);
              }}
            >
              <BulkFileActions onSuccess={() => setIsBulkActionModalOpen(false)} />
            </Modal>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default ProgramStats;
