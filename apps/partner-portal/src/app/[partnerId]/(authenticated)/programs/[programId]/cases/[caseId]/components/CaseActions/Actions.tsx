import { canInitiatePayment } from '@/app/_utils/payments';
import { Permission } from '@/app/_utils/roles';
import Loading from '@/app/loading';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { useCanAccess } from '@/app/providers/CanAccessProvider';
import { useNextAuth } from '@/app/providers/NextAuthProvider';
import MenuButton, { type MenuButtonProps } from '@/spa-legacy/common/components/MenuButton';
import Typography from '@/spa-legacy/common/components/Typography';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { getAvailableApplicantTypes } from '@/spa-legacy/common/utilities/applicantType';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import ChangeStatusOrAssigneeModal from '@/spa-legacy/portal/components/ChangeStatusOrAssigneeModal';
import ExpediteModal from '@/spa-legacy/portal/components/ExpediteModal';
import LinkLegacyUserModal from '@/spa-legacy/portal/components/LinkLegacyUserModal';
import ManageCaseParticipants from '@/spa-legacy/portal/components/ManageCaseParticipantsModal';
import SendMessageModal from '@/spa-legacy/portal/components/SendMessageModal';
import useCreateApplication from '@/spa-legacy/portal/hooks/useCreateApplication';
import useTransitionStatus from '@/spa-legacy/portal/hooks/useTransitionStatus';
import { isReadyToPay } from '@/spa-legacy/portal/utils/applications';
import { canEditCase } from '@/spa-legacy/portal/utils/roles';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkProgramHasAnyFeature } from '@/spa-legacy/utilities/checkFeature';
import { programHasPayments } from '@/spa-legacy/utilities/programs';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import {
  type Case,
  CaseStatus,
  EXPEDITED_PRIORITIES,
  FeatureName,
  type Program,
} from '@bybeam/platform-types';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { Button, ButtonProps, Flex, Tooltip } from '@radix-ui/themes';
import { Route } from 'next';
import { useRouter } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { type ReactNode, useState } from 'react';

export enum OpenModal {
  None = '',
  LabelFeedback = 'feedback',
  Payment = 'payment',
}

export enum CaseAction {
  MarkAsPaid = 'MarkAsPaid',
  Deny = 'Deny',
  Approve = 'Approve',
  ChangeStatus = 'ChangeStatus',
  Expedite = 'Expedite',
  UndoExpedite = 'UndoExpedite',
  LinkLegacyUser = 'LinkLegacyUser',
  NewApplication = 'NewApplication',
  InitiatePayment = 'InitiatePayment',
  ViewApplicantProfile = 'ViewApplicantProfile',
  ManageCaseParticipants = 'ManageCaseParticipants',
  SendMessage = 'SendMessage',
}

const DEFAULT_DOT_MENU_ACTIONS = [
  CaseAction.ChangeStatus,
  CaseAction.Expedite,
  CaseAction.UndoExpedite,
  CaseAction.LinkLegacyUser,
  CaseAction.ViewApplicantProfile,
  CaseAction.SendMessage,
];

const GLOBAL_ACTIONS = [CaseAction.ChangeStatus, CaseAction.NewApplication];

const STATUS_ACTIONS: { [status in CaseStatus | string]: CaseAction[] } = {
  [CaseStatus.InProgress]: [
    CaseAction.ChangeStatus,
    CaseAction.LinkLegacyUser,
    CaseAction.ViewApplicantProfile,
    CaseAction.SendMessage,
    CaseAction.ManageCaseParticipants,
  ],
  [CaseStatus.Incomplete]: [
    CaseAction.ChangeStatus,
    CaseAction.LinkLegacyUser,
    CaseAction.ViewApplicantProfile,
    CaseAction.SendMessage,
    CaseAction.ManageCaseParticipants,
  ],
  [CaseStatus.ReadyForReview]: [
    CaseAction.InitiatePayment,
    CaseAction.Approve,
    CaseAction.Deny,
    CaseAction.ManageCaseParticipants,
    ...DEFAULT_DOT_MENU_ACTIONS,
  ],
  [CaseStatus.InReview]: [
    CaseAction.InitiatePayment,
    CaseAction.Approve,
    CaseAction.Deny,
    CaseAction.ManageCaseParticipants,
    ...DEFAULT_DOT_MENU_ACTIONS,
  ],
  [CaseStatus.PendingCertification]: [CaseAction.Deny, ...DEFAULT_DOT_MENU_ACTIONS],
  [CaseStatus.FiscalReview]: [
    CaseAction.MarkAsPaid,
    CaseAction.ChangeStatus,
    CaseAction.ViewApplicantProfile,
    CaseAction.SendMessage,
  ],
  [CaseStatus.Approved]: [
    CaseAction.InitiatePayment,
    CaseAction.ChangeStatus,
    CaseAction.NewApplication,
    CaseAction.ViewApplicantProfile,
    CaseAction.SendMessage,
    CaseAction.ManageCaseParticipants,
  ],
  [CaseStatus.PaymentSent]: [
    CaseAction.ChangeStatus,
    CaseAction.NewApplication,
    CaseAction.ViewApplicantProfile,
    CaseAction.SendMessage,
  ],
  [CaseStatus.Denied]: [
    CaseAction.ChangeStatus,
    CaseAction.NewApplication,
    CaseAction.ViewApplicantProfile,
    CaseAction.SendMessage,
  ],
  [CaseStatus.Withdrawn]: [
    CaseAction.ChangeStatus,
    CaseAction.NewApplication,
    CaseAction.ViewApplicantProfile,
    CaseAction.SendMessage,
  ],
  [CaseStatus.Archived]: [CaseAction.ChangeStatus, CaseAction.SendMessage],
};

const VIEW_ONLY_ACTIONS: CaseAction[] = [CaseAction.ViewApplicantProfile];
const FISCAL_ACTIONS: CaseAction[] = [CaseAction.InitiatePayment];

interface ActionsProps {
  case: Case;
  setOpenInitModal?: (open: OpenModal) => void;
}

export function canChangeStatus(program: Program, case_: Case): boolean {
  switch (case_.status) {
    case CaseStatus.Approved:
      return !checkProgramHasAnyFeature(program, [
        FeatureName.PaymentsClaimFunds,
        FeatureName.PaymentsPartnerIssued,
      ]);
    case CaseStatus.PaymentSent:
      return !programHasPayments(program);
    default:
      return true;
  }
}

export default function Actions({ case: case_, setOpenInitModal }: ActionsProps) {
  const { user } = useNextAuth();
  const postHog = usePostHog();

  const { transitionStatus, loading } = useTransitionStatus(case_.id);
  const [openModal, setOpenModal] = useState<CaseAction>();
  const closeModal = (): void => setOpenModal(undefined);
  const router = useRouter();

  const { doMutation: createApplication } = useCreateApplication();

  const program = useProgram();
  if (!program) return <Loading />;

  const admin = user?.admin;
  if (!admin) return null;
  const { canAccess } = useCanAccess({
    resource: { objectType: 'CASE', objectId: case_.id },
  });
  const { canAccess: canAccessPayments } = useCanAccess({
    resource: { objectType: 'PROGRAM', objectId: program.id },
    permission: Permission.Fiscal,
  });

  const application = case_?.applications?.[0];
  const fulfillment = case_?.fulfillments?.[0];

  const actions = STATUS_ACTIONS[case_.status].filter((action) => {
    const hasAccess =
      canEditCase(admin, case_) ||
      (GLOBAL_ACTIONS.includes(action) && case_.status !== CaseStatus.Archived);

    if (!hasAccess) return false;

    if (action === CaseAction.InitiatePayment)
      return (
        !postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.casePaymentsPage) &&
        checkProgramHasAnyFeature(program, [
          FeatureName.PaymentsClaimFunds,
          FeatureName.PaymentsPartnerIssued,
        ])
      );
    if (action === CaseAction.Approve) {
      return (
        checkFeature(program?.features, FeatureName.PaymentsExternalTracking) ||
        !programHasPayments(program)
      );
    }

    if (action === CaseAction.LinkLegacyUser)
      return (
        checkFeature(program?.features, FeatureName.MigrationLegacyUsers) &&
        !application?.submitter.legacyId
      );

    // All other actions are available by default
    return true;
  });

  if (actions.length === 0) return null;

  const buttonProps: {
    [action in CaseAction]: (ButtonProps | MenuButtonProps<string>) & {
      actionType: 'primary' | 'secondary';
      tooltip?: string;
    };
  } = {
    [CaseAction.Approve]: {
      onClick: (): Promise<boolean> => transitionStatus({ status: CaseStatus.Approved }),
      children: 'Approve',
      disabled: !fulfillment?.approvedAmount && programHasPayments(program),
      actionType: 'primary',
    },
    [CaseAction.MarkAsPaid]: {
      onClick: (): Promise<boolean> => transitionStatus({ status: CaseStatus.PaymentSent }),
      children: 'Mark as Paid',
      disabled: loading || !isReadyToPay(case_),
      actionType: 'primary',
    },
    [CaseAction.Deny]: {
      variant: 'outline',
      size: '3',
      id: 'deny',
      onClick: (): void => setOpenModal(CaseAction.Deny),
      children: 'Deny',
      actionType: 'primary',
    },
    [CaseAction.ChangeStatus]: {
      variant: 'outline',
      onClick: (): void => setOpenModal(CaseAction.ChangeStatus),
      children: 'Change Status or Assignee',
      actionType: 'secondary',
      hidden: !canChangeStatus(program, case_),
    },
    [CaseAction.Expedite]: {
      variant: 'outline',
      onClick: (): void => setOpenModal(CaseAction.Expedite),
      children: 'Expedite',
      hidden: EXPEDITED_PRIORITIES.has(case_.priority),
      actionType: 'secondary',
    },
    [CaseAction.UndoExpedite]: {
      variant: 'outline',
      onClick: (): void => setOpenModal(CaseAction.UndoExpedite),
      children: 'Undo Expedite',
      hidden: !EXPEDITED_PRIORITIES.has(case_.priority),
      actionType: 'secondary',
    },
    [CaseAction.LinkLegacyUser]: {
      variant: 'outline',
      onClick: (): void => setOpenModal(CaseAction.LinkLegacyUser),
      children: 'Link to Legacy User',
      actionType: 'secondary',
    },
    [CaseAction.NewApplication]: {
      variant: 'outline',
      onClick: () =>
        application?.submitter?.id && createApplication(application.submitter.id, [program.id]),
      children: 'Add New Application',
      actionType: 'secondary',
    },
    [CaseAction.InitiatePayment]: {
      onClick: (): void => setOpenInitModal?.(OpenModal.Payment),
      id: 'initiatePayment',
      children: 'Initiate Payment',
      size: '3',
      disabled: !canInitiatePayment(case_, program),
      ...(!canInitiatePayment(case_, program) && {
        tooltip:
          '*Note: To initiate payment, please confirm the information below by clicking the Save button in the Payment Details section.',
      }),
      actionType: 'primary',
    },
    [CaseAction.ViewApplicantProfile]: {
      onClick: () =>
        application?.submitter?.id &&
        router.push(
          makeRoute(PORTAL_ROUTES.APPLICANT_PROFILE, {
            userId: application.submitter.id,
          }) as Route<string>,
        ),
      children: 'View Applicant Profile',
      actionType: 'secondary',
    },
    [CaseAction.ManageCaseParticipants]: {
      onClick: (): void => setOpenModal(CaseAction.ManageCaseParticipants),
      children: 'Manage Case Participants',
      actionType: 'secondary',
      hidden: getAvailableApplicantTypes(program)?.length <= 1,
    },
    [CaseAction.SendMessage]: {
      variant: 'outline',
      onClick: (): void => setOpenModal(CaseAction.SendMessage),
      children: 'Send new message',
      actionType: 'secondary',
    },
  };

  const visibleActions = actions.filter((action) => {
    if (buttonProps[action].hidden) return false;
    if (VIEW_ONLY_ACTIONS.includes(action)) return true;
    if (FISCAL_ACTIONS.includes(action)) return canAccessPayments;
    return canAccess;
  });

  const primaryActions = visibleActions.filter(
    (action) => buttonProps[action].actionType === 'primary',
  );
  const secondaryActions = visibleActions.filter(
    (action) => buttonProps[action].actionType === 'secondary',
  );

  return (
    <Flex gap="2" align="center">
      {primaryActions.map((action) => {
        // Note: Removed 'shrink: true' prop as it's not a valid Radix UI Button/MenuButton prop
        const commonProps = { key: action, disabled: loading };
        const { actionType: _, ...actionProps } = buttonProps[action];
        return 'options' in actionProps ? (
          <MenuButton {...commonProps} {...actionProps} key={action} />
        ) : (
          <Tooltip content={actionProps.tooltip} hidden={!actionProps.tooltip} key={action}>
            <Button {...commonProps} {...actionProps} key={action} />
          </Tooltip>
        );
      })}
      {secondaryActions.length > 0 && (
        <MenuButton
          id="more"
          disabled={loading}
          variant="outline"
          color="gray"
          highContrast
          size="3"
          options={secondaryActions.map((action) => ({
            value: action,
            key: action,
            label: (
              <Typography variant="body" tag="span" textTransform="uppercase" bold>
                {buttonProps[action].children}
              </Typography>
            ),
          }))}
          onClick={(value): void => buttonProps?.[value]?.onClick?.(null)}
        >
          <DotsHorizontalIcon />
        </MenuButton>
      )}
      {((): ReactNode => {
        switch (openModal) {
          case CaseAction.Expedite:
            return <ExpediteModal onClose={closeModal} ids={[case_.id]} />;
          case CaseAction.UndoExpedite:
            return <ExpediteModal onClose={closeModal} ids={[case_.id]} undo />;
          case CaseAction.LinkLegacyUser:
            return (
              application?.submitter?.id && (
                <LinkLegacyUserModal userId={application.submitter.id} onClose={closeModal} />
              )
            );
          case CaseAction.Deny:
          case CaseAction.ChangeStatus:
            return (
              <ChangeStatusOrAssigneeModal
                mode="SINGLE"
                ids={[case_.id]}
                initialState={openModal === CaseAction.Deny ? { status: CaseStatus.Denied } : case_}
                initialStep={openModal === CaseAction.Deny ? 'SetReason' : 'ChooseStatus'}
                onClose={closeModal}
              />
            );
          case CaseAction.ManageCaseParticipants:
            return <ManageCaseParticipants onClose={closeModal} case={case_} />;
          case CaseAction.SendMessage:
            return <SendMessageModal onClose={closeModal} ids={[case_.id]} />;
          default:
            return null;
        }
      })()}
    </Flex>
  );
}
