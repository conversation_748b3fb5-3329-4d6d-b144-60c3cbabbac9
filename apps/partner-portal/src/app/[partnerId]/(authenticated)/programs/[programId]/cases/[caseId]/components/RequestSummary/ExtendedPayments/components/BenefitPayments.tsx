import { BENEFIT_DISPLAY } from '@/app/_utils/benefits';
import { useCanAccess } from '@/app/providers/CanAccessProvider';
import { useNextAuth } from '@/app/providers/NextAuthProvider';
import DetailSection from '@/spa-legacy/portal/components/DetailSection';
import { canEditCase } from '@/spa-legacy/portal/utils/roles';
import type { BenefitType, Case } from '@bybeam/platform-types';
import { PlusIcon } from '@radix-ui/react-icons';
import { Button } from '@radix-ui/themes';
import type { PaymentInput, UseRequestSummaryResponse } from '../useRequestSummary';
import Payment from './Payment';
import RequestTally from './RequestTally';

interface BenefitPaymentsProps {
  autocompleteVendors: UseRequestSummaryResponse['autocompleteVendors'];
  benefit: BenefitType;
  case: Case;
  payments: PaymentInput[];
  onAddPayment: () => void;
  onRemovePayment: (data: PaymentInput) => void;
  onSavePayment: (data: PaymentInput) => Promise<boolean>;
  onAddVendor: () => void;
  loading: boolean;
}

export default function BenefitPayments({
  autocompleteVendors,
  benefit,
  case: case_,
  payments,
  onAddPayment,
  onRemovePayment,
  onSavePayment,
  onAddVendor,
  loading,
}: BenefitPaymentsProps): JSX.Element {
  const {
    user: { admin },
  } = useNextAuth();
  const { canAccess } = useCanAccess({ resource: { objectType: 'CASE', objectId: case_.id } });
  const canEdit = canEditCase(admin, case_) && canAccess;

  return (
    <DetailSection
      title={`${BENEFIT_DISPLAY[benefit]} Request Summary`}
      action={{
        children: 'Add Vendor',
        icon: 'add',
        onClick: onAddVendor,
      }}
    >
      <div className="flex flex-col items-start gap-6">
        <RequestTally benefit={benefit} case={case_} payments={payments} />
        {payments.map((payment) => (
          <Payment
            key={payment.key}
            autocompleteVendors={autocompleteVendors}
            benefit={benefit}
            case={case_}
            initialData={payment}
            onSave={onSavePayment}
            onRemove={(): void => onRemovePayment(payment)}
            loading={loading}
          />
        ))}
        {canEdit && (
          <Button variant="ghost" onClick={onAddPayment}>
            <PlusIcon />
            Add {BENEFIT_DISPLAY[benefit]}
          </Button>
        )}
      </div>
    </DetailSection>
  );
}
