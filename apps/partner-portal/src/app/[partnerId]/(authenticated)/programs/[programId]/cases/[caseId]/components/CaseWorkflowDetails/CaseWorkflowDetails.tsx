import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';
import { VerificationStatus as VerificationStatusComponent } from '@/app/components/features/Verification';
import CopyToClipboard from '@/app/components/ui/CopyToClipboard/CopyToClipboard';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { hasMultiParty } from '@/spa-legacy/common/utilities/applicantType';
import { formatSnakeToTitleCase } from '@/spa-legacy/common/utilities/format';
import { formatCaseStatus } from '@/spa-legacy/common/utilities/statuses';
import { getNeedLevelTierColumnValue } from '@/spa-legacy/portal/components/ProgramApplicationsTable/utils/columnHelpers';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import sort from '@bybeam/platform-lib/utilities/sort';
import { ApplicantTypeRole, Case, EXPEDITED_PRIORITIES, FeatureName } from '@bybeam/platform-types';
import { Flag as FlagIcon } from '@mui/icons-material';
import { Badge, DataList, Flex, Link as RadixLink, Text, Tooltip } from '@radix-ui/themes';
import Link from 'next/link';
import { usePostHog } from 'posthog-js/react';
import React, { useMemo } from 'react';
import styles from './CaseWorkflowDetails.module.css';

interface CaseWorkflowDetailsProps {
  caseDetail?: Case;
}

const CaseWorkflowDetails: React.FC<CaseWorkflowDetailsProps> = ({ caseDetail }) => {
  if (!caseDetail) return null;

  const posthog = usePostHog();
  const program = useProgram();

  const isMultiParty = program && hasMultiParty(program);

  // TODO: Make this more declaritive in the API response so that it doesn't need to be parsed here.
  // or move this to a utility function in follow up tickets.
  const application =
    caseDetail.applications?.find(
      (app) =>
        app.submitter?.applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty,
    ) ?? caseDetail.applications?.[0];

  const currentVersion = application?.versions?.[0];
  const latestScore = application?.score?.score;
  const applicationRoute = makeRoute(PORTAL_ROUTES.APPLICATION_REVIEW_LEGACY, {
    caseId: caseDetail?.id,
    applicationId: application?.id || '',
  });

  const needLevelTier = useMemo(() => {
    return typeof latestScore === 'number' && Number.isFinite(latestScore)
      ? getNeedLevelTierColumnValue(latestScore)
      : null;
  }, [latestScore]);

  const applicationVerification = useMemo(() => {
    if (!caseDetail.applications) return undefined;
    const [latestVerification] = sort(caseDetail?.applications, {
      accessor: (app) => app.createdAt,
      ascending: false,
    });
    return latestVerification.verification;
  }, [caseDetail]);

  const programRoute = makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, {
    programId: caseDetail?.program?.id || program?.id || '',
  });

  return (
    <div className={styles.CaseWorkflowDetails}>
      {caseDetail && (
        <DataList.Root className={styles.dataList}>
          <DataList.Item>
            <DataList.Label>Program</DataList.Label>
            <DataList.Value>
              <RadixLink asChild>
                <Link href={{ pathname: programRoute }}>
                  {caseDetail.program?.name || program?.name}
                </Link>
              </RadixLink>
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Created</DataList.Label>
            <DataList.Value>
              {new Date(caseDetail.createdAt).toLocaleDateString() || 'None'}
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Case status</DataList.Label>
            <DataList.Value>{formatCaseStatus(caseDetail?.status)}</DataList.Value>
          </DataList.Item>
          {checkFeature(program?.features, FeatureName.ApplicationScoring) && needLevelTier ? (
            <DataList.Item>
              <DataList.Label>Need Level | Tier</DataList.Label>
              <DataList.Value>{needLevelTier}</DataList.Value>
            </DataList.Item>
          ) : null}
          <DataList.Item>
            <DataList.Label>Case ID</DataList.Label>
            <DataList.Value>
              {' '}
              <CopyToClipboard.Root>
                <CopyToClipboard.Content>{caseDetail?.displayId}</CopyToClipboard.Content>
                <CopyToClipboard.Trigger text={caseDetail?.displayId} contentType="Case_Id" />
              </CopyToClipboard.Root>
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Assignee</DataList.Label>
            <DataList.Value>{caseDetail?.assignee?.user.name || 'None'}</DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Expedited</DataList.Label>
            <DataList.Value>
              {EXPEDITED_PRIORITIES.has(caseDetail.priority) ? (
                <Flex gap="2" align="center">
                  Yes
                  <Text color="red" size="2">
                    <FlagIcon fontSize="inherit" />
                  </Text>
                </Flex>
              ) : (
                'No'
              )}
            </DataList.Value>
          </DataList.Item>
          <DataList.Item>
            <DataList.Label>Submissions</DataList.Label>
            <DataList.Value>{application?.versions?.length || 0}</DataList.Value>
          </DataList.Item>
          {currentVersion ? (
            <DataList.Item>
              <DataList.Label>Latest Submission</DataList.Label>
              <DataList.Value>
                <Tooltip content="View and edit latest application">
                  <RadixLink asChild>
                    <Link
                      href={{ pathname: applicationRoute }}
                      color="blue"
                      target="_blank"
                      className={styles.applicationLink}
                      aria-label="Latest Application Submission"
                    >
                      {new Date(currentVersion?.createdAt).toLocaleString() || 0}
                    </Link>
                  </RadixLink>
                </Tooltip>
              </DataList.Value>
            </DataList.Item>
          ) : null}
          {isMultiParty ? (
            <DataList.Item>
              <DataList.Label>Case Participants</DataList.Label>
              <DataList.Value>
                <Flex direction="column" gap="1">
                  {caseDetail?.applications?.map((participantApplication) => {
                    const applicantType =
                      participantApplication.submitter.applicantProfile?.applicantType;

                    const programApplicantType = program.applicantTypes?.find((type) => {
                      return type.applicantType.id === applicantType?.id;
                    });

                    return (
                      <div key={participantApplication.id}>
                        <Tooltip content={formatSnakeToTitleCase(applicantType?.role)}>
                          <Link
                            href={{
                              pathname: makeRoute(PORTAL_ROUTES.APPLICATION_REVIEW, {
                                caseId: caseDetail.id,
                                applicationId: application?.id || '',
                                programId: program.id,
                              }),
                              hash: participantApplication.displayId,
                            }}
                          >
                            <Badge
                              color={
                                applicantType?.role === ApplicantTypeRole.FirstParty
                                  ? 'blue'
                                  : 'gray'
                              }
                            >
                              {programApplicantType?.nameOverride ||
                                applicantType?.name ||
                                'Unknown participant type'}
                            </Badge>
                          </Link>
                        </Tooltip>
                      </div>
                    );
                  })}
                </Flex>
              </DataList.Value>
            </DataList.Item>
          ) : null}
          {!!applicationVerification && (
            <DataList.Item>
              <DataList.Label>Verification status</DataList.Label>
              <DataList.Value>
                <VerificationStatusComponent
                  verification={applicationVerification}
                  showDialog={true}
                />
              </DataList.Value>
            </DataList.Item>
          )}
        </DataList.Root>
      )}
    </div>
  );
};

export default CaseWorkflowDetails;
