import { BENEFIT_DISPLAY } from '@/app/_utils/benefits';
import Loading from '@/spa-legacy/common/components/Loading';
import Typography from '@/spa-legacy/common/components/Typography';
import Color from '@/spa-legacy/common/utilities/Color';
import DetailSection from '@/spa-legacy/portal/components/DetailSection';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { BenefitType, type Case, FeatureName, ScheduleType } from '@bybeam/platform-types';
import useHistoricalApplications from '../../hooks/useHistoricalApplications';
import FulfillmentsPaymentHistory from './FulfillmentsPaymentHistory';
import PaymentHistoryTable from './PaymentHistoryTable';

export default function PaymentHistorySection({ fulfillments, applications, program }: Case) {
  const {
    applications: historicalApplications,
    loading,
    error,
  } = useHistoricalApplications({
    applications: applications ?? [],
    programId: program.id,
  });
  const core = checkFeature(program?.features, FeatureName.WorkflowCore);
  const recurringFulfillments = fulfillments?.filter(
    (fulfillment) => fulfillment.scheduleType === ScheduleType.Recurring,
  );
  const recurring =
    checkFeature(program?.features, FeatureName.PaymentsRecurring) &&
    !!recurringFulfillments?.length;

  const display = !core || recurring || historicalApplications?.length > 0;
  if (!display) return null;

  if (recurring)
    return (
      <DetailSection title={`${program.name} Payment Overview`}>
        <FulfillmentsPaymentHistory fulfillments={recurringFulfillments} />
      </DetailSection>
    );

  const PaymentHistoryContent = ({ benefit }: { benefit?: BenefitType }): JSX.Element => {
    if (loading) return <Loading size="XL" fullPage />;
    if (error)
      return (
        <Typography variant="body" color={Color.Error}>
          Something went wrong!
        </Typography>
      );
    return (
      <PaymentHistoryTable
        benefit={benefit}
        applications={historicalApplications}
        program={program}
      />
    );
  };

  if (core)
    return (
      <DetailSection title="Payment History">
        <PaymentHistoryContent />
      </DetailSection>
    );

  return (
    <div className="flex flex-col gap-1">
      {Object.values(BenefitType).map((benefit) => (
        <DetailSection key={benefit} title={`${BENEFIT_DISPLAY[benefit]} Payment History`}>
          <PaymentHistoryContent benefit={benefit} />
        </DetailSection>
      ))}
    </div>
  );
}
