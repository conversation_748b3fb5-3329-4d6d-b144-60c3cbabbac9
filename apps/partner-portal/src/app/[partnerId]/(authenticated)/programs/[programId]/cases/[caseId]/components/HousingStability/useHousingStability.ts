import { useQuery } from '@apollo/client';
import type { Case, Outcome, Service } from '@bybeam/platform-types';
import { useParams } from 'next/navigation';
import GetOutcomesAndServices from './graphql/GetOutcomesAndServicesQuery.graphql';

export enum HousingType {
  Outcome = 'Outcome',
  Service = 'Service',
}

export interface ServiceInput {
  id?: string;
  options: string[];
  date: string;
}

type ServiceOutcomeQueryResponse = {
  programs: { programs: [{ outcomes: Outcome[]; services: Service[] }] };
};

export interface UseHousingStabilityResponse {
  queryResponse: ServiceOutcomeQueryResponse;
  enrollment: {
    startDateEnrollment: string;
    endDateEnrollment: string;
  };
  services: {
    [housingType in HousingType]: ServiceInput[];
  };
}

export default function useHousingStability(case_: Case): UseHousingStabilityResponse {
  const { programId } = useParams<{ programId: string }>();
  const application = case_.applications[0];

  const enrollment = {
    startDateEnrollment:
      application?.submitter?.enrollments?.length > 0 &&
      application?.submitter?.enrollments[0]?.startDate
        ? application?.submitter?.enrollments[0]?.startDate?.toString()
        : '',
    endDateEnrollment:
      application?.submitter?.enrollments?.length > 0 &&
      application?.submitter?.enrollments[0]?.endDate
        ? application?.submitter?.enrollments[0]?.endDate?.toString()
        : '',
  };

  const servicesData = [
    [
      HousingType.Outcome,
      (application?.submitter?.enrollments?.length > 0 &&
        application?.submitter?.enrollments[0]?.enrollmentOutcomes?.map((outcome) => {
          return {
            id: outcome.id,
            options: outcome.outcomes.map((opt) => opt.id),
            date: outcome.outcomeDate,
          };
        })) ||
        [],
    ],
    [
      HousingType.Service,
      (application?.submitter?.enrollments?.length > 0 &&
        application?.submitter?.enrollments[0]?.enrollmentServices?.map((service) => {
          return {
            id: service.id,
            options: service.services.map((opt) => opt.id),
            date: service.serviceDate,
          };
        })) ||
        [],
    ],
  ];

  const services = Object.fromEntries(servicesData) as {
    [housingType in HousingType]: ServiceInput[];
  };

  const { data: queryResponse } = useQuery<ServiceOutcomeQueryResponse>(GetOutcomesAndServices, {
    variables: {
      programId,
    },
  });

  return {
    queryResponse,
    enrollment,
    services,
  };
}
