'use client';
import { useUserHomeRoute } from '@/app/hooks/useUserHomeRoute';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import useCustomPageTitle from '@/spa-legacy/common/hooks/useCustomPageTitle';
import ProgramApplicationsTable from '@/spa-legacy/portal/components/ProgramApplicationsTable';
import { Route } from 'next';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import styles from './page.module.css';

export default function ProgramViewPage() {
  const program = useProgram();
  const router = useRouter();
  useCustomPageTitle(`${program?.name} Applications`);
  const defaultRoute = useUserHomeRoute();

  // Redirect in an effect to avoid navigation during render
  useEffect(() => {
    if (!program) {
      router.replace(defaultRoute as Route);
    }
  }, [program, defaultRoute, router]);

  if (!program)
    return (
      <main className={styles.programDetailsCasesTable}>
        <span role="status" aria-live="polite">
          Redirecting…
        </span>
      </main>
    );

  return (
    <main className={styles.programDetailsCasesTable}>
      <ProgramApplicationsTable />
    </main>
  );
}
