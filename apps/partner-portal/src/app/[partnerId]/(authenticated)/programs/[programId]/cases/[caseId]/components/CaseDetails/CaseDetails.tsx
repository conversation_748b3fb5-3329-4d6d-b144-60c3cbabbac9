import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Notes from '@/spa-legacy/portal/components/Notes';
import PaymentModal from '@/spa-legacy/portal/components/PaymentModal';
import { CaseContext } from '@/spa-legacy/portal/hooks/useCase';
import { useQuery } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { ApplicantTypeRole, type Case, FeatureName, type Program } from '@bybeam/platform-types';
import { ExclamationTriangleIcon } from '@radix-ui/react-icons';
import { Callout, Flex, Separator } from '@radix-ui/themes';
import { useParams } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { useEffect, useState } from 'react';
import {
  ActivityFeed,
  CaseActions,
  CaseParticipants,
  DocumentsSection,
  HousingStability,
} from '../';
import RequestAccessButton from '../RequestAccessButton';
import ReviewFields from '../ReviewFields/ReviewFieldsSection';
import GetCaseQuery from './CaseDetailsQuery.graphql';

interface CaseDetailsProps {
  program: Program;
}

export enum OpenModal {
  None = '',
  LabelFeedback = 'feedback',
  Payment = 'payment',
}

export default function CaseDetails({ program }: CaseDetailsProps) {
  const partner = usePartner();
  const { caseId } = useParams<{ caseId: string }>();
  const postHog = usePostHog();

  const doctopusEnabled = checkFeature(partner?.features, FeatureName.DocumentsClassify);
  const partnerIssuedPaymentsEnabled = checkFeature(
    program?.features,
    FeatureName.PaymentsPartnerIssued,
  );

  const { data, loading, error } = useQuery<
    { cases: { cases: Case[] } },
    { id: string; bankAccount: boolean; documentSummary: boolean }
  >(GetCaseQuery, {
    variables: {
      id: caseId,
      bankAccount: partnerIssuedPaymentsEnabled,
      documentSummary: doctopusEnabled,
    },
  });

  const { showSnackbar } = useSnackbar();
  const [openModal, setOpenModal] = useState(OpenModal.None);
  const closeModal = (): void => setOpenModal(OpenModal.None);

  useEffect(() => {
    if (error) showSnackbar('Something went wrong. Please refresh and try again.');
  }, [error, showSnackbar]);

  const case_ = data?.cases.cases[0];

  if (loading) return <LoadingComponent />;
  if (error || !case_)
    return (
      <Callout.Root color="red">
        <Callout.Icon>
          <ExclamationTriangleIcon />
        </Callout.Icon>
        <Callout.Text>Failed to load case details. Please refresh and try again.</Callout.Text>
      </Callout.Root>
    );

  // TODO: Make this more declaritive in the API response so that it doesn't need to be parsed here.
  // or move this to a utility function in follow up tickets.
  const firstPartyApplication = case_.applications?.find(
    (app) => app.submitter?.applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty,
  );
  const application = firstPartyApplication ?? case_.applications?.[0];

  const fulfillment = case_.fulfillments[0];

  if (!application) {
    return <>Failed to load application details for this case.</>;
  }

  return (
    <CaseContext.Provider value={case_}>
      {openModal === OpenModal.Payment && (
        <PaymentModal onClose={closeModal} fulfillment={fulfillment} case={case_} />
      )}
      <Flex justify="end" align="center" mb="3">
        <Flex gap="2" align="center">
          <RequestAccessButton caseId={case_.id} />
          <CaseActions case={case_} setOpenInitModal={setOpenModal} />
        </Flex>
      </Flex>
      <Flex direction="column" width="100%" gap="3">
        <CaseParticipants case={case_} />
        {postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.applicationReviewFields) &&
        firstPartyApplication ? (
          <ReviewFields application={firstPartyApplication} />
        ) : null}
        <DocumentsSection caseId={case_.id} />
        {checkFeature(program.features, FeatureName.WorkflowServicesAndOutcomes) && (
          <HousingStability {...case_} />
        )}
        <Notes relation={{ id: caseId, type: 'case' }} notes={case_.notes} />
        {case_ ? (
          <>
            <Separator size="4" />
            <ActivityFeed workflowEvents={case_.workflowEvents} />
          </>
        ) : null}
      </Flex>
    </CaseContext.Provider>
  );
}
