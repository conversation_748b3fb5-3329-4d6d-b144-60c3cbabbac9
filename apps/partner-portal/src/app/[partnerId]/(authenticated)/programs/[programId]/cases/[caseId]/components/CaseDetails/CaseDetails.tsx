import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import Loading from '@/spa-legacy/common/components/Loading';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Color from '@/spa-legacy/common/utilities/Color';
import Notes from '@/spa-legacy/portal/components/Notes';
import PaymentModal from '@/spa-legacy/portal/components/PaymentModal';
import { CaseContext } from '@/spa-legacy/portal/hooks/useCase';
import { programHasPayments } from '@/spa-legacy/utilities/programs';
import { useQuery } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { ApplicantTypeRole, type Case, FeatureName, type Program } from '@bybeam/platform-types';
import { Flex } from '@radix-ui/themes';
import { useParams } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { useEffect, useState } from 'react';
import {
  ActivityFeed,
  CaseActions,
  CaseParticipants,
  DocumentsSection,
  HousingStability,
  PaymentHistorySection,
  RequestSummary,
} from '../';
import RequestAccessButton from '../RequestAccessButton';
import ReviewFields from '../ReviewFields/ReviewFieldsSection';
import GetCaseQuery from './CaseDetailsQuery.graphql';

interface CaseDetailsProps {
  program: Program;
}

export enum OpenModal {
  None = '',
  LabelFeedback = 'feedback',
  Payment = 'payment',
}

export default function CaseDetails({ program }: CaseDetailsProps) {
  const partner = usePartner();
  const { caseId } = useParams<{ caseId: string }>();
  const postHog = usePostHog();

  const doctopusEnabled = checkFeature(partner?.features, FeatureName.DocumentsClassify);
  const partnerIssuedPaymentsEnabled = checkFeature(
    program?.features,
    FeatureName.PaymentsPartnerIssued,
  );

  const { data, loading, error } = useQuery<
    { cases: { cases: Case[] } },
    { id: string; bankAccount: boolean; documentSummary: boolean }
  >(GetCaseQuery, {
    variables: {
      id: caseId,
      bankAccount: partnerIssuedPaymentsEnabled,
      documentSummary: doctopusEnabled,
    },
  });

  const { showSnackbar } = useSnackbar();
  const [openModal, setOpenModal] = useState(OpenModal.None);
  const closeModal = (): void => setOpenModal(OpenModal.None);

  useEffect(() => {
    if (error) showSnackbar('Something went wrong. Please refresh and try again.');
  }, [error, showSnackbar]);

  const case_ = data?.cases.cases[0];

  if (loading) return <Loading size="XXL" fullPage />;
  if (error || !case_)
    return (
      <Typography variant="body" color={Color.Error}>
        Something went wrong
      </Typography>
    );

  // TODO: Make this more declaritive in the API response so that it doesn't need to be parsed here.
  // or move this to a utility function in follow up tickets.
  const firstPartyApplication = case_.applications?.find(
    (app) => app.submitter?.applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty,
  );
  const application = firstPartyApplication ?? case_.applications?.[0];

  const fulfillment = case_.fulfillments[0];

  if (!application) {
    return <>Failed to load application details for this case.</>;
  }

  return (
    <CaseContext.Provider value={case_}>
      {openModal === OpenModal.Payment && (
        <PaymentModal onClose={closeModal} fulfillment={fulfillment} case={case_} />
      )}
      <Flex justify="end" align="center" mb="3">
        <Flex gap="2" align="center">
          <RequestAccessButton caseId={case_.id} />
          <CaseActions case={case_} setOpenInitModal={setOpenModal} />
        </Flex>
      </Flex>
      <Flex direction="column" width="100%" gap="3">
        <CaseParticipants case={case_} />
        {!postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.casePaymentsPage) && (
          <>
            {programHasPayments(program) && <RequestSummary />}
            {programHasPayments(program) && <PaymentHistorySection {...case_} program={program} />}
          </>
        )}
        {postHog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.applicationReviewFields) &&
        firstPartyApplication ? (
          <ReviewFields application={firstPartyApplication} />
        ) : null}
        <DocumentsSection caseId={case_.id} />
        {checkFeature(program.features, FeatureName.WorkflowServicesAndOutcomes) && (
          <HousingStability {...case_} />
        )}
        <Notes relation={{ id: caseId, type: 'case' }} notes={case_.notes} />
        {case_ ? (
          <>
            <hr className="border-tableBorder mb-1" />
            <ActivityFeed workflowEvents={case_.workflowEvents} />
          </>
        ) : null}
      </Flex>
    </CaseContext.Provider>
  );
}
