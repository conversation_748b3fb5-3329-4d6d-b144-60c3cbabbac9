'use client';
import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';
import { checkPartnerHasFeature } from '@/app/_utils/checkFeature';
import { Role, hasRole } from '@/app/_utils/roles';
import CanAccess from '@/app/components/features/CanAccess';
import { VerificationStatus as VerificationStatusComponent } from '@/app/components/features/Verification';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import Page from '@/app/components/ui/Page/Page';
import SidesheetToggle from '@/app/components/ui/Page/Parts/SidesheetToggle';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { useNextAuth } from '@/app/providers/NextAuthProvider';
import { useSidesheet } from '@/app/providers/SidesheetProvider';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import EditApplicantModal from '@/spa-legacy/portal/components/EditApplicantModal';
import { useMutation, useQuery } from '@apollo/client';
import sort from '@bybeam/platform-lib/utilities/sort';
import {
  ApplicantTypeRole,
  Case,
  CaseStatus,
  EXPEDITED_PRIORITIES,
  FeatureName,
} from '@bybeam/platform-types';
import { Flag as FlagIcon } from '@mui/icons-material';
import { Box, Button, Callout, Dialog, Flex, Text, Tooltip } from '@radix-ui/themes';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useFeatureFlagEnabled, usePostHog } from 'posthog-js/react';
import { useState } from 'react';
import {
  ApplicantDetails,
  CaseNavigation,
  CaseWorkflowDetails,
  Eligibility,
  TagManager,
} from './components';
import EvaluateEligibility from './components/Eligibility/EvaluateEligibility.graphql';
import styles from './layout.module.css';
import GetCaseDetail from './pageQuery.graphql';

export default function CaseDetailLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { caseId } = useParams<{ programId: string; caseId: string }>();
  const { isSidesheetOpen } = useSidesheet({ open: true });
  const program = useProgram();
  const partner = usePartner();
  const posthog = usePostHog();
  const showEligibility = useFeatureFlagEnabled(POSTHOG_FEATURE_FLAGS.rulesEngine);
  const includeRecentLogin =
    checkPartnerHasFeature(partner, FeatureName.UserValidation) &&
    useFeatureFlagEnabled(POSTHOG_FEATURE_FLAGS.recentLoginDetails);
  const { roles } = useNextAuth();

  const { data, loading, error, refetch } = useQuery<
    { cases: { cases: Case[] } },
    { id: string; includeVerification: boolean; includeRecentLogin: boolean }
  >(GetCaseDetail, {
    variables: {
      id: caseId,
      includeVerification: true,
      includeRecentLogin: includeRecentLogin ?? false,
    },
  });

  // IMPORTANT: Call all hooks unconditionally before any early returns to keep hook order stable
  // across renders. This prevents "Rendered more hooks than during the previous render" errors.
  const [mutate, evalState] = useMutation(EvaluateEligibility);
  const [evalError, setEvalError] = useState<string | null>(null);

  if (loading) {
    return <LoadingComponent />;
  }

  if (error) {
    return <>Failed to load case details.</>;
  }

  if (!program) {
    return <>Failed to load program details</>;
  }

  const caseDetail = data?.cases?.cases[0];

  if (!caseDetail) {
    return <>Failed to load case details.</>;
  }

  // TODO: Make this more declaritive in the API response so that it doesn't need to be parsed here.
  // or move this to a utility function in follow up tickets.
  const primaryApplication =
    caseDetail.applications?.find(
      (app) =>
        app.submitter?.applicantProfile?.applicantType?.role === ApplicantTypeRole.FirstParty,
    ) ?? caseDetail.applications?.[0];

  const { id: applicationId, submitter: applicant } = primaryApplication || {};

  // Eligibility button visibility: partner admins only
  const isPartnerAdmin = hasRole(Role.Manager, roles);

  // Enable only when: application submitted, case in review or ready-for-review, payment not sent, case open
  const isSubmitted = !!primaryApplication?.submittedAt;
  const isInReviewOrReady =
    caseDetail?.status === CaseStatus.InReview || caseDetail?.status === CaseStatus.ReadyForReview;
  const paymentNotSent = caseDetail?.status !== CaseStatus.PaymentSent;
  const isOpenCase =
    !!caseDetail &&
    ![
      CaseStatus.Denied,
      CaseStatus.Withdrawn,
      CaseStatus.Archived,
      CaseStatus.Approved,
      CaseStatus.PaymentSent,
    ].includes(caseDetail.status);

  const canEvaluate = Boolean(isSubmitted && isInReviewOrReady && paymentNotSent && isOpenCase);
  const onConfirmEvaluate = async () => {
    if (!applicationId) return;
    setEvalError(null);
    try {
      const { data } = await mutate({ variables: { input: { id: applicationId } } });
      const meta = data?.application?.evaluateEligibility?.metadata;
      const status = typeof meta?.status === 'number' ? meta.status : Number(meta?.status);
      if (!meta || !status || status >= 400) {
        setEvalError(meta?.message || 'Failed to evaluate eligibility');
        return;
      }
      await refetch();
    } catch (err) {
      // Surface a user-visible error and avoid refetch on failure
      console.error('Failed to evaluate eligibility', err);
      const message = err instanceof Error ? err.message : 'Failed to evaluate eligibility';
      setEvalError(message);
    }
  };

  if (!applicant) {
    return <>Failed to load applicant details.</>;
  }

  // Get the latest application's verification data for the header
  const applicationVerification = caseDetail.applications
    ? sort(caseDetail.applications, {
        accessor: (app) => app.createdAt,
        ascending: false,
      })[0]?.verification
    : undefined;

  const hasConfidence =
    applicationVerification?.confidence !== undefined &&
    applicationVerification?.confidence !== null;
  const showVerificationIcon = hasConfidence;

  const programRoute = makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, {
    programId: caseDetail?.program?.id || '',
  });

  return (
    <Page>
      <Page.Header>
        <Page.Breadcrumbs>
          <Page.Breadcrumbs.Crumb>
            <Link href={{ pathname: programRoute }}>
              <Flex minWidth="0" maxWidth="8em">
                <Tooltip content={caseDetail?.program?.name}>
                  <Text truncate>{caseDetail?.program?.name}</Text>
                </Tooltip>
              </Flex>
            </Link>
          </Page.Breadcrumbs.Crumb>
          <Page.Breadcrumbs.Crumb>
            {caseDetail?.displayId} • {applicant?.name}
            {showVerificationIcon && applicationVerification && (
              <Box display="inline" ml="1">
                <VerificationStatusComponent
                  variant="icon-only"
                  verification={applicationVerification}
                />
              </Box>
            )}
            {EXPEDITED_PRIORITIES.has(caseDetail?.priority) && (
              <Tooltip content="This case is expedited.">
                <Box display="inline" ml="1">
                  <Text color="red" size="2">
                    <FlagIcon fontSize="inherit" />
                  </Text>
                </Box>
              </Tooltip>
            )}
          </Page.Breadcrumbs.Crumb>
        </Page.Breadcrumbs>
        <SidesheetToggle />
      </Page.Header>
      <Page.Content className={styles.caseDetailsPageContent}>
        <div className={styles.caseDetailsMain}>
          <CaseNavigation applicationId={applicationId} />
          <main>{children}</main>
        </div>
        {isSidesheetOpen ? (
          <aside className={styles.sidesheet}>
            <div className={styles.sidesheetSection}>
              <div className={styles.sidesheetSectionTitle}>
                <strong>Case Workflow Details</strong>
              </div>
              <CaseWorkflowDetails caseDetail={caseDetail} />
            </div>
            <div className={styles.sidesheetSection}>
              <Flex
                justify="between"
                align="center"
                gap="1"
                className={styles.sidesheetSectionTitle}
              >
                <strong>Applicant details</strong>
                {applicant ? (
                  <CanAccess>
                    <EditApplicantModal
                      applicant={applicant}
                      dialogTrigger={
                        <Button variant="outline" size="1">
                          Edit
                        </Button>
                      }
                    />
                  </CanAccess>
                ) : null}
              </Flex>
              <ApplicantDetails applicant={applicant} />
            </div>
            {showEligibility ? (
              <div className={styles.sidesheetSection}>
                <Flex
                  justify="between"
                  align="center"
                  gap="1"
                  className={styles.sidesheetSectionTitle}
                >
                  <strong>Rules</strong>
                  {isPartnerAdmin ? (
                    <Dialog.Root>
                      <Dialog.Trigger>
                        <Button
                          variant="outline"
                          size="1"
                          disabled={!canEvaluate || evalState.loading}
                        >
                          {evalState.loading ? 'Evaluating…' : 'Evaluate'}
                        </Button>
                      </Dialog.Trigger>
                      <Dialog.Content maxWidth="35rem">
                        <Dialog.Title>Run rules evaluation?</Dialog.Title>
                        <Dialog.Description>
                          <Text size="2">
                            Evaluate the latest application version for this case using the latest
                            ruleset. If an evaluation exists, it will be overwritten with the new
                            result.
                          </Text>
                        </Dialog.Description>
                        <Flex mt="4" gap="3" justify="end">
                          <Dialog.Close>
                            <Button variant="soft">Cancel</Button>
                          </Dialog.Close>
                          <Dialog.Close>
                            <Button
                              onClick={onConfirmEvaluate}
                              disabled={!canEvaluate || evalState.loading}
                            >
                              {evalState.loading ? 'Evaluating…' : 'Evaluate'}
                            </Button>
                          </Dialog.Close>
                        </Flex>
                      </Dialog.Content>
                    </Dialog.Root>
                  ) : null}
                </Flex>
                {evalError && (
                  <Callout.Root color="red" highContrast role="alert">
                    <Callout.Text>{evalError}</Callout.Text>
                  </Callout.Root>
                )}
                <Eligibility caseDetail={caseDetail} />
              </div>
            ) : null}
            {caseDetail && (
              <div className={styles.sidesheetSection}>
                <div className={styles.sidesheetSectionTitle}>
                  <strong>Tags</strong>
                </div>
                <TagManager case_={caseDetail} />
              </div>
            )}
          </aside>
        ) : null}
      </Page.Content>
    </Page>
  );
}
