import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName, Program } from '@bybeam/platform-types';
import { Cross1Icon } from '@radix-ui/react-icons';
import { Button, Dialog, Flex, IconButton } from '@radix-ui/themes';
import { ReactElement, useState } from 'react';
import { PaymentStatusDetails } from './PaymentStatusDetails';

export default function PaymentStatusCell({
  displayValue,
  caseId,
  program,
  canViewDetails,
}: {
  displayValue: string;
  caseId: string;
  program: Program;
  canViewDetails: boolean;
}): ReactElement {
  const [open, setIsPaymentDialogOpen] = useState(false);
  const isMultiPaymentEnabled =
    checkFeature(program?.features, FeatureName.PaymentsMultiPayment) &&
    checkFeature(program?.features, FeatureName.PaymentsPartnerIssued);

  return isMultiPaymentEnabled && canViewDetails ? (
    <>
      <Dialog.Root open={open} onOpenChange={setIsPaymentDialogOpen}>
        <Dialog.Trigger>
          <Button variant="ghost">{displayValue}</Button>
        </Dialog.Trigger>
        <Dialog.Content size="4" maxWidth="560px">
          <Flex justify="between" gap="1">
            <Dialog.Title>Payment Status Summary</Dialog.Title>
            <Dialog.Close>
              <IconButton variant="ghost" color="gray">
                <Cross1Icon />
              </IconButton>
            </Dialog.Close>
          </Flex>
          <Dialog.Description mb="4">Payment Status: {displayValue}</Dialog.Description>
          {open && <PaymentStatusDetails caseId={caseId} />}
          <Flex mt="4" justify="start">
            <Dialog.Close>
              <Button>Exit</Button>
            </Dialog.Close>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </>
  ) : (
    <>{displayValue}</>
  );
}
