import { checkProgramHasAnyFeature } from '@/app/_utils/checkFeature';
import { areCasePaymentsInitiated, canInitiatePayment } from '@/app/_utils/payments';
import { Permission } from '@/app/_utils/roles';
import { useCanAccess } from '@/app/providers/CanAccessProvider';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import useTransitionStatus from '@/spa-legacy/portal/hooks/useTransitionStatus';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { Case, CaseStatus, FeatureName, Fulfillment, Program } from '@bybeam/platform-types';
import { Cross2Icon } from '@radix-ui/react-icons';
import { Button, Dialog, Flex, IconButton, Tooltip } from '@radix-ui/themes';
import { useState } from 'react';

export enum PaymentAction {
  InitiatePayment = 'InitiatePayment',
  MarkAsDone = 'MarkAsDone',
}

const STATUS_ACTIONS: { [status in CaseStatus | string]: PaymentAction[] } = {
  [CaseStatus.ReadyForReview]: [PaymentAction.InitiatePayment],
  [CaseStatus.InReview]: [PaymentAction.InitiatePayment],
  [CaseStatus.Approved]: [PaymentAction.InitiatePayment, PaymentAction.MarkAsDone],
};

export default function PaymentActions({
  case: case_,
  program,
  initiatePayment,
}: { case: Case; program: Program; initiatePayment: (fulfillment: Fulfillment) => void }) {
  const { showSnackbar } = useSnackbar();
  const [openMarkAsDoneDialog, setOpenMarkAsDoneDialog] = useState(false);

  const { transitionStatus, loading: isTransitionLoading } = useTransitionStatus(case_.id);

  const { canAccess: canAccessPayments } = useCanAccess({
    resource: { objectType: 'PROGRAM', objectId: program?.id },
    permission: Permission.Fiscal,
  });
  const isCaseAction = (action: PaymentAction) => STATUS_ACTIONS[case_.status]?.includes(action);

  // program features
  const isPaymentEnabled = checkProgramHasAnyFeature(program, [
    FeatureName.PaymentsClaimFunds,
    FeatureName.PaymentsPartnerIssued,
  ]);
  const isMultiPayment = checkFeature(program?.features, FeatureName.PaymentsMultiPayment);

  if (!canAccessPayments || !isPaymentEnabled || !case_?.fulfillments?.length) return null;

  const canInitiateFirstPayment = canInitiatePayment(case_, program);
  const allPaymentsInitiated = areCasePaymentsInitiated(case_);

  const doTransitionStatus = async (): Promise<void> => {
    const success = await transitionStatus({ status: CaseStatus.PaymentSent });
    if (success) {
      showSnackbar('Case successfully marked as done.');
      setOpenMarkAsDoneDialog(false);
    } else {
      showSnackbar('Something went wrong!');
    }
  };

  return (
    <Flex gap="4" align="center">
      {isCaseAction(PaymentAction.InitiatePayment) && !isMultiPayment && (
        <Tooltip
          content={
            !canInitiateFirstPayment
              ? 'Please confirm the information below by clicking the Save button in the Payment Details section.'
              : 'Initiate Payment'
          }
        >
          <Button
            disabled={!canInitiateFirstPayment}
            onClick={(): void => initiatePayment(case_?.fulfillments?.[0] as Fulfillment)}
          >
            Initiate Payment
          </Button>
        </Tooltip>
      )}
      <Dialog.Root onOpenChange={setOpenMarkAsDoneDialog} open={openMarkAsDoneDialog}>
        {isCaseAction(PaymentAction.MarkAsDone) && isMultiPayment && (
          <Tooltip
            content={
              !allPaymentsInitiated
                ? 'This button will be enabled once all payments have been successfully initiated.'
                : 'Mark As Done'
            }
          >
            <Dialog.Trigger>
              <Button disabled={!allPaymentsInitiated}>Mark As Done</Button>
            </Dialog.Trigger>
          </Tooltip>
        )}
        <Dialog.Content size="3" maxWidth="35rem">
          <Flex justify="end">
            <Dialog.Close disabled={isTransitionLoading}>
              <IconButton variant="ghost" aria-label="close dialog">
                <Cross2Icon />
              </IconButton>
            </Dialog.Close>
          </Flex>
          <Dialog.Title mb="4">Mark as Done?</Dialog.Title>
          <Dialog.Description>
            Marking this case as done will prevent any future payments from being added. The case
            status will be automatically updated, and this action cannot be undone.
          </Dialog.Description>
          <Flex mt="4" gap="3" justify="end">
            <Dialog.Close disabled={isTransitionLoading}>
              <Button variant="outline">Cancel</Button>
            </Dialog.Close>
            <Button
              loading={isTransitionLoading}
              onClick={(): Promise<void> => {
                return doTransitionStatus();
              }}
            >
              Mark as Done
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </Flex>
  );
}
