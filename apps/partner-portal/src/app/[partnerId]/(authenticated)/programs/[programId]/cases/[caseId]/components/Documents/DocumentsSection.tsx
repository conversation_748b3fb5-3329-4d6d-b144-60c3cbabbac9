import { NameMatchingIndicator } from '@/app/components/features/NameMatching';
import type {
  ApplicantNameConsistency,
  DocumentConsistency,
} from '@/app/components/features/NameMatching';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Documents from '@/spa-legacy/portal/components/Documents';
import { LabelFeedbackModal } from '@/spa-legacy/portal/components/LabelFeedbackModal';
import PinDocumentMutation from '@/spa-legacy/portal/graphql/mutations/PinDocumentMutation.graphql';
import useSubmitFeedback from '@/spa-legacy/portal/hooks/useSubmitFeedback';
import { useMutation, useQuery } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import {
  type Case,
  type Document,
  FeatureName,
  type MutationResponse,
  YesNoUnsure,
} from '@bybeam/platform-types';
import { useFeatureFlagEnabled } from 'posthog-js/react';
import { useEffect, useState } from 'react';
import GetApplicantNameConsistency from './graphql/GetApplicantNameConsistencyQuery.graphql';
import GetCaseDocuments from './graphql/GetCaseDocumentsQuery.graphql';
import GetDocPredictions from './graphql/GetDocumentPredictionsQuery.graphql';
import RemoveCaseDocumentMutation from './graphql/RemoveCaseDocumentMutation.graphql';
import UploadCaseDocumentMutation from './graphql/UploadCaseDocumentMutation.graphql';
import { useCaseDocuments } from './useCaseDocuments';

interface DocumentsSectionProps {
  caseId: string;
}

const POLL_INTERVAL = 1000 * 5;
const ENTITY_RESOLUTION_REFETCH_DELAY_MS = 3000; // Initial delay before first retry
const ENTITY_RESOLUTION_RETRY_INTERVAL_MS = 2000; // Interval between retries
const ENTITY_RESOLUTION_MAX_RETRIES = 3; // Maximum number of retry attempts

export default function DocumentsSection({ caseId: id }: DocumentsSectionProps): JSX.Element {
  const partner = usePartner();
  const program = useProgram();
  const doctopusEnabled = checkFeature(partner.features, FeatureName.DocumentsClassify);

  // Name matching feature flags
  const nameMatchingFlagEnabled = useFeatureFlagEnabled(
    POSTHOG_FEATURE_FLAGS.nameMatchingDriversLicense,
  );
  const nameMatchingConfigEnabled = program?.config?.nameMatchingEnabled ?? false;
  const nameMatchingEnabled = nameMatchingFlagEnabled && nameMatchingConfigEnabled;

  const { data, loading } = useQuery<
    { cases: { cases: Case[] } },
    { id: string; documentSummary: boolean }
  >(GetCaseDocuments, { variables: { id, documentSummary: doctopusEnabled } });

  // Fetch resolved entities for name matching (only if feature is enabled)
  const { data: resolvedEntitiesData, refetch: refetchResolvedEntities } = useQuery<
    {
      case: {
        id: string;
        resolvedEntities: {
          caseId: string;
          applicantNameConsistency: ApplicantNameConsistency;
        };
      };
    },
    { caseId: string }
  >(GetApplicantNameConsistency, {
    variables: { caseId: id },
    skip: !nameMatchingEnabled,
  });

  const {
    data: predictionsData,
    startPolling,
    stopPolling,
  } = useQuery<{ cases: { cases: Case[] } }, { id: string }>(GetDocPredictions, {
    variables: { id },
    skip: !doctopusEnabled,
  });

  const [uploadDocument, { loading: uploadLoading }] = useMutation<
    { case: { uploadDocuments: MutationResponse<Case> } },
    { id: string; files: File[] }
  >(UploadCaseDocumentMutation);
  const [removeDocument, { loading: removeLoading }] = useMutation<
    { case: { removeDocuments: MutationResponse<Case> } },
    { id: string; documentId: string }
  >(RemoveCaseDocumentMutation);
  const [pinDocument] = useMutation<
    { document: { pinDocument: MutationResponse<Document> } },
    { documentId: string; pinned: boolean }
  >(PinDocumentMutation);

  const { showSnackbar } = useSnackbar();
  const doUpload = async (files: File[]): Promise<void> => {
    const {
      data: {
        case: {
          uploadDocuments: { metadata },
        },
      },
    } = await uploadDocument({ variables: { id, files } });
    if (doctopusEnabled) {
      startPolling(POLL_INTERVAL);
    }
    if (metadata.status < 400) return;

    showSnackbar('Failed to upload document. Please refresh and try again');
  };

  const doRemove = async (documentId: string): Promise<void> => {
    const {
      data: {
        case: {
          removeDocuments: { metadata },
        },
      },
    } = await removeDocument({ variables: { id, documentId } });
    if (metadata.status < 400) return;

    showSnackbar('Failed to remove document. Please refresh and try again');
  };

  const doPin = async (documentId: string, pinned: boolean): Promise<void> => {
    const {
      data: {
        document: {
          pinDocument: { metadata },
        },
      },
    } = await pinDocument({ variables: { documentId, pinned } });
    if (metadata.status < 400) return;

    showSnackbar('Failed to pin document. Please try again');
  };

  const [openModal, setOpenModal] = useState(false);
  const initialGetFeedbackState = {
    id: '',
    initialState: { accurate: '', preferred: '', classified: false },
  };
  const [getFeedback, setGetFeedback] = useState(initialGetFeedbackState);

  const { allDocuments, allClassified, solicitationThreshold } = useCaseDocuments({
    case_: data?.cases?.cases?.[0],
    program,
  });

  const { doMutation: submitFeedback, error: feedbackError } = useSubmitFeedback();
  useEffect(() => {
    if (feedbackError) showSnackbar('There was an error submitting feedback. Please try again.');
  }, [feedbackError, showSnackbar]);

  const feedbackDocument = allDocuments.find((doc) => doc?.id === getFeedback.id);

  const feedback = doctopusEnabled && {
    solicitationThreshold,
    get: (id: string, feedbackType: YesNoUnsure, classified: boolean) => {
      if (feedbackType === YesNoUnsure.Yes) {
        return submitFeedback({ id, accurate: feedbackType });
      }
      setOpenModal(true);
      return setGetFeedback({
        id: id,
        initialState: {
          ...getFeedback.initialState,
          accurate: feedbackType === YesNoUnsure.Unsure ? '' : feedbackType,
          classified,
        },
      });
    },
  };

  useEffect(() => {
    if (!doctopusEnabled) return;
    if (!allClassified) {
      startPolling(POLL_INTERVAL);
    } else {
      stopPolling();
    }
  }, [doctopusEnabled, allClassified, startPolling, stopPolling]);

  // Refetch resolved entities when predictions data changes (new classifications)
  // Uses a retry mechanism since entity resolution may take time to process documents
  useEffect(() => {
    if (!nameMatchingEnabled || !predictionsData) return;

    let retryCount = 0;
    let timeoutId: ReturnType<typeof setTimeout> | undefined;

    const refetchWithRetry = async () => {
      try {
        await refetchResolvedEntities();
        retryCount++;

        // Retry up to MAX_RETRIES times to give entity-resolution time to process
        if (retryCount < ENTITY_RESOLUTION_MAX_RETRIES) {
          timeoutId = setTimeout(refetchWithRetry, ENTITY_RESOLUTION_RETRY_INTERVAL_MS);
        }
      } catch (error) {
        console.error('Failed to refetch resolved entities:', error);
        // Gracefully degrade - name matching indicators will show as unavailable
      }
    };

    // Start with initial delay, then retry at intervals
    timeoutId = setTimeout(refetchWithRetry, ENTITY_RESOLUTION_REFETCH_DELAY_MS);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [nameMatchingEnabled, predictionsData, refetchResolvedEntities]);

  // Helper function to check if a document is a Driver's License
  const isDriversLicense = (doc: Document | undefined): boolean => {
    if (!doc?.summary?.prediction?.label) return false;
    return doc.summary.prediction.label.name === 'drivers_license';
  };

  // Helper function to get name matching data for a specific document
  const getNameMatchingForDocument = (documentId: string): DocumentConsistency | undefined => {
    if (!nameMatchingEnabled || !resolvedEntitiesData) return undefined;

    const consistency = resolvedEntitiesData.case.resolvedEntities.applicantNameConsistency;
    if (!consistency.result) return undefined;

    return consistency.result.documents.find((doc) => doc.documentId === documentId);
  };

  // Name matching consistency data
  const nameMatchingConsistency = nameMatchingEnabled
    ? resolvedEntitiesData?.case.resolvedEntities.applicantNameConsistency
    : undefined;

  // Helper function to enhance a document with name matching indicator and data
  const enhanceDocumentWithNameMatching = (doc: Document | undefined): Document | undefined => {
    if (
      !nameMatchingEnabled ||
      !doc ||
      !isDriversLicense(doc as Document) ||
      !nameMatchingConsistency
    ) {
      return doc;
    }

    const documentConsistency = getNameMatchingForDocument(doc.id);
    if (!documentConsistency) return doc;

    // Create a document-specific consistency object with per-document similarity
    const documentSpecificConsistency: ApplicantNameConsistency = {
      status: nameMatchingConsistency.status,
      result: nameMatchingConsistency.result
        ? {
            canonicalName: nameMatchingConsistency.result.canonicalName,
            overallSimilarity: documentConsistency.similarity,
            documents: [documentConsistency],
          }
        : null,
    };

    return {
      ...doc,
      nameMatchingIndicator: (
        <NameMatchingIndicator consistency={documentSpecificConsistency} variant="icon-only" />
      ),
      nameMatchingData: {
        consistency: documentSpecificConsistency,
        bestMatchedName: documentConsistency.bestMatchedName,
      },
    };
  };

  if (loading)
    return (
      <div>
        <LoadingComponent message="Loading documents..." />
      </div>
    );

  return (
    <>
      {openModal && (
        <LabelFeedbackModal
          onClose={() => {
            setOpenModal(false);
            setGetFeedback(initialGetFeedbackState);
          }}
          document={feedbackDocument}
          initialState={getFeedback.initialState}
        />
      )}
      <Documents
        id="case-review-documents"
        documents={allDocuments.map(enhanceDocumentWithNameMatching) as Document[]}
        removeDocument={{ loading: removeLoading, doRemove }}
        uploadDocument={{ loading: uploadLoading, doUpload }}
        pinDocument={{ doPin }}
        feedback={feedback}
      />
    </>
  );
}
