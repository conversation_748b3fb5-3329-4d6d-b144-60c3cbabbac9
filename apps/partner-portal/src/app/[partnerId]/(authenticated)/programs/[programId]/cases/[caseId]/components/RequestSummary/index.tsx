import AddVendorModal from '@/app/[partnerId]/(authenticated)/vendors/_components/AddVendorModal';
import { checkProgramHasAnyFeature } from '@/app/_utils/checkFeature';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName } from '@bybeam/platform-types';
import { ReactElement, useState } from 'react';
import CorePayments from './CorePayments';
import ExtendedPayments from './ExtendedPayments';

export default function RequestSummary(): ReactElement {
  const program = useProgram();
  const [vendorModalOpen, setVendorModalOpen] = useState(false);

  if (
    !checkProgramHasAnyFeature(program, [
      FeatureName.PaymentsClaimFunds,
      FeatureName.PaymentsPartnerIssued,
      FeatureName.PaymentsExternalTracking,
    ])
  ) {
    return <></>;
  }

  const onAddVendor = (): void => setVendorModalOpen(true);
  const onCloseModal = (): void => setVendorModalOpen(false);

  return (
    <>
      {vendorModalOpen && <AddVendorModal onClose={onCloseModal} />}
      {checkFeature(program?.features, FeatureName.WorkflowCore) && (
        <CorePayments onAddVendor={onAddVendor} />
      )}
      {checkFeature(program?.features, FeatureName.WorkflowExtended) && (
        <ExtendedPayments onAddVendor={onAddVendor} />
      )}
    </>
  );
}
