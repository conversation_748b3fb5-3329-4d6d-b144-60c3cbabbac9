import Loading from '@/spa-legacy/common/components/Loading';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { DateFormat, formatDate } from '@/spa-legacy/common/utilities/format';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { ApplicantTypeRole, type Case, CaseParticipantStatus } from '@bybeam/platform-types';
import { ExternalLinkIcon } from '@radix-ui/react-icons';
import {
  Badge,
  Callout,
  Card,
  Container,
  DataList,
  Flex,
  Heading,
  Link as RadixLink,
  Text,
  Tooltip,
} from '@radix-ui/themes';
import Link from 'next/link';
import {
  type CaseParticipantDetails,
  getCaseParticipantsDetails,
} from '../../utils/getCaseParticipantsDetails';

interface ParticipantCardProps {
  participant: CaseParticipantDetails;
  caseId: string;
}

function ParticipantCard({ participant, caseId }: ParticipantCardProps) {
  const { applicantType, applicant, application, status, linkDetails } = participant;
  const program = useProgram();

  const programApplicantType = program?.applicantTypes?.find(
    (type) => type.applicantType.id === applicantType?.id,
  );

  const getStatusBadge = () => {
    switch (status) {
      case CaseParticipantStatus.Linked:
        return (
          <Badge highContrast color="green">
            Linked
          </Badge>
        );
      case CaseParticipantStatus.FailedLink:
        return (
          <Tooltip content="Failed to link due to incorrect code">
            <Badge highContrast color="red">
              Failed Link
            </Badge>
          </Tooltip>
        );
      case CaseParticipantStatus.PendingLink:
        return (
          <Tooltip content={`Invitation sent to ${linkDetails?.email}`}>
            <Badge highContrast color="orange">
              Invitation Sent
            </Badge>
          </Tooltip>
        );
      case CaseParticipantStatus.Unlinked:
        return <Badge color="gray">Not Invited</Badge>;
      default:
        return null;
    }
  };

  const profileUrl = applicant
    ? makeRoute(PORTAL_ROUTES.APPLICANT_PROFILE, { userId: applicant.id })
    : null;
  const applicationUrl = application
    ? makeRoute(PORTAL_ROUTES.APPLICATION_REVIEW_LEGACY, {
        caseId,
        applicationId: application.id,
      })
    : null;

  const displayName = applicant?.name || linkDetails?.name || applicantType.name;

  return (
    <Card>
      <Container width="100%" maxWidth="360px" minWidth="400px" pb="6">
        <Flex justify="between" align="start" mb="4" gap="2">
          <Text as="div" size="3" weight="bold">
            {displayName}
          </Text>
          <Flex direction="column" gap="2" align="end">
            <Badge
              highContrast
              color={applicantType.role === ApplicantTypeRole.FirstParty ? 'blue' : 'gray'}
            >
              {programApplicantType?.nameOverride || applicantType.name || 'Unknown'}
            </Badge>
            {getStatusBadge()}
          </Flex>
        </Flex>

        {status === CaseParticipantStatus.FailedLink && (
          <Callout.Root color="red" size="1" mb="3">
            <Callout.Text>
              Unable to link because the {applicantType.name} submitted an incorrect code.
            </Callout.Text>
          </Callout.Root>
        )}

        <DataList.Root>
          <DataList.Item>
            <DataList.Label>Email</DataList.Label>
            <DataList.Value>
              {applicant?.email || linkDetails?.email || 'Not provided'}
            </DataList.Value>
          </DataList.Item>
          {profileUrl && applicant?.displayId && (
            <DataList.Item>
              <DataList.Label>Beam User ID</DataList.Label>
              <DataList.Value>
                <RadixLink asChild>
                  <Link href={{ pathname: profileUrl }}>{applicant.displayId}</Link>
                </RadixLink>
              </DataList.Value>
            </DataList.Item>
          )}

          {status === CaseParticipantStatus.PendingLink && linkDetails?.invitationCode && (
            <DataList.Item>
              <DataList.Label>Invitation Code</DataList.Label>
              <DataList.Value>
                <Text weight="bold">{linkDetails.invitationCode}</Text>
              </DataList.Value>
            </DataList.Item>
          )}

          {applicationUrl && application?.submittedAt && (
            <DataList.Item>
              <DataList.Label>Latest Submission</DataList.Label>
              <DataList.Value>
                <RadixLink asChild>
                  <Link
                    href={{ pathname: applicationUrl }}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Flex align="center" gap="1">
                      {formatDate(application.submittedAt, DateFormat.Slashes)}
                      <ExternalLinkIcon />
                    </Flex>
                  </Link>
                </RadixLink>
              </DataList.Value>
            </DataList.Item>
          )}
        </DataList.Root>
      </Container>
    </Card>
  );
}

interface CaseParticipantsProps {
  case: Case;
}

export default function CaseParticipants({ case: case_ }: CaseParticipantsProps) {
  const program = useProgram();
  if (!program) return <Loading size="SM" />;

  const participantsDetails = getCaseParticipantsDetails(program, case_);

  if (participantsDetails.length <= 1) {
    return <></>;
  }

  return (
    <Flex direction="column" gap="3">
      <Heading as="h2" size="4">
        Case Participants
      </Heading>
      <Flex gap="4" wrap="wrap">
        {participantsDetails.map((participant) => {
          const key =
            participant.application?.id ||
            participant.applicant?.id ||
            participant.applicantType.id;

          return <ParticipantCard key={key} participant={participant} caseId={case_.id} />;
        })}
      </Flex>
    </Flex>
  );
}
