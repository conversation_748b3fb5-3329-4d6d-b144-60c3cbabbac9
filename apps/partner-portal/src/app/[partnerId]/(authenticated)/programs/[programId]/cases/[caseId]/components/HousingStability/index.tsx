import DetailSection from '@/spa-legacy/portal/components/DetailSection';
import type { Case } from '@bybeam/platform-types';
import { Box, Flex, Heading, Table, Text } from '@radix-ui/themes';
import useHousingStability, { HousingType } from './useHousingStability';

export default function HousingInstabilitySection(case_: Case): React.JSX.Element {
  const { queryResponse, enrollment, services } = useHousingStability(case_);

  const getServiceNames = (serviceIds: string[]) => {
    const allServices = queryResponse?.programs?.programs?.[0]?.services ?? [];
    return serviceIds
      .map((id) => {
        // Check top-level services
        const service = allServices.find((s) => s.id === id);
        if (service) return service.name;

        // Check children of all services
        for (const parentService of allServices) {
          if (parentService.children) {
            const childService = parentService.children.find((c) => c.id === id);
            if (childService) return childService.name;
          }
        }
        return null;
      })
      .filter(Boolean)
      .join(', ');
  };

  const getOutcomeNames = (outcomeIds: string[]) => {
    const allOutcomes = queryResponse?.programs?.programs?.[0]?.outcomes ?? [];
    return outcomeIds
      .map((id) => {
        const outcome = allOutcomes.find((o) => o.id === id);
        return outcome?.name;
      })
      .filter(Boolean)
      .join(', ');
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <DetailSection title="Housing Stability Services">
      <Flex direction="column" align="start" gap="5" width="100%">
        <Flex direction="column" gap="2" width="100%">
          {enrollment.startDateEnrollment && (
            <Flex gap="2">
              <Text weight="bold">Start Date of Enrollment:</Text>
              <Text>{formatDate(enrollment.startDateEnrollment)}</Text>
            </Flex>
          )}
          {enrollment.endDateEnrollment && (
            <Flex gap="2">
              <Text weight="bold">End Date of Enrollment:</Text>
              <Text>{formatDate(enrollment.endDateEnrollment)}</Text>
            </Flex>
          )}
        </Flex>

        {services[HousingType.Service]?.length > 0 && (
          <Flex direction="column" align="start" gap="3" width="100%">
            <Heading size="4">Services</Heading>
            <Box width="100%">
              <Table.Root variant="surface">
                <Table.Header>
                  <Table.Row>
                    <Table.ColumnHeaderCell>Service(s)</Table.ColumnHeaderCell>
                    <Table.ColumnHeaderCell>Service Date</Table.ColumnHeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {services[HousingType.Service].map((service, idx) => (
                    <Table.Row key={`${service.id}-${idx}-service`}>
                      <Table.Cell>{getServiceNames(service.options)}</Table.Cell>
                      <Table.Cell>{formatDate(service.date)}</Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table.Root>
            </Box>
          </Flex>
        )}

        {services[HousingType.Outcome]?.length > 0 && (
          <Flex direction="column" align="start" gap="3" width="100%">
            <Heading size="4">Outcomes</Heading>
            <Box width="100%">
              <Table.Root variant="surface">
                <Table.Header>
                  <Table.Row>
                    <Table.ColumnHeaderCell>Outcome(s)</Table.ColumnHeaderCell>
                    <Table.ColumnHeaderCell>Date of Outcome</Table.ColumnHeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {services[HousingType.Outcome].map((outcome, idx) => (
                    <Table.Row key={`${outcome.id}-${idx}-outcome`}>
                      <Table.Cell>{getOutcomeNames(outcome.options)}</Table.Cell>
                      <Table.Cell>{formatDate(outcome.date)}</Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table.Root>
            </Box>
          </Flex>
        )}
      </Flex>
    </DetailSection>
  );
}
