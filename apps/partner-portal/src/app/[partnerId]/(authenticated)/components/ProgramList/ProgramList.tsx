import { PORTAL_ROUTES } from '@/app/Routes';
import { makeRoute } from '@/app/Routes';
import { ChipSize } from '@/spa-legacy/common/components/Chip';
import usePollingQuery from '@/spa-legacy/common/hooks/usePollingQuery';
import ClosedChip from '@/spa-legacy/portal/components/ClosedChip';
import FundSummary from '@/spa-legacy/portal/components/FundSummary';
import { programSummaryDisplay } from '@/spa-legacy/portal/utils/programs';
import { Program } from '@bybeam/platform-types';
import { Button, Flex, Skeleton, Text } from '@radix-ui/themes';
import Link from 'next/link';
import React from 'react';
import programListQuery from './programList.graphql';

const ProgramList: React.FC = () => {
  const {
    data: queryData,
    loading,
    error,
  } = usePollingQuery<{ programs: { programs: Program[] } }>(programListQuery);
  const programs = queryData?.programs?.programs ?? [];

  const getProgramLink = (programId: string) => {
    return makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, { programId });
  };

  if (error) {
    return <>Failed to load programs.</>;
  }

  if (loading) {
    return <Skeleton title="loading programs" />;
  }

  return (
    <Flex wrap="wrap" gapX="6" gapY="3">
      {programSummaryDisplay(programs)?.map(({ id, name, fundData, active }) => (
        <Flex key={id} direction="column" gap="2" data-cy={id}>
          <Flex justify="between" align="center">
            <Flex gap="4" align="center">
              <Link href={{ pathname: getProgramLink(id) }}>
                <Text weight="bold">{name}</Text>
              </Link>
              {!active && <ClosedChip size={ChipSize.S} />}
            </Flex>
            <Button asChild variant="ghost" mr="2">
              <Link href={getProgramLink(id)}>View Details</Link>
            </Button>
          </Flex>
          <FundSummary fundingData={fundData} disabled={!active} />
        </Flex>
      ))}
    </Flex>
  );
};

export default ProgramList;
