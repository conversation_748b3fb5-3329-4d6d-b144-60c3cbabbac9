import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';
import { useSavedViews } from '@/app/hooks/useSavedViews';
import { SavedView } from '@bybeam/platform-types';
import * as Accordion from '@radix-ui/react-accordion';
import { ChevronRightIcon, StarFilledIcon } from '@radix-ui/react-icons';
import { Box, Flex, Text } from '@radix-ui/themes';
import Link from 'next/link';
import { NavItem } from '../NavItem';
import styles from './savedViews.module.css';

const SavedViewLink = ({ savedView }: { savedView: SavedView }) => {
  const { activeSavedView } = useSavedViews();
  const isActive = savedView === activeSavedView;

  return (
    <NavItem asChild isActive={isActive}>
      <Link
        href={{
          pathname: makeRoute(PORTAL_ROUTES.PROGRAM_OVERVIEW, savedView.filters?.routeParameters),
          query: savedView.filters.query,
        }}
      >
        <Text size="2" title={savedView.name}>
          {savedView.name}
        </Text>
      </Link>
    </NavItem>
  );
};

const SavedViews = () => {
  const { savedViews, loading, error } = useSavedViews();

  if (loading) {
    return <>Loading</>;
  }

  if (error) {
    return <>Something went wrong</>;
  }

  if (!savedViews.length) {
    return <></>;
  }

  return (
    <Accordion.Root type="single" collapsible defaultValue="saved-views">
      <Accordion.Item value="saved-views" className={styles.AccordionItem}>
        <Accordion.Header asChild>
          <NavItem asChild tabIndex={0} className={styles.accordionTrigger}>
            <Accordion.Trigger>
              <Flex gap="1" align="center" width="100%">
                <Flex gap="2" align="center">
                  <StarFilledIcon height={18} width={18} className={styles.headingIcon} />
                  <Text size="2">Saved Views</Text>
                </Flex>
                <ChevronRightIcon className={styles.accordionChevron} />
              </Flex>
            </Accordion.Trigger>
          </NavItem>
        </Accordion.Header>
        <Accordion.Content className={styles.AccordionContent}>
          <Box p="1">
            <Flex asChild direction="column" gap="1" pl="2">
              <ul>
                {savedViews.map((savedView: SavedView) => {
                  return (
                    <li key={savedView.id} className={styles.savedViewListItem}>
                      <SavedViewLink savedView={savedView} />
                    </li>
                  );
                })}
              </ul>
            </Flex>
          </Box>
        </Accordion.Content>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default SavedViews;
