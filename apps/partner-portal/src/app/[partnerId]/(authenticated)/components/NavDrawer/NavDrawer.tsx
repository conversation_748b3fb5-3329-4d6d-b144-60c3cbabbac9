import { checkFeatureAnyProgram, checkPartnerHasFeature } from '@/app/_utils/checkFeature';
import MyAssignments from '@/app/components/features/MyAssignments/MyAssignments';
import BeamLogo from '@/spa-legacy/common/components/BeamLogo';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { FeatureName } from '@bybeam/platform-types';
import {
  AttachMoney,
  Dashboard,
  Folder,
  Insights,
  Language,
  Logout,
  RecentActors,
  Settings,
} from '@mui/icons-material';
import { Rule } from '@mui/icons-material';

import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';
import { Role } from '@/app/_utils/roles';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { useNextAuth } from '@/app/providers/NextAuthProvider';
import { Divider } from '@mui/material';
import { Popover } from '@radix-ui/themes';
import cx from 'classnames';
import { IKImage } from 'imagekitio-react';
import { useFeatureFlagEnabled, usePostHog } from 'posthog-js/react';
import Changelog from './Changelog/Changelog';
import styles from './NavDrawer.module.css';
import { HelpButton, NavButton, NavLink } from './NavItem';
import NavItemStyles from './NavItem.module.css';
import SavedViews from './SavedViews/SavedViews';
import ViewOnlyIndicator from './ViewOnlyIndicator/ViewOnlyIndicator';

const PoweredByBeam = (): JSX.Element => (
  <div className={styles.poweredByBeam}>
    <Typography variant="fieldLabel">powered by</Typography>
    <BeamLogo className={styles.beamLogo} />
  </div>
);

export function NavGroup({
  className,
  children,
}: { className?: string; children: React.ReactNode }): JSX.Element {
  return <div className={cx(className, styles.navGroup)}>{children}</div>;
}

export default function NavDrawer(): JSX.Element {
  const partner = usePartner();
  const posthog = usePostHog();
  const { roles, signOut } = useNextAuth();
  const changelogsEnabled = posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.changelog);
  // Reactive PostHog flag for rules engine – keeps nav item in sync with flag resolution/changes
  const showEligibility = useFeatureFlagEnabled(POSTHOG_FEATURE_FLAGS.rulesEngine);
  // TODO: check to see if the user has view-only permission on the organization
  const viewOnlyMode = roles?.includes(Role.ViewOnly);

  const { programs } = partner;

  return (
    <div className={styles.navigationDrawer}>
      <div className={styles.partnerLogoContainer}>
        {partner && (
          <IKImage
            src={partner.whitelabeling.logo}
            alt={`${partner.name} logo`}
            className={styles.partnerLogoImage}
            urlEndpoint="https://ik.imagekit.io/bybeam/"
            transformation={[{ height: '32', dpr: `${window.devicePixelRatio}` }]}
          />
        )}
      </div>
      <NavGroup>
        <NavLink link={makeRoute(PORTAL_ROUTES.HOME)} leadingIcon={<Dashboard />}>
          Programs Dashboard
        </NavLink>
        <NavLink link={makeRoute(PORTAL_ROUTES.APPLICANTS)} leadingIcon={<RecentActors />}>
          Applicants
        </NavLink>
        {checkPartnerHasFeature(partner, FeatureName.AnalyticsEmbed) && (
          <NavLink link={makeRoute(PORTAL_ROUTES.ANALYTICS_DASHBOARD)} leadingIcon={<Insights />}>
            Analytics
          </NavLink>
        )}
        {checkFeatureAnyProgram(programs || [], FeatureName.PaymentsVendors) && (
          <NavLink link={makeRoute(PORTAL_ROUTES.VENDOR_OVERVIEW)} leadingIcon={<Language />}>
            Vendors
          </NavLink>
        )}
        {posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.fundsPage) && (
          <NavLink link={makeRoute(PORTAL_ROUTES.FUNDS_OVERVIEW)} leadingIcon={<AttachMoney />}>
            Funds
          </NavLink>
        )}
        {showEligibility && (
          <NavLink link={makeRoute(PORTAL_ROUTES.ELIGIBILITY)} leadingIcon={<Rule />}>
            Rules
          </NavLink>
        )}
      </NavGroup>
      <Divider />
      <NavGroup>
        <div>
          <Popover.Root>
            <Popover.Trigger>
              <NavButton leadingIcon={<Folder />}>My assignments</NavButton>
            </Popover.Trigger>
            <Popover.Content side="right" width="360px" maxHeight={'95vh'} style={{ padding: 0 }}>
              <MyAssignments />
            </Popover.Content>
          </Popover.Root>
          <SavedViews />
        </div>
      </NavGroup>
      <Divider />
      <NavGroup className={styles.lastGroup}>
        <NavLink link={makeRoute(PORTAL_ROUTES.SETTINGS)} leadingIcon={<Settings />}>
          Settings
        </NavLink>
        <HelpButton link="http://beampartners.zendesk.com/" />
        <div>
          <button onClick={signOut} className={NavItemStyles.navItem} type="button">
            <Logout />
            Logout
          </button>
        </div>
        {viewOnlyMode ? <ViewOnlyIndicator /> : null}
      </NavGroup>
      <Divider />
      {changelogsEnabled ? (
        <>
          <NavGroup>
            <Changelog />
          </NavGroup>
          <Divider />
        </>
      ) : null}
      <PoweredByBeam />
    </div>
  );
}
