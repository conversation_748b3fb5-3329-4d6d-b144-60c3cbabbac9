'use client';
import Page from '@/app/components/ui/Page/Page';
import { useUserHomeRoute } from '@/app/hooks/useUserHomeRoute';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useCustomPageTitle from '@/spa-legacy/common/hooks/useCustomPageTitle';
import { useMutation } from '@apollo/client';
import {
  Container,
  Flex,
  Heading,
  Select,
  Separator,
  Switch,
  Text,
  Tooltip,
} from '@radix-ui/themes';
import { Route } from 'next';
import { useRouter, useSearchParams } from 'next/navigation';
import { useFeatureFlagEnabled } from 'posthog-js/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import AuditLogTable from './components/AuditLogTable';
import JdmEditorPanel from './components/JdmEditor/JdmEditorPanel';
import ToggleRulesEvaluation from './graphql/ToggleRulesEvaluation.graphql';

export default function RulesPage() {
  useCustomPageTitle('Rules');

  const defaultRoute = useUserHomeRoute();
  const router = useRouter();

  // Gate the section behind the same rules engine flag used elsewhere
  const isEnabled = useFeatureFlagEnabled(POSTHOG_FEATURE_FLAGS.rulesEngine);
  useEffect(() => {
    if (isEnabled === false) router.replace(defaultRoute as Route);
  }, [isEnabled, router, defaultRoute]);
  // Avoid rendering while the flag is still loading
  if (isEnabled !== true) return null;

  const searchParams = useSearchParams();
  const { programs = [], externalId } = usePartner() || {};
  const [selectedProgramId, setSelectedProgramId] = useState<string | undefined>(undefined);
  const [toggleProgram, { loading: toggling }] = useMutation(ToggleRulesEvaluation);

  const selectedProgram = useMemo(
    () => programs.find((program) => program.id === selectedProgramId),
    [programs, selectedProgramId],
  );

  // Initialize selection from the URL (?programId=...) once programs are loaded.
  useEffect(() => {
    const urlProgramId = searchParams.get('programId');
    if (
      !selectedProgramId &&
      urlProgramId &&
      programs.some((program) => program.id === urlProgramId)
    ) {
      setSelectedProgramId(urlProgramId);
    }
  }, [searchParams, programs, selectedProgramId]);

  const toggleRulesEvaluation = useCallback(
    async (checked: boolean) => {
      try {
        if (!selectedProgramId) return;
        await toggleProgram({
          variables: { input: [{ id: selectedProgramId, rulesEvaluationEnabled: checked }] },
        });
      } catch (err) {
        console.error('Failed to toggle rules evaluation', err);
      }
    },
    [selectedProgramId, toggleProgram],
  );

  const onSelectProgram = useCallback(
    (programId: string) => {
      setSelectedProgramId(programId);
      // Reflect selection in the URL without navigating away (only when selected)
      // Build path without the "/partner" prefix; the app already mounts at /:partnerId
      if (externalId) {
        const base = `/${externalId}/rules`;
        const nextUrl = (
          programId ? `${base}?programId=${encodeURIComponent(programId)}` : base
        ) as Route;
        router.replace(nextUrl, { scroll: false });
      }
    },
    [externalId, router],
  );

  return (
    <Page>
      <Page.Header>
        <Page.Breadcrumbs>
          <Page.Breadcrumbs.Crumb>Rules</Page.Breadcrumbs.Crumb>
        </Page.Breadcrumbs>
      </Page.Header>
      <Page.Content>
        <Container p="4" align="left" maxWidth="100%">
          <Flex direction="column" gap="4">
            <Text size="3">
              Beam's Rules Engine allows partners to define the rules and requirements for their
              programs to automatically add evaluations of eligibility and scores to applications as
              they come in. To create the rules evaluation model for your program, use the{' '}
              <strong>decision table</strong> below to define sets of criteria (inputs) and their
              results (outputs) in a spreadsheet format.
            </Text>

            <Separator size="4" />

            <Heading size="3">Select a program</Heading>
            {programs.length ? (
              <Flex direction="row" gap="4">
                <Select.Root
                  onValueChange={onSelectProgram}
                  value={selectedProgramId}
                  key="program-select"
                >
                  <Select.Trigger placeholder="Select a program" />
                  <Select.Content position="popper">
                    {programs?.map((option) => (
                      <Select.Item key={option.id} value={option.id}>
                        {option.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Root>

                {selectedProgramId ? (
                  <Flex direction="row" gap="2" align="center">
                    <Tooltip content="When on, new application submissions and updates in this program will trigger automatic rules evaluations. When off, manual evaluations on cases can still be triggered, and rules can still be edited and saved.">
                      <Text size="2">
                        <strong>Automatic evaluations</strong>
                      </Text>
                    </Tooltip>
                    <Switch
                      disabled={toggling}
                      checked={Boolean(selectedProgram?.config?.rulesEvaluationEnabled)}
                      onCheckedChange={toggleRulesEvaluation}
                    />
                    <Text size="2" color="gray">
                      {selectedProgram?.config?.rulesEvaluationEnabled ? 'On' : 'Off'}
                    </Text>
                  </Flex>
                ) : null}
              </Flex>
            ) : (
              <Text size="2">No programs available for this partner.</Text>
            )}

            <Separator size="4" />
            {/* Editor */}
            <Heading size="3">Rules Evaluation Model</Heading>
            <JdmEditorPanel
              programId={selectedProgramId}
              enabled={
                programs.find((program) => program.id === selectedProgramId)?.config
                  ?.rulesEvaluationEnabled
              }
            />

            <Separator size="4" />
            {/* Audit Log */}
            <Heading size="3">Audit Log</Heading>
            {selectedProgramId ? (
              <AuditLogTable programId={selectedProgramId} />
            ) : (
              <Text size="2">Select a program to view its ruleset history.</Text>
            )}
          </Flex>
        </Container>
      </Page.Content>
    </Page>
  );
}
