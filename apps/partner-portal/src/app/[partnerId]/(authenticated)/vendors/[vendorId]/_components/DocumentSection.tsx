import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Documents from '@/spa-legacy/portal/components/Documents';
import PinDocumentMutation from '@/spa-legacy/portal/graphql/mutations/PinDocumentMutation.graphql';
import { useMutation } from '@apollo/client';
import sort from '@bybeam/platform-lib/utilities/sort';
import type { Document, MutationResponse, Vendor } from '@bybeam/platform-types';
import RemoveVendorDocumentMutation from './RemoveVendorDocumentMutation.graphql';
import UploadVendorDocumentMutation from './UploadVendorDocumentMutation.graphql';

export default function DocumentSection({ id, documents }: Vendor): JSX.Element {
  const [uploadDocument, { loading: uploadLoading }] = useMutation<
    { vendor: { uploadDocuments: MutationResponse<Vendor> } },
    { id: string; files: File[] }
  >(UploadVendorDocumentMutation);
  const [removeDocument, { loading: removeLoading }] = useMutation<
    { vendor: { removeDocuments: MutationResponse<Vendor> } },
    { id: string; documentId: string }
  >(RemoveVendorDocumentMutation);
  const [pinDocument] = useMutation<
    { document: { pinDocument: MutationResponse<Document> } },
    { documentId: string; pinned: boolean }
  >(PinDocumentMutation);

  const { showSnackbar } = useSnackbar();
  const doUpload = async (files: File[]): Promise<void> => {
    const {
      data: {
        vendor: {
          uploadDocuments: { metadata },
        },
      },
    } = await uploadDocument({ variables: { id, files } });
    if (metadata.status < 400) return;

    showSnackbar('Failed to upload document. Please refresh and try again');
  };

  const doRemove = async (documentId: string): Promise<void> => {
    const {
      data: {
        vendor: {
          removeDocuments: { metadata },
        },
      },
    } = await removeDocument({ variables: { id, documentId } });
    if (metadata.status < 400) return;

    showSnackbar('Failed to remove document. Please refresh and try again');
  };

  const doPin = async (documentId: string, pinned: boolean): Promise<void> => {
    const {
      data: {
        document: {
          pinDocument: { metadata },
        },
      },
    } = await pinDocument({ variables: { documentId, pinned } });
    if (metadata.status < 400) return;

    showSnackbar('Failed to pin document. Please try again');
  };

  return (
    <Documents
      id="vendor-details-documents"
      documents={sort(documents, { accessor: (doc) => Number(doc.pinned), ascending: false })}
      removeDocument={{ loading: removeLoading, doRemove }}
      uploadDocument={{ loading: uploadLoading, doUpload }}
      pinDocument={{ doPin }}
    />
  );
}
