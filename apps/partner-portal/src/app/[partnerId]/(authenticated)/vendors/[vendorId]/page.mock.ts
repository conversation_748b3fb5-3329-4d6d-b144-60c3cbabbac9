import { Permission } from '@/app/_utils/roles';
import CAN_ACCESS_QUERY from '@/app/providers/HasAccessQuery.graphql';
import { PaymentMethod, PaymentStatus } from '@bybeam/platform-types';
import VENDOR_PAYMENTS_QUERY from './_components/GetPaymentsByVendorIdQuery.graphql';
import VENDOR_QUERY from './_components/GetVendorQuery.graphql';

const CAN_ACCESS_VENDOR_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: Permission.View,
        resource: { objectType: 'VENDOR', objectId: 'mockVendorA' },
      },
    },
  },
  result: {
    data: {
      hasAccess: {
        id: 'user:mock-user-id:vendor:mockVendorA',
        canAccess: true,
        hasAccess: true,
        message: 'user:mock-user-id can access vendor:mockVendorA',
        token: 'mockToken',
        __typename: 'HasAccessResponse',
      },
    },
  },
  newData: () => ({
    data: {
      hasAccess: {
        id: 'user:mock-user-id:vendor:mockVendorA',
        canAccess: true,
        hasAccess: true,
        message: 'user:mock-user-id can access vendor:mockVendorA',
        token: 'mockToken',
        __typename: 'HasAccessResponse',
      },
    },
  }),
};

const VENDOR_MOCK = {
  request: {
    query: VENDOR_QUERY,
    variables: {
      bankAccount: true,
    },
  },
  result: {
    data: {
      vendors: {
        nodes: [
          {
            id: 'mockVendorA',
            name: 'Brighter Days Cleaning',
            mailingAddress: {
              addressLine1: '650 S Town Center Dr',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89106,
            },
            types: [{ name: 'Cleaning Services' }],
            externalId: 'EXT1234',
            email: '<EMAIL>',
            phone: '+***********',
            aggregatePayments: { count: 10 },
            documents: [
              {
                id: 'mock-doc-id',
                previewUrl: 'fake.com',
                filename: '403b-certification.pdf',
                mimetype: 'application/pdf',
                createdAt: new Date('2020-10-20T17:39'),
                uploader: { name: 'Eugene Debs' },
              },
            ],
            notes: [
              {
                id: 'mock-note-id',
                content: 'We should look into this vendor something fishy is going on',
                author: { identityUser: { name: 'Eugene Debs' } },
              },
            ],
            bankAccount: {
              accountType: 'checking',
              accountNumber: '1234',
              routingNumber: '********',
            },
          },
        ],
      },
    },
  },
};

const VENDOR_PAYMENTS_MOCK = {
  request: {
    query: VENDOR_PAYMENTS_QUERY,
    variables: {
      vendorId: 'mockVendorA',
      pagination: { page: 0, take: 15 },
      sort: { column: 'CheckDate', direction: 'Ascending' },
    },
  },
  result: {
    data: {
      payments: {
        payments: [
          {
            id: 'mockPaymentA',
            displayId: 'P0001245',
            amount: 35000,
            status: PaymentStatus.Success,
            method: PaymentMethod.DirectDeposit,
            initiatedAt: new Date('2020-10-20T07:12'),
            completedAt: new Date('2020-10-21T17:39'),
            fulfillment: {
              case: {
                id: 'mockCaseIdA',
                displayId: 'C00012456',
                program: {
                  id: 'mockProgramId',
                  name: 'Housing Assistance Program',
                  features: [],
                },
              },
              fulfillmentMeta: { checkNumber: '632759', billingCode: 'EXT-123' },
            },
          },
        ],
        pageInfo: { count: 3 },
      },
    },
  },
};

export { CAN_ACCESS_VENDOR_MOCK, VENDOR_MOCK, VENDOR_PAYMENTS_MOCK };
