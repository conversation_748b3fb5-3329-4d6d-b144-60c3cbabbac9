query GetVendor($id: UUID!, $bankAccount: Boolean = false) {
  vendors(filter: { id: $id }) {
    nodes {
      id
      name
      types {
        id
        name
      }
      taxId
      externalId
      phone
      email
      updatedAt

      aggregatePayments {
        count
        sum
      }

      mailingAddress {
        id
        addressLine1
        addressLine2
        city
        state
        zip
        incomeLimitArea {
          id
          county
        }
      }

      documents {
        id
        previewUrl
        filename
        mimetype
        createdAt
        pinned
        uploader {
          id
          name
        }
      }

      notes {
        id
        createdAt
        updatedAt
        content
        author {
          id
          archivedAt
          identityUser {
            id
            name
          }
        }
      }

      bankAccount @include(if: $bankAccount) {
        accountType
        accountNumber
        routingNumber
      }
    }
  }
}
