import Typography from '@/spa-legacy/common/components/Typography';
import { formatCurrency, formatNumber } from '@/spa-legacy/common/utilities/format';
import DashboardCard, { DashboardCardMetric } from '@/spa-legacy/portal/components/Card';
import type { Vendor } from '@bybeam/platform-types';
import { Flex } from '@radix-ui/themes';

export default function TotalsSection({ aggregatePayments }: Vendor): JSX.Element {
  return (
    <section>
      <Flex direction="column" gap="1">
        <Typography variant="body" bold tag="h2">
          Totals
        </Typography>
      </Flex>
      <Flex className="mt-3" gap="4">
        <DashboardCard>
          <DashboardCardMetric
            label="Total paid"
            value={formatCurrency(aggregatePayments?.sum ?? 0)}
          />
        </DashboardCard>
        <DashboardCard>
          <DashboardCardMetric
            label="Payments sent"
            value={formatNumber(aggregatePayments?.count ?? 0)}
          />
        </DashboardCard>
        <DashboardCard>
          <DashboardCardMetric
            label="Average payment"
            value={formatCurrency((aggregatePayments?.sum || 0) / (aggregatePayments?.count || 1))}
          />
        </DashboardCard>
      </Flex>
    </section>
  );
}
