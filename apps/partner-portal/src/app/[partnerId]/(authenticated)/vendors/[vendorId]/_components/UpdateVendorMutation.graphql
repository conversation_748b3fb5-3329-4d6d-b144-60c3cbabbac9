mutation UpdateVendor($input: UpdateVendorInput!) {
  vendor {
    update(input: $input) {
      metadata {
        status
        message
      }
      record {
        id
        name
        types {
          id
          name
        }
        taxId
        externalId
        phone
        email
        mailingAddress {
          id
          addressLine1
          addressLine2
          city
          state
          zip
          incomeLimitArea {
            id
            county
          }
        }
        createdAt
        documents {
          id
          previewUrl
          filename
          mimetype
          createdAt
          pinned
        }
        bankAccount {
          accountType
          accountNumber
          routingNumber
        }
      }
    }
  }
}
