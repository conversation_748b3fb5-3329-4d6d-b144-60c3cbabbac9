import { HAS_ACCESS_ORGANIZATION_EDIT_MOCK } from '@/tests/mocks/hasAccessMocks';
import { PARTNER } from '@/tests/mocks/mockData';
import { Meta, StoryObj } from '@storybook/nextjs';
import Page from './page';
import { CAN_ACCESS_VENDOR_MOCK, VENDOR_MOCK, VENDOR_PAYMENTS_MOCK } from './page.mock';

const meta: Meta = {
  component: Page,
};
export default meta;

type Story = StoryObj<typeof Page>;

export const Primary: Story = {
  args: {},
  parameters: {
    partner: PARTNER,
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/partner/mockPartnerId/vendors/mockVendorA',
        segments: [
          ['mockVendorA', 'vendors'],
          ['mockPartnerId', 'partner'],
        ],
      },
    },
    apolloClient: {
      mocks: [
        HAS_ACCESS_ORGANIZATION_EDIT_MOCK,
        CAN_ACCESS_VENDOR_MOCK,
        VEN<PERSON><PERSON>_MOCK,
        VENDOR_PAYMENTS_MOCK,
      ],
    },
  },
};
