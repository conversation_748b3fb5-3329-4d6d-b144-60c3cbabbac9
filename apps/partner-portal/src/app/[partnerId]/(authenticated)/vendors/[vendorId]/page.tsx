'use client';
import Page from '@/app/components/ui/Page/Page';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import VendorDetails from './_components/VendorDetails';

export default function VendorViewPage() {
  const { partnerId } = useParams<{ partnerId: string }>();

  return (
    <Page>
      <Page.Header>
        <Page.Breadcrumbs>
          <Page.Breadcrumbs.Crumb>
            <Link href={`/${partnerId}/vendors`}>Vendors</Link>
          </Page.Breadcrumbs.Crumb>
          <Page.Breadcrumbs.Crumb>Vendor Details</Page.Breadcrumbs.Crumb>
        </Page.Breadcrumbs>
      </Page.Header>
      <Page.Content>
        <VendorDetails />
      </Page.Content>
    </Page>
  );
}
