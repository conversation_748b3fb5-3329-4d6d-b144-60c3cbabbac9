import Checkbox from '@/spa-legacy/common/components/Checkbox';
import Dropdown from '@/spa-legacy/common/components/Dropdown';
import ErrorDisplay from '@/spa-legacy/common/components/ErrorDisplay';
import Modal from '@/spa-legacy/common/components/Modal';
import TextInput from '@/spa-legacy/common/components/TextInput';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useGeocoder, { type GeocoderResult } from '@/spa-legacy/common/hooks/useGeocoder';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Color from '@/spa-legacy/common/utilities/Color';
import { isEmail } from '@/spa-legacy/common/utilities/email';
import getFormAddress from '@/spa-legacy/common/utilities/getFormAddress';
import { AccountTypesDisplay } from '@/spa-legacy/common/utilities/payment';
import AddressFieldVendor from '@/spa-legacy/portal/components/AddressFieldVendor';
import useVendorTypes from '@/spa-legacy/portal/hooks/useVendorTypes';
import { hasBankInfoChanged, validateAccountNumber } from '@/spa-legacy/portal/utils/bankAccount';
import {
  checkFeatureAnyProgram,
  checkFeaturesAnyProgram,
} from '@/spa-legacy/utilities/checkFeature';
import isRoutingNumberValid from '@/spa-legacy/utilities/routingNumber';
import { useMutation } from '@apollo/client';
import { formatPhone, isPhoneNumber } from '@bybeam/formatting';
import {
  AccountType,
  type Address,
  type BankAccount,
  FeatureName,
  type MutationResponse,
  Program,
  type Vendor,
} from '@bybeam/platform-types';
import { Button, Flex, Heading, Theme } from '@radix-ui/themes';
import { usePostHog } from 'posthog-js/react';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import UpdateVendorMutation from './UpdateVendorMutation.graphql';

type OmitAddress = Omit<Address, 'id' | 'createdAt'>;

interface FormData {
  typeIds?: string[];
  name: string;
  taxId?: string;
  phone?: string;
  email: string;
  externalId?: string;
  accountType?: AccountType;
  routingNumber?: string;
  accountNumber?: string;
  address: string;
  apartment?: string;
  city: string;
  state: string;
  zip: string;
}

interface AddressData {
  address: string;
  apartment?: string;
  city: string;
  state: string;
  zip: string;
}

interface SimpleUpdateAction {
  type: 'simpleUpdate';
  payload: {
    field: keyof FormData;
    value: string;
  };
}

export default function EditVendor({
  id,
  name,
  taxId,
  externalId,
  phone,
  email,
  mailingAddress,
  types,
  bankAccount,
  onCloseModal,
}: Vendor & { onCloseModal: () => void }): JSX.Element {
  const posthog = usePostHog();
  const [isManualAddress, setIsManualAddress] = useState(false);
  const [isValidAddress, setIsValidAddress] = useState(false);
  const vendorTypes = useVendorTypes();
  const { formFields: addressFields } = getFormAddress(mailingAddress);
  const { programs } = usePartner();
  const collectExternalId = checkFeatureAnyProgram(
    programs as Program[],
    FeatureName.PaymentsExternalTracking,
  );
  const collectAccount = checkFeaturesAnyProgram(programs as Program[], [
    FeatureName.PaymentsDirectDeposit,
    FeatureName.PaymentsPartnerIssued,
    FeatureName.PaymentsVendors,
  ]);

  const {
    handleSubmit,
    control,
    formState: { errors, isDirty },
    watch,
    setValue,
  } = useForm<FormData>({
    defaultValues: {
      typeIds: types?.map((type) => type.id) || [],
      name,
      taxId,
      phone: phone ? formatPhone(phone) : '',
      email,
      externalId,
      accountType: bankAccount?.accountType || AccountType.Checking,
      routingNumber: bankAccount?.routingNumber ?? '',
      accountNumber: bankAccount?.accountNumber ?? '',
      ...addressFields,
    },
  });

  const [updateVendor, { loading }] = useMutation<{
    vendor: { update: MutationResponse<Vendor> };
  }>(UpdateVendorMutation);

  const { addressIsValid } = useGeocoder();
  const validateAddress = async (formData: AddressData): Promise<OmitAddress> => {
    let validatedAddress: false | GeocoderResult = false;
    if (!isManualAddress) validatedAddress = await addressIsValid(formData);
    if (validatedAddress) {
      const { city, postalCode, stateCode, houseNumber, street, county } = validatedAddress.address;
      return {
        addressLine1: [houseNumber, street].filter(Boolean).join(' '),
        addressLine2: formData.apartment,
        city,
        county,
        state: stateCode,
        zip: postalCode,
        latitude: validatedAddress.position.lat,
        longitude: validatedAddress.position.lng,
      };
    }
    return {
      addressLine1: formData?.address,
      addressLine2: formData?.apartment,
      city: formData?.city,
      state: formData?.state,
      zip: formData?.zip,
    };
  };

  const { showSnackbar } = useSnackbar();
  const onSubmit = async (formData: FormData): Promise<void> => {
    if (!isDirty) {
      onCloseModal();
      return;
    }

    const {
      phone: phoneValue,
      email: emailValue,
      accountType,
      accountNumber,
      routingNumber,
      typeIds,
    } = formData;

    const addressData: AddressData = {
      address: formData.address,
      apartment: formData.apartment,
      city: formData.city,
      state: formData.state,
      zip: formData.zip,
    };

    const validatedAddress = await validateAddress(addressData);
    const result = await updateVendor({
      variables: {
        input: {
          id,
          email: emailValue || null,
          mailingAddress: validatedAddress,
          phone: phoneValue ? formatPhone(phoneValue, 'E164') : null,
          typeIds,
          externalId: formData?.externalId,
          taxId: formData?.taxId,
          ...(collectAccount &&
            bankAccount &&
            hasBankInfoChanged(formData as BankAccount, bankAccount) && {
              bankAccount: { accountType, accountNumber, routingNumber },
            }),
        },
      },
    });

    posthog.capture('edit_vendor', { formData });

    if (
      result.data?.vendor?.update?.metadata?.status &&
      result.data.vendor.update.metadata.status < 400
    ) {
      showSnackbar('Vendor successfully updated.');
      onCloseModal();
      return;
    }
    showSnackbar(
      'There was an error editing the vendor.',
      <Button variant="text" textColor={Color.Link} onClick={handleSubmit(onSubmit)}>
        Try Again
      </Button>,
    );
  };

  return (
    <Modal isOpen onClickClose={onCloseModal} title="" size="S">
      <Theme accentColor="indigo">
        <Flex asChild direction="column" gap="4">
          <form onSubmit={handleSubmit(onSubmit)} noValidate>
            <Heading>Edit Vendor</Heading>
            <Flex asChild direction="column" gap="4" mb="4">
              <fieldset>
                <div>
                  <Checkbox
                    label={
                      <Typography variant="body" tag="span">
                        Manually input address
                      </Typography>
                    }
                    checked={isManualAddress}
                    onClick={(): void => setIsManualAddress((prevState) => !prevState)}
                  />
                </div>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Vendor Name is required' }}
                  render={({ field }) => (
                    <TextInput
                      id="vendorName"
                      label="Vendor Name"
                      required
                      value={field.value}
                      onChange={field.onChange}
                      error={errors.name?.message}
                    />
                  )}
                />

                <AddressFieldVendor
                  useFormResponse={{
                    formData: watch(),
                    dispatch: (action: SimpleUpdateAction) => {
                      if (action.type === 'simpleUpdate') {
                        setValue(action.payload.field, action.payload.value);
                      }
                    },
                  }}
                  handleChange={(type, value) => setValue(type as keyof FormData, value)}
                  isManually={isManualAddress}
                  setIsValidAddress={setIsValidAddress}
                />

                <Controller
                  name="phone"
                  control={control}
                  rules={{
                    validate: (value) =>
                      !value || isPhoneNumber(value) || 'Please enter a valid phone number',
                  }}
                  render={({ field }) => (
                    <TextInput
                      id="phone"
                      label="Phone Number"
                      type="tel"
                      value={field?.value ?? ''}
                      onChange={field.onChange}
                      error={errors.phone?.message}
                    />
                  )}
                />

                <Controller
                  name="email"
                  control={control}
                  rules={{
                    validate: (value) => !value || isEmail(value) || 'Please enter a valid email',
                  }}
                  render={({ field }) => (
                    <TextInput
                      id="contactEmail"
                      label="Contact Email"
                      type="email"
                      value={field.value}
                      onChange={field.onChange}
                      error={errors.email?.message}
                    />
                  )}
                />

                <Controller
                  name="typeIds"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      id="vendorType"
                      label="Vendor Type"
                      required
                      value={field?.value ?? []}
                      items={vendorTypes}
                      onChange={field.onChange}
                      multiple
                      error={errors.typeIds?.message}
                    />
                  )}
                />

                {collectExternalId && (
                  <Controller
                    name="externalId"
                    control={control}
                    render={({ field }) => (
                      <TextInput
                        id="externalId"
                        label="Vendor External ID"
                        required
                        value={field?.value ?? ''}
                        onChange={field.onChange}
                        error={errors.externalId?.message}
                      />
                    )}
                  />
                )}

                <Controller
                  name="taxId"
                  control={control}
                  render={({ field }) => (
                    <TextInput
                      id="taxId"
                      label="Tax ID/SSN"
                      value={field?.value ?? ''}
                      onChange={field.onChange}
                      error={errors.taxId?.message}
                    />
                  )}
                />

                {collectAccount && bankAccount && (
                  <>
                    <Controller
                      name="accountType"
                      control={control}
                      rules={{
                        validate: (value) =>
                          !hasBankInfoChanged(watch() as BankAccount, bankAccount) ||
                          value ||
                          'Account Type is required',
                      }}
                      render={({ field }) => (
                        <Dropdown
                          id="accountType"
                          label="Account Type"
                          value={field.value}
                          items={[
                            {
                              label: AccountTypesDisplay[AccountType.Checking],
                              value: AccountType.Checking,
                            },
                            {
                              label: AccountTypesDisplay[AccountType.Savings],
                              value: AccountType.Savings,
                            },
                          ]}
                          onChange={field.onChange}
                          error={errors.accountType?.message}
                          required={hasBankInfoChanged(watch() as BankAccount, bankAccount)}
                        />
                      )}
                    />

                    <Controller
                      name="routingNumber"
                      control={control}
                      rules={{
                        validate: (value) =>
                          !hasBankInfoChanged(watch() as BankAccount, bankAccount) ||
                          isRoutingNumberValid(value ?? '') ||
                          'Please enter a valid routing number',
                      }}
                      render={({ field }) => (
                        <TextInput
                          id="routingNumber"
                          label="Routing Number"
                          value={field?.value ?? ''}
                          onChange={field.onChange}
                          error={errors.routingNumber?.message}
                          required={hasBankInfoChanged(watch() as BankAccount, bankAccount)}
                        />
                      )}
                    />

                    <Controller
                      name="accountNumber"
                      control={control}
                      rules={{
                        validate: (value) =>
                          !hasBankInfoChanged(watch() as BankAccount, bankAccount) ||
                          validateAccountNumber(watch() as BankAccount, bankAccount) ||
                          'Please enter a valid account number',
                      }}
                      render={({ field }) => (
                        <TextInput
                          id="accountNumber"
                          label="Account Number"
                          value={field?.value ?? ''}
                          onChange={field.onChange}
                          onFocus={(): void => {
                            if (
                              bankAccount?.accountNumber &&
                              field.value === bankAccount?.accountNumber
                            ) {
                              setValue('accountNumber', '');
                            }
                          }}
                          error={errors.accountNumber?.message}
                          required={hasBankInfoChanged(watch() as BankAccount, bankAccount)}
                        />
                      )}
                    />
                  </>
                )}
              </fieldset>
            </Flex>
            <ErrorDisplay
              errors={Object.keys(errors).length}
              message={Object.values(errors)
                .map((error) => error?.message)
                .filter(Boolean)}
              visible={Object.keys(errors).length > 0}
            />
            <Flex gap="3" justify="end">
              <Button type="button" onClick={onCloseModal} variant="outline">
                Cancel
              </Button>
              <Button type="submit" loading={loading} disabled={loading || !isValidAddress}>
                Save
              </Button>
            </Flex>
          </form>
        </Flex>
      </Theme>
    </Modal>
  );
}
