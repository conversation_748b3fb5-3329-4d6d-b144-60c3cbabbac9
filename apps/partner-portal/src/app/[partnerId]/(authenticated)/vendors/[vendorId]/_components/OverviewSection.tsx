import CanAccess from '@/app/components/features/CanAccess';
import FieldDetail from '@/spa-legacy/common/components/FieldDetail';
import FieldDetailList from '@/spa-legacy/common/components/FieldDetail/FieldDetailList';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import { formatAddress, formatIfPhone, mask } from '@/spa-legacy/common/utilities/format';
import { AccountTypesDisplay } from '@/spa-legacy/common/utilities/payment';
import {
  checkFeatureAnyProgram,
  checkFeaturesAnyProgram,
} from '@/spa-legacy/utilities/checkFeature';
import { FeatureName, type Vendor } from '@bybeam/platform-types';
import { EnvelopeClosedIcon, Pencil1Icon } from '@radix-ui/react-icons';
import { Box, Button, Flex, Heading, IconButton, Link, Text, Tooltip } from '@radix-ui/themes';
import { useState } from 'react';
import EditVendor from './EditVendor';
import styles from './OverviewSection.module.css';

export default function OverviewSection(vendor: Vendor): JSX.Element {
  const { types, externalId, taxId, mailingAddress, phone, email, bankAccount } = vendor;
  const { programs } = usePartner();
  const showVendorId = checkFeatureAnyProgram(programs, FeatureName.PaymentsExternalTracking);
  const showAccount = checkFeaturesAnyProgram(programs, [
    FeatureName.PaymentsDirectDeposit,
    FeatureName.PaymentsPartnerIssued,
    FeatureName.PaymentsVendors,
  ]);
  const [modalOpen, setModalOpen] = useState(false);

  return (
    <Box className={styles.overviewSection} p="4">
      <Flex justify="between" align="start" gap="2" mb="2">
        <Heading size="4" as="h2">
          Overview
        </Heading>
        <CanAccess>
          <Button type="button" variant="outline" onClick={(): void => setModalOpen(true)}>
            <Pencil1Icon /> Edit Vendor Details
          </Button>
        </CanAccess>
      </Flex>
      {modalOpen && <EditVendor {...vendor} onCloseModal={(): void => setModalOpen(false)} />}
      <FieldDetailList>
        <FieldDetail label="Vendor Type">{types.map((type) => type.name).join(', ')}</FieldDetail>
        <FieldDetail label="Tax ID/SSN">{taxId ? mask(taxId) : '-'}</FieldDetail>
        <FieldDetail label="Mailing Address">{formatAddress(mailingAddress) ?? '-'}</FieldDetail>
        <FieldDetail label="Phone">{formatIfPhone(phone) ?? '-'}</FieldDetail>
        <FieldDetail label="Email Address">
          {email ? (
            <Flex align="center" gap="2">
              <Text truncate>{email}</Text>
              <Tooltip content="Send Email">
                <IconButton variant="ghost" asChild>
                  <Link href={`mailto:${email}`}>
                    <EnvelopeClosedIcon />
                  </Link>
                </IconButton>
              </Tooltip>
            </Flex>
          ) : (
            '-'
          )}
        </FieldDetail>
        {showVendorId && <FieldDetail label="Vendor External ID">{externalId ?? '-'}</FieldDetail>}
        {showAccount && (
          <>
            <FieldDetail label="Account Type">
              {AccountTypesDisplay[bankAccount?.accountType] ?? '-'}
            </FieldDetail>
            <FieldDetail label="Routing Number">{bankAccount?.routingNumber ?? '-'}</FieldDetail>
            <FieldDetail label="Account Number">{bankAccount?.accountNumber ?? '-'}</FieldDetail>
          </>
        )}
      </FieldDetailList>
    </Box>
  );
}
