query GetPaymentsByVendorId($vendorId: UUID!, $pagination: OffsetPagination, $sort: PaymentSort) {
  payments(vendorId: $vendorId, pagination: $pagination, sort: $sort) {
    pageInfo {
      count
    }
    payments {
      id
      displayId
      amount
      status
      method
      completedAt
      createdAt
      initiatedAt
      fulfillment {
        id
        case {
          id
          displayId
          applications {
            id
            displayId
          }

          program {
            id
            name
            features {
              id
              enabled
              feature {
                id
                name
              }
            }
          }
        }
        fulfillmentMeta {
          checkNumber
          billingCode
        }
      }
    }
  }
}
