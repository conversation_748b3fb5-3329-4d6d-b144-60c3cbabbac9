'use client';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import Notes from '@/spa-legacy/portal/components/Notes';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkFeatureAnyProgram } from '@/spa-legacy/utilities/checkFeature';
import { useQuery } from '@apollo/client';
import { FeatureName, type Vendor } from '@bybeam/platform-types';
import { Flex } from '@radix-ui/themes';
import { useParams, useRouter } from 'next/navigation';
import { ReactElement, useEffect } from 'react';
import DocumentSection from './DocumentSection';
import GetVendorQuery from './GetVendorQuery.graphql';
import OverviewSection from './OverviewSection';
import PaymentSection from './PaymentSection';
import TotalsSection from './TotalsSection';

export default function VendorDetails(): ReactElement {
  const { vendorId } = useParams<{ vendorId: string }>();
  const { programs } = usePartner();
  const { showSnackbar } = useSnackbar();
  const router = useRouter();

  const partnerIssuedPaymentsEnabled = checkFeatureAnyProgram(
    programs || [],
    FeatureName.PaymentsPartnerIssued,
  );

  const { data, loading, error } = useQuery<{ vendors: { nodes: Vendor[] } }>(GetVendorQuery, {
    variables: { id: vendorId, bankAccount: partnerIssuedPaymentsEnabled },
  });

  useEffect(() => {
    if (error) showSnackbar('Something went wrong! Please refresh and try again.');
  }, [error, showSnackbar]);

  const vendor = data?.vendors.nodes[0];

  useEffect(() => {
    if (!loading && !error && !vendor) {
      router.replace(makeRoute(PORTAL_ROUTES.VENDOR_OVERVIEW) as Route);
    }
  }, [loading, error, vendor, router]);

  if (loading) {
    return <LoadingComponent />;
  }

  if (error) {
    return <>Failed to load vendor details</>;
  }

  if (!vendor) return null;

  return (
    <Flex py="4" px="5" gap="4" direction="column">
      <Typography variant="h1">{vendor?.name}</Typography>
      <OverviewSection {...vendor} />
      <TotalsSection {...vendor} />
      <PaymentSection {...vendor} />
      <DocumentSection {...vendor} />
      <Notes relation={{ id: vendorId, type: 'vendor' }} notes={vendor?.notes || []} />
    </Flex>
  );
}
