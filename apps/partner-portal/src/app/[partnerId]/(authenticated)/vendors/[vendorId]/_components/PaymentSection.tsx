import Link from '@/spa-legacy/common/components/Link';
import Table from '@/spa-legacy/common/components/Table';
import TableHeader from '@/spa-legacy/common/components/Table/components/TableHeader';
import type { Column, TableState } from '@/spa-legacy/common/components/Table/types';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { DateFormat, formatCurrency, formatDate } from '@/spa-legacy/common/utilities/format';
import {
  PaymentMethodDisplay,
  PaymentStatusDisplay,
  getExternalPaymentStatusDisplay,
} from '@/spa-legacy/common/utilities/payment';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkFeatureAnyProgram } from '@/spa-legacy/utilities/checkFeature';
import { useLazyQuery } from '@apollo/client';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import {
  FeatureName,
  type Payment,
  PaymentSortColumn,
  SortDirection,
  type Vendor,
} from '@bybeam/platform-types';
import { useEffect, useMemo, useState } from 'react';
import type { IdType } from 'react-table';
import GetPaymentsByVendorIdQuery from './GetPaymentsByVendorIdQuery.graphql';

interface VendorPaymentRow {
  caseId: {
    displayId: string;
    link: string;
  };
  checkDate: string;
  checkNumber: string;
  amount: string;
  billingCode: string;
  status: string;
  method: string;
  programName: string;
  transactionId: string;
  transactionDate: string;
}

function getSortColumn(column: IdType<VendorPaymentRow>): PaymentSortColumn {
  switch (column) {
    case 'checkDate':
      return PaymentSortColumn.CheckDate;
    default:
      throw new Error(`Trying to sort by an invalid column: ${column}`);
  }
}

function coercePaymentRows(payments: Payment[]): VendorPaymentRow[] {
  return payments
    ?.map(
      ({
        amount,
        status,
        completedAt,
        initiatedAt,
        fulfillment,
        method,
        displayId,
      }): VendorPaymentRow | null => {
        if (!fulfillment) return null;
        const {
          case: { id: caseId, displayId: caseDisplayId, program },
          fulfillmentMeta,
        } = fulfillment;
        const date =
          completedAt || initiatedAt
            ? formatDate(completedAt || initiatedAt || '', DateFormat.ShortSlashes)
            : '';
        return {
          caseId: {
            displayId: caseDisplayId ?? '',
            link: makeRoute(PORTAL_ROUTES.CASE_REVIEW, { programId: program.id, caseId }),
          },
          transactionId: displayId,
          transactionDate: date,
          checkDate: date,
          status: checkFeature(program?.features, FeatureName.PaymentsExternalTracking)
            ? getExternalPaymentStatusDisplay(status)
            : PaymentStatusDisplay[status],
          amount: formatCurrency(amount, true),
          checkNumber: fulfillmentMeta?.checkNumber || '',
          billingCode: fulfillmentMeta?.billingCode || '',
          method: method ? PaymentMethodDisplay[method] : '',
          programName: program.name || '',
        };
      },
    )
    ?.filter(Boolean) as VendorPaymentRow[];
}

function massageData({ vendorId }: { vendorId: string }): {
  preppedData: VendorPaymentRow[];
  totalRows: number;
  loading: boolean;
  tableParams: TableState<VendorPaymentRow>;
  setTableParams: (params: TableState<VendorPaymentRow>) => void;
} {
  const [getPayments, { data: queryData, loading, error }] = useLazyQuery<{
    payments: { pageInfo: { count: number }; payments: Payment[] };
  }>(GetPaymentsByVendorIdQuery);
  const [tableParams, setTableParams] = useState<TableState<VendorPaymentRow>>({
    pageSize: 15,
    pageIndex: 0,
    sortBy: { id: 'checkDate', desc: false },
  });

  useEffect(() => {
    if (!tableParams) return;

    const { pageSize, sortBy, pageIndex } = tableParams ?? {};
    getPayments({
      variables: {
        vendorId,
        pagination: { page: pageIndex, take: pageSize },
        ...(sortBy && {
          sort: {
            column: getSortColumn(sortBy.id),
            direction: sortBy.desc ? SortDirection.Descending : SortDirection.Ascending,
          },
        }),
      },
    });
  }, [tableParams, getPayments, vendorId]);

  const [preppedData, setPreppedData] = useState<VendorPaymentRow[]>([]);
  const [totalRows, setTotalRows] = useState<number>(0);
  useEffect(() => {
    if (queryData) {
      setPreppedData(coercePaymentRows(queryData.payments.payments));
      setTotalRows(queryData.payments.pageInfo.count);
    }
  }, [queryData]);

  const { showSnackbar } = useSnackbar();
  useEffect(() => {
    if (error) showSnackbar('Uh oh! An error occurred! Please refresh and try again.');
  }, [error, showSnackbar]);

  return {
    preppedData,
    loading,
    totalRows,
    tableParams,
    setTableParams,
  };
}

export default function PaymentSection({ id }: Vendor): JSX.Element {
  const { loading, preppedData, tableParams, setTableParams, totalRows } = massageData({
    vendorId: id,
  });

  const COLUMNS: Column<VendorPaymentRow>[] = [
    {
      accessor: 'caseId',
      Header: 'Case ID',
      Cell: function ApplicationIdCell({
        value,
      }: {
        value: { link: string; displayId: string };
      }): JSX.Element {
        return (
          <Link to={value?.link as __next_route_internal_types__.RouteImpl<string>}>
            {value?.displayId}{' '}
          </Link>
        );
      },
    },
    {
      accessor: 'programName',
      Header: 'Program Name',
    },
    {
      accessor: 'method',
      Header: 'Payment Method',
    },
    {
      accessor: 'transactionId',
      Header: 'Transaction ID',
    },
    {
      accessor: 'checkDate',
      enableSorting: true,
      Header: 'Check Date',
    },

    {
      accessor: 'checkNumber',
      Header: 'Check #',
    },
    {
      accessor: 'transactionDate',
      Header: 'Transaction Date',
    },
    {
      accessor: 'amount',
      Header: 'Amount',
    },
    {
      accessor: 'billingCode',
      Header: 'Billing Code',
    },
    {
      accessor: 'status',
      Header: 'Status',
    },
  ];

  const WORKFLOW_COLUMNS: {
    [key in FeatureName.WorkflowExtended | FeatureName.WorkflowCore | 'multiProgram']: string[];
  } = {
    [FeatureName.WorkflowExtended]: [
      'caseId',
      'checkDate',
      'checkNumber',
      'amount',
      'billingCode',
      'status',
    ],
    [FeatureName.WorkflowCore]: [
      'caseId',
      'method',
      'transactionId',
      'transactionDate',
      'amount',
      'status',
    ],
    multiProgram: [
      'caseId',
      'programName',
      'method',
      'transactionId',
      'transactionDate',
      'amount',
      'status',
    ],
  };

  const usePaymentColumns = (): Column<VendorPaymentRow>[] => {
    const { programs = [] } = usePartner();

    const columns = useMemo(() => {
      let keys = WORKFLOW_COLUMNS[FeatureName.WorkflowCore];

      if (programs.length > 1) {
        keys = WORKFLOW_COLUMNS.multiProgram;
      } else if (checkFeatureAnyProgram(programs, FeatureName.WorkflowExtended))
        keys = WORKFLOW_COLUMNS[FeatureName.WorkflowExtended];

      return COLUMNS.filter((column) => keys.includes(column.accessor as string));
    }, [programs]);

    return columns;
  };
  const columns = usePaymentColumns();

  return (
    <Table
      columns={columns}
      data={{ rows: preppedData, total: totalRows }}
      state={tableParams}
      onStateChange={setTableParams}
      loading={loading}
      emptyMessage="No Payments Yet"
    >
      <TableHeader>
        <Typography variant="body" bold tag="h2">
          Payments to Vendor
        </Typography>
      </TableHeader>
    </Table>
  );
}
