import type { Meta, StoryObj } from '@storybook/nextjs';
import Page from './page';
import { CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK } from './page.mock';

const meta: Meta<typeof Page> = {
  component: Page,
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
      navigation: {
        segments: [['partnerId', 'mock-partner-id']],
      },
    },
  },
};
export default meta;

type Story = StoryObj<typeof Page>;

export const Default: Story = {
  parameters: {
    apolloClient: {
      mocks: [CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK],
    },
    nextjs: {
      navigation: {
        query: {},
      },
    },
  },
};

export const WithSearch: Story = {
  parameters: {
    apolloClient: {
      mocks: [CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK],
    },
    nextjs: {
      navigation: {
        query: { search: 'Cleaning' },
      },
    },
  },
};

export const WithSorting: Story = {
  parameters: {
    apolloClient: {
      mocks: [CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK],
    },
    nextjs: {
      navigation: {
        query: { sortBy: 'paymentsSent', sortDir: 'desc' },
      },
    },
  },
};

export const WithPagination: Story = {
  parameters: {
    apolloClient: {
      mocks: [CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK],
    },
    nextjs: {
      navigation: {
        query: { page: '1', pageSize: '25' },
      },
    },
  },
};
