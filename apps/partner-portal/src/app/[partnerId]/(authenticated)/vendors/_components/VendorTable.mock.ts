import CAN_ACCESS_QUERY from '@/app/providers/HasAccessQuery.graphql';
import VENDORS_LIST from '../graphql/ListVendors.graphql';

const CAN_ACCESS_PARTNER_MOCK = {
  request: {
    query: CAN_ACCESS_QUERY,
    variables: {
      hasAccessInput: {
        action: 'edit',
        resource: { objectId: 'mockPartnerId', objectType: 'ORGANIZATION' },
      },
    },
  },
  result: {
    data: {
      canAccess: {
        id: 'user:mock-user-id:partner:mockPartnerId',
        canAccess: true,
        message: 'user:mock-user-id can access partner:mockPartnerId',
        token: 'mockToken',
      },
    },
  },
  newData: () => ({
    data: {
      canAccess: {
        id: 'user:mock-user-id:partner:mockPartnerId',
        canAccess: true,
        message: 'user:mock-user-id can access partner:mockPartnerId',
        token: 'mockToken',
      },
    },
  }),
};

const VENDORS_LIST_MOCK = {
  request: {
    query: VENDORS_LIST,
    variables: {
      filter: { search: undefined },
      pagination: { page: 0, take: 25 },
      sort: { column: 'Name', direction: 'Ascending' },
    },
  },
  result: {
    data: {
      vendors: {
        nodes: [
          {
            id: 'mockVendorA',
            name: 'Brighter Days Cleaning',
            mailingAddress: {
              addressLine1: '650 S Town Center Dr',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89106,
            },
            types: [{ name: 'Cleaning Services' }],
            externalId: 'EXT1234',
            email: '<EMAIL>',
            phone: '+17024445555',
            aggregatePayments: { count: 10 },
          },
          {
            id: 'mockVendorB',
            name: 'Evergreen Landscaping',
            mailingAddress: {
              addressLine1: '2211 Rainbow Blvd',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89108,
            },
            types: [{ name: 'Landscaping' }],
            externalId: 'EXT9876',
            email: '<EMAIL>',
            phone: '+17025557890',
            aggregatePayments: { count: 24 },
          },
          {
            id: 'mockVendorC',
            name: 'Summit Property Management',
            mailingAddress: {
              addressLine1: '8465 W Sahara Ave',
              city: 'Las Vegas',
              state: 'NV',
              zip: 89117,
            },
            types: [{ name: 'Property Management' }],
            externalId: 'EXT5432',
            email: '<EMAIL>',
            phone: '+17027778888',
            aggregatePayments: { count: 36 },
          },
        ],
        pageInfo: { count: 3 },
      },
    },
  },
};

export { CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK };
