import isAccountNumberValid from '@/app/_utils/accountNumber';
import { checkFeatureAnyProgram } from '@/app/_utils/checkFeature';
import isRoutingNumberValid from '@/app/_utils/routingNumber';
import useForm, {
  conditionallyRequiredField,
  defaultRequiredErrorMessage,
  emailField,
  fieldUpdate,
  optionalField,
  phoneField,
  requiredField,
  ValidationMode,
} from '@/app/hooks/useForm';
import type { TemporaryNewDocument } from '@/spa-legacy/@types/document';
import Checkbox from '@/spa-legacy/common/components/Checkbox';
import DocumentUpload from '@/spa-legacy/common/components/DocumentUpload';
import Dropdown from '@/spa-legacy/common/components/Dropdown';
import ErrorDisplay from '@/spa-legacy/common/components/ErrorDisplay';
import Modal from '@/spa-legacy/common/components/Modal';
import TextInput from '@/spa-legacy/common/components/TextInput';
import Typography from '@/spa-legacy/common/components/Typography';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useGeocoder, { type GeocoderResult } from '@/spa-legacy/common/hooks/useGeocoder';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import Color from '@/spa-legacy/common/utilities/Color';
import getFormAddress from '@/spa-legacy/common/utilities/getFormAddress';
import { AccountTypesDisplay } from '@/spa-legacy/common/utilities/payment';
import AddressFieldVendor from '@/spa-legacy/portal/components/AddressFieldVendor';
import useVendorTypes from '@/spa-legacy/portal/hooks/useVendorTypes';
import { hasBankInfoChanged } from '@/spa-legacy/portal/utils/bankAccount';
import { useMutation } from '@apollo/client';
import { formatPhone, isPhoneNumber } from '@bybeam/formatting';
import {
  AccountType,
  type Address,
  type BankAccount,
  FeatureName,
  type MutationResponse,
  Program,
  type Vendor,
} from '@bybeam/platform-types';
import { Button, Flex } from '@radix-ui/themes';
import { type FormEvent, useState } from 'react';
import CreateVendorMutation from '../graphql/CreateVendorMutation.graphql';
import ListVendors from '../graphql/ListVendors.graphql';

interface AddVendorModalProps {
  onClose: () => void;
}

type OmitAddress = Omit<Address, 'id' | 'createdAt'>;

export default function AddVendorModal({ onClose }: AddVendorModalProps): JSX.Element {
  const [isManually, setIsManually] = useState(false);
  const [isValidAddress, setIsValidAddress] = useState(false);
  const partner = usePartner();
  const { programs } = partner;
  const collectExternalId = checkFeatureAnyProgram(
    programs as Program[],
    FeatureName.PaymentsExternalTracking,
  );
  const collectAccount =
    checkFeatureAnyProgram(programs as Program[], FeatureName.PaymentsDirectDeposit) &&
    checkFeatureAnyProgram(programs as Program[], FeatureName.PaymentsPartnerIssued);
  const bankAccountInitialValues = {
    accountType: undefined,
    routingNumber: '',
    accountNumber: '',
  } as unknown as BankAccount;
  const vendorTypes = useVendorTypes();
  const { formFields: addressFields, validationConfig: addressConfig } = getFormAddress();
  const { formData, trySubmit, dispatch, errors, isPristine, counters } = useForm(
    {
      typeIds: [],
      name: '',
      taxId: '',
      phone: '',
      email: '',
      files: undefined as unknown as TemporaryNewDocument[],
      externalId: '',
      accountType: bankAccountInitialValues.accountType,
      routingNumber: bankAccountInitialValues.routingNumber,
      accountNumber: bankAccountInitialValues.accountNumber,
    },
    {
      name: requiredField(undefined, defaultRequiredErrorMessage('Vendor Name')),
      phone: phoneField(false),
      email: emailField(false, 'email', defaultRequiredErrorMessage('Contact Email')),
      files: optionalField(),
      typeIds: requiredField(undefined, defaultRequiredErrorMessage('Vendor Type')),
      taxId: optionalField(),
      externalId: optionalField(),
      accountType: conditionallyRequiredField(
        undefined,
        (form) => collectAccount && hasBankInfoChanged(form, bankAccountInitialValues),
        defaultRequiredErrorMessage('Account Type'),
      ),
      routingNumber: conditionallyRequiredField(
        ({ routingNumber }) => isRoutingNumberValid(routingNumber),
        (form) => collectAccount && hasBankInfoChanged(form, bankAccountInitialValues),
        defaultRequiredErrorMessage('Routing Number'),
      ),
      accountNumber: conditionallyRequiredField(
        ({ accountNumber }) => isAccountNumberValid(accountNumber),
        (form) => collectAccount && hasBankInfoChanged(form, bankAccountInitialValues),
        defaultRequiredErrorMessage('Account Number'),
      ),
    },
    ValidationMode.RequiredOnSubmit,
  );

  const useFormResponse = useForm(
    {
      ...addressFields,
    },
    { ...addressConfig },
  );

  const [createVendor, { loading }] = useMutation<{
    vendor: { create: MutationResponse<Vendor, Vendor> };
  }>(CreateVendorMutation, { refetchQueries: [ListVendors] });

  const { showSnackbar } = useSnackbar();
  const { addressIsValid } = useGeocoder();

  const validateAddress = async (): Promise<OmitAddress> => {
    let validatedAddress: false | GeocoderResult = false;
    if (!isManually) validatedAddress = await addressIsValid(useFormResponse.formData);
    if (validatedAddress) {
      const { city, postalCode, stateCode, houseNumber, street, county } = validatedAddress.address;
      return {
        addressLine1: [houseNumber, street].filter(Boolean).join(' '),
        addressLine2: useFormResponse.formData.apartment,
        city,
        county,
        state: stateCode,
        zip: postalCode,
        latitude: validatedAddress.position.lat,
        longitude: validatedAddress.position.lng,
      };
    }
    return {
      addressLine1: useFormResponse.formData?.address,
      addressLine2: useFormResponse.formData?.apartment,
      city: useFormResponse.formData?.city,
      state: useFormResponse.formData?.state,
      zip: useFormResponse.formData?.zip,
    };
  };

  const onSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    if (isPristine) {
      onClose();
      return;
    }
    trySubmit(async (): Promise<void> => {
      const filteredFormData = Object.fromEntries(
        Object.entries(formData).filter(([_, value]) => value),
      ) as typeof formData;
      const { files, phone, accountNumber, accountType, routingNumber, ...rest } = filteredFormData;

      const mailingAddress = await validateAddress();
      const response = await createVendor({
        variables: {
          input: {
            ...rest,
            ...(phone && { phone: formatPhone(phone, 'E164') }),
            ...(collectAccount &&
              hasBankInfoChanged(formData, bankAccountInitialValues) && {
                bankAccount: { accountType, accountNumber, routingNumber },
              }),
            files: files?.map((file) => file.file) ?? [],
            mailingAddress,
          },
        },
      });
      const responseMetadata = response?.data?.vendor?.create?.metadata;
      if (!!responseMetadata?.status && responseMetadata?.status < 400) {
        showSnackbar('Vendor successfully created.');
        onClose();
      } else {
        showSnackbar(
          'There was an error creating the vendor.',
          <Button variant="text" textColor={Color.Link} onClick={(): Promise<void> => onSubmit(e)}>
            Try Again
          </Button>,
        );
      }
    });
  };

  const handleChangeAddress = (type: string, value: string): void => {
    useFormResponse.dispatch(
      fieldUpdate(type as 'address' | 'apartment' | 'city' | 'county' | 'state' | 'zip', value),
    );
  };

  return (
    <Modal isOpen title="Add Vendor" size="S" onClickClose={onClose}>
      <Flex asChild direction="column" gap="2" className="w-field">
        <form onSubmit={onSubmit} noValidate>
          <Typography variant="h3">Enter a new vendor into the system:</Typography>
          <div className="ml-2">
            <Checkbox
              label={
                <Typography variant="body" tag="span">
                  Manually input address
                </Typography>
              }
              checked={isManually}
              onClick={(): void => setIsManually((prevState) => !prevState)}
            />
          </div>
          <TextInput
            id="vendorName"
            label="Vendor Name"
            required
            value={formData.name}
            onChange={(value: string): void => dispatch(fieldUpdate('name', value))}
            error={errors.name as string}
          />
          <AddressFieldVendor
            handleChange={handleChangeAddress}
            isManually={isManually}
            setIsValidAddress={setIsValidAddress}
          />
          <TextInput
            id="phone"
            label="Phone Number"
            type="tel"
            value={formData.phone}
            onChange={(value): void => {
              const formatted = isPhoneNumber(value) ? formatPhone(value) : value;
              dispatch(fieldUpdate('phone', formatted));
            }}
            error={errors.phone as string}
          />
          <TextInput
            id="contactEmail"
            label="Contact Email"
            type="email"
            value={formData.email}
            onChange={(value: string): void => dispatch(fieldUpdate('email', value))}
            error={errors.email as string}
            required
          />
          <Dropdown
            id="vendorType"
            label="Vendor Type"
            required
            value={formData.typeIds}
            items={vendorTypes}
            onChange={(values: string[]): void => dispatch(fieldUpdate('typeIds', values))}
            multiple
            error={errors.typeIds as string}
          />
          {collectExternalId && (
            <TextInput
              id="externalId"
              label="Vendor External ID"
              value={formData.externalId}
              onChange={(value: string): void => dispatch(fieldUpdate('externalId', value))}
              error={errors.externalId as string}
            />
          )}
          {/* TODO: Should this be a protected/"password" field? */}
          <TextInput
            id="taxId"
            label="Tax ID/SSN"
            value={formData.taxId}
            onChange={(value: string): void => dispatch(fieldUpdate('taxId', value))}
            error={errors.taxId as string}
          />
          {collectAccount && (
            <>
              <Dropdown
                id="accountType"
                label="Account Type"
                value={formData.accountType}
                items={[
                  { label: AccountTypesDisplay[AccountType.Checking], value: AccountType.Checking },
                  { label: AccountTypesDisplay[AccountType.Savings], value: AccountType.Savings },
                ]}
                onChange={(value: AccountType): void => {
                  dispatch(fieldUpdate('accountType', value));
                }}
                error={errors.accountType as string}
                required={hasBankInfoChanged(formData, bankAccountInitialValues)}
              />
              <TextInput
                id="routingNumber"
                label="Routing Number"
                value={formData.routingNumber}
                onChange={(value: string): void => dispatch(fieldUpdate('routingNumber', value))}
                error={errors.routingNumber as string}
                required={hasBankInfoChanged(formData, bankAccountInitialValues)}
              />
              <TextInput
                id="accountNumber"
                label="Account Number"
                value={formData.accountNumber}
                onChange={(value: string): void => dispatch(fieldUpdate('accountNumber', value))}
                error={errors.accountNumber as string}
                required={hasBankInfoChanged(formData, bankAccountInitialValues)}
              />
            </>
          )}
          <DocumentUpload
            id="add-vendor-modal"
            documents={formData.files ? formData.files : []}
            onAddDocument={(docs: TemporaryNewDocument[]): void =>
              dispatch(fieldUpdate('files', docs))
            }
            onRemoveDocument={(id: string, url?: string): void =>
              dispatch(
                fieldUpdate(
                  'files',
                  formData.files.filter((file) => file.url !== url),
                ),
              )
            }
            buttonText="Upload W-9"
            containerStyle="flex flex-col gap-y-10 mb-2"
            loading={{ add: loading }}
          />
          <ErrorDisplay
            errors={counters?.missingRequiredFields}
            visible={counters?.missingRequiredFields > 0}
          />
          <Flex gap="3" mt="4" justify="end">
            {/* TODO: The address not being entered shouldn't disable this button, we should follow our 
          error handling approach and have the button enabled but an error appear when clicked */}
            <Button type="button" onClick={onClose} variant="outline">
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !isValidAddress}>
              Add
            </Button>
          </Flex>
        </form>
      </Flex>
    </Modal>
  );
}
