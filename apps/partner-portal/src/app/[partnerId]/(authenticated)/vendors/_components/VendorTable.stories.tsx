import type { Meta, StoryObj } from '@storybook/nextjs';
import VendorTable from './VendorTable';
import { CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK } from './VendorTable.mock';

const meta: Meta<typeof VendorTable> = {
  component: VendorTable,
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
      navigation: {
        segments: [['partnerId', 'mock-partner-id']],
      },
    },
  },
};
export default meta;

type Story = StoryObj<typeof VendorTable>;

export const Default: Story = {
  parameters: {
    canAccess: true,
    apolloClient: {
      mocks: [CAN_ACCESS_PARTNER_MOCK, VENDORS_LIST_MOCK],
    },
    nextjs: {
      navigation: {
        query: {},
      },
    },
  },
};

export const Empty: Story = {
  parameters: {
    canAccess: true,
    apolloClient: {
      mocks: [
        CAN_ACCESS_PARTNER_MOCK,
        {
          ...VENDORS_LIST_MOCK,
          result: {
            data: {
              vendors: {
                nodes: [],
                pageInfo: { count: 0 },
              },
            },
          },
        },
      ],
    },
    nextjs: {
      navigation: {
        query: {},
      },
    },
  },
};
