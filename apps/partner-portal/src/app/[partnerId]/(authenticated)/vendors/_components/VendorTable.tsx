'use client';

import CanAccess from '@/app/components/features/CanAccess';
import LoadingComponent from '@/app/components/ui/Loading/LoadingComponent';
import usePartner from '@/spa-legacy/common/hooks/contexts/usePartner';
import useSnackbar from '@/spa-legacy/common/hooks/useSnackbar';
import { formatIfPhone } from '@/spa-legacy/common/utilities/format';
import { checkFeatureAnyProgram } from '@/spa-legacy/utilities/checkFeature';
import { useQuery } from '@apollo/client';
import {
  FeatureName,
  type OffsetPagination,
  type PageInfo,
  type Sort,
  SortDirection,
  type Vendor,
  type VendorFilter,
} from '@bybeam/platform-types';
import {
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  Cross2Icon,
  MagnifyingGlassIcon,
  PlusIcon,
} from '@radix-ui/react-icons';
import {
  Box,
  Button,
  Flex,
  IconButton,
  Link as RadixLink,
  Select,
  Strong,
  Table,
  Text,
  TextField,
} from '@radix-ui/themes';
import type { Route } from 'next';
import NextLink from 'next/link';
import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import ListVendors from '../graphql/ListVendors.graphql';
import AddVendorModal from './AddVendorModal';
import styles from './VendorTable.module.css';

const ALLOWED_PAGE_SIZES = [10, 25, 50, 100] as const;
const SORT_COLUMN_MAP = {
  vendorName: 'Name',
  vendorTypes: 'Type',
  paymentsSent: 'Payments',
} as const;

type SortColumn = keyof typeof SORT_COLUMN_MAP;

export default function VendorsTable() {
  const { partnerId } = useParams<{ partnerId: string }>();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const partner = usePartner();
  const { programs } = partner;

  const [modalOpen, setModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(searchParams.get('search') || '');
  const debounceRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const page = Math.max(0, Number(searchParams.get('page')) || 0);
  const rawPageSize = Number(searchParams.get('pageSize')) || 25;
  const pageSize = ALLOWED_PAGE_SIZES.includes(rawPageSize as (typeof ALLOWED_PAGE_SIZES)[number])
    ? rawPageSize
    : 25;

  const rawSortBy = searchParams.get('sortBy');
  const sortBy: SortColumn =
    rawSortBy && rawSortBy in SORT_COLUMN_MAP ? (rawSortBy as SortColumn) : 'vendorName';
  const sortDir = searchParams.get('sortDir') === 'desc' ? 'desc' : 'asc';

  const updateUrl = useCallback(
    (updates: Record<string, string | undefined>) => {
      const params = new URLSearchParams(searchParams);
      for (const [key, value] of Object.entries(updates)) {
        value ? params.set(key, value) : params.delete(key);
      }
      router.push(`${pathname}?${params.toString()}` as Route);
    },
    [searchParams, pathname, router],
  );

  // Debounced search
  useEffect(() => {
    const searchFromUrl = searchParams.get('search') || '';
    if (searchInput === searchFromUrl) return;

    clearTimeout(debounceRef.current);
    debounceRef.current = setTimeout(() => {
      const params = new URLSearchParams(searchParams);
      searchInput.trim() ? params.set('search', searchInput.trim()) : params.delete('search');
      params.set('page', '0');
      router.replace(`${pathname}?${params.toString()}` as Route);
    }, 300);
  }, [searchInput, searchParams, pathname, router]);

  const { data, loading, error } = useQuery<
    { vendors: { pageInfo: PageInfo; nodes: Vendor[] } },
    { filter: VendorFilter; pagination: OffsetPagination; sort?: Sort<string> }
  >(ListVendors, {
    variables: {
      filter: { search: searchParams.get('search') || undefined },
      pagination: { page, take: pageSize },
      sort: {
        column: SORT_COLUMN_MAP[sortBy],
        direction: sortDir === 'desc' ? SortDirection.Descending : SortDirection.Ascending,
      },
    },
    fetchPolicy: 'cache-and-network',
  });

  const { showSnackbar } = useSnackbar();
  useEffect(() => {
    if (error) showSnackbar('Uh oh! An error occurred! Please refresh and try again.');
  }, [error, showSnackbar]);

  const vendors = data?.vendors.nodes ?? [];
  const totalCount = data?.vendors.pageInfo.count ?? 0;
  const totalPages = Math.ceil(totalCount / pageSize);
  const showVendorId = checkFeatureAnyProgram(programs || [], FeatureName.PaymentsExternalTracking);
  const columnCount = showVendorId ? 7 : 6;

  const handleSort = (column: SortColumn) => {
    updateUrl({
      sortBy: column,
      sortDir: sortBy === column && sortDir === 'asc' ? 'desc' : 'asc',
    });
  };

  // Reusable sort button component
  const SortButton = ({ column, label }: { column: SortColumn; label: string }) => {
    const isActive = sortBy === column;
    return (
      <Button
        variant="ghost"
        onClick={() => handleSort(column)}
        className={styles.sortButton}
        color="gray"
        highContrast
        aria-label={`Sort by ${label} ${isActive ? (sortDir === 'asc' ? 'descending' : 'ascending') : 'ascending'}`}
      >
        <Flex align="center" gap="1">
          <Strong>{label}</Strong>
          {isActive &&
            (sortDir === 'asc' ? (
              <ChevronUpIcon aria-hidden="true" />
            ) : (
              <ChevronDownIcon aria-hidden="true" />
            ))}
        </Flex>
      </Button>
    );
  };

  return (
    <>
      {modalOpen && <AddVendorModal onClose={(): void => setModalOpen(false)} />}
      <Flex direction="column" width="100%" maxWidth="1200px" mx="auto" height="100%" px="2">
        <Box pt="5">
          <Flex gap="3" justify="between" mb="4">
            <TextField.Root
              placeholder="Search vendors..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              size="2"
            >
              <TextField.Slot>
                <MagnifyingGlassIcon height="16" width="16" />
              </TextField.Slot>
              {searchInput && (
                <TextField.Slot>
                  <IconButton
                    size="1"
                    variant="ghost"
                    color="gray"
                    onClick={() => setSearchInput('')}
                    aria-label="Clear search"
                  >
                    <Cross2Icon height="14" width="14" />
                  </IconButton>
                </TextField.Slot>
              )}
            </TextField.Root>
            <CanAccess resource={{ objectId: partner?.id, objectType: 'ORGANIZATION' }}>
              <Button type="button" variant="outline" onClick={(): void => setModalOpen(true)}>
                <PlusIcon />
                Add New Vendor
              </Button>
            </CanAccess>
          </Flex>
          <Flex gap="3" align="center" justify="between" mb="2">
            <Text weight="medium" ml="2">
              {totalCount} {totalCount === 1 ? 'result' : 'results'}
            </Text>
            <Flex gap="3" align="center" asChild>
              <nav aria-label="Table pagination">
                <Flex gap="2" align="center">
                  <Text size="2" id="page-size-label">
                    Page size:
                  </Text>
                  <Select.Root
                    value={String(pageSize)}
                    onValueChange={(value) => updateUrl({ pageSize: value, page: '0' })}
                    aria-labelledby="page-size-label"
                  >
                    <Select.Trigger />
                    <Select.Content>
                      {ALLOWED_PAGE_SIZES.map((size) => (
                        <Select.Item key={size} value={String(size)}>
                          {size}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Root>
                </Flex>
                <Flex gap="2" align="center">
                  <IconButton
                    disabled={page === 0}
                    onClick={() => updateUrl({ page: String(page - 1) })}
                    variant="soft"
                    radius="full"
                    aria-label="Go to previous page"
                  >
                    <ChevronLeftIcon />
                  </IconButton>
                  <Text size="2" aria-live="polite" aria-atomic="true">
                    {totalCount > 0
                      ? `${page * pageSize + 1}-${Math.min((page + 1) * pageSize, totalCount)} of ${totalCount}`
                      : '0 vendors'}
                  </Text>
                  <IconButton
                    disabled={page >= totalPages - 1 || totalPages === 0}
                    onClick={() => updateUrl({ page: String(page + 1) })}
                    variant="soft"
                    radius="full"
                    aria-label="Go to next page"
                  >
                    <ChevronRightIcon />
                  </IconButton>
                </Flex>
              </nav>
            </Flex>
          </Flex>
        </Box>
        <Box flexGrow="1" overflow="auto" minHeight="0" mb="2">
          <Table.Root variant="surface">
            <Table.Header>
              <Table.Row>
                <Table.ColumnHeaderCell
                  minWidth="12rem"
                  aria-sort={
                    sortBy === 'vendorName'
                      ? sortDir === 'asc'
                        ? 'ascending'
                        : 'descending'
                      : 'none'
                  }
                >
                  <SortButton column="vendorName" label="Vendor Name" />
                </Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell minWidth="12rem">Mailing Address</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell
                  minWidth="8rem"
                  aria-sort={
                    sortBy === 'vendorTypes'
                      ? sortDir === 'asc'
                        ? 'ascending'
                        : 'descending'
                      : 'none'
                  }
                >
                  <SortButton column="vendorTypes" label="Vendor Type" />
                </Table.ColumnHeaderCell>
                {showVendorId && (
                  <Table.ColumnHeaderCell minWidth="7rem">Vendor ID</Table.ColumnHeaderCell>
                )}
                <Table.ColumnHeaderCell>Email</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell minWidth="8rem">Phone</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell
                  minWidth="8rem"
                  aria-sort={
                    sortBy === 'paymentsSent'
                      ? sortDir === 'asc'
                        ? 'ascending'
                        : 'descending'
                      : 'none'
                  }
                >
                  <SortButton column="paymentsSent" label="Payments Sent" />
                </Table.ColumnHeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {loading && vendors.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={columnCount}>
                    <Flex align="center" justify="center" p="6">
                      <LoadingComponent />
                    </Flex>
                  </Table.Cell>
                </Table.Row>
              ) : vendors.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={columnCount}>
                    <Flex align="center" justify="center" p="6">
                      <Text color="gray">No vendors found</Text>
                    </Flex>
                  </Table.Cell>
                </Table.Row>
              ) : (
                vendors.map((vendor) => (
                  <Table.Row key={vendor.id}>
                    <Table.Cell>
                      <RadixLink asChild>
                        <NextLink href={`/${partnerId}/vendors/${vendor.id}`}>
                          {vendor.name}
                        </NextLink>
                      </RadixLink>
                    </Table.Cell>
                    <Table.Cell>
                      {vendor.mailingAddress ? (
                        <Flex direction="column">
                          <Text size="2">
                            {vendor.mailingAddress.addressLine1}{' '}
                            {vendor.mailingAddress.addressLine2}
                          </Text>
                          <Text size="2">
                            {vendor.mailingAddress.city}, {vendor.mailingAddress.state}{' '}
                            {vendor.mailingAddress.zip}
                          </Text>
                        </Flex>
                      ) : (
                        '-'
                      )}
                    </Table.Cell>
                    <Table.Cell>
                      {vendor.types.map((type) => type.name).join(', ') || '-'}
                    </Table.Cell>
                    {showVendorId && <Table.Cell>{vendor.externalId ?? '-'}</Table.Cell>}
                    <Table.Cell>{vendor.email || '-'}</Table.Cell>
                    <Table.Cell>{formatIfPhone(vendor.phone) || '-'}</Table.Cell>
                    <Table.Cell>{vendor.aggregatePayments?.count ?? 0}</Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table.Root>
        </Box>
      </Flex>
    </>
  );
}
