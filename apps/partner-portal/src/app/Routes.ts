export enum PORTAL_ROUTES {
  HOME = 'HOME',
  ANALYTICS_DASHBOARD = 'ANALYTICS_DASHBOARD',
  APPLICANT_PROFILE = 'APPLICANT_PROFILE',
  APPLICANTS = 'APPLICANTS',
  APPLICATION_PREVIEW = 'APPLICATION_PREVIEW',
  APPLICATION_REVIEW_LEGACY = 'APPLICATION_REVIEW_LEGACY',
  APPLICATION_REVIEW = 'APPLICATION_REVIEW',
  AUTH_SIGNIN = 'AUTH_SIGNIN',
  BULK_PAYMENT = 'BULK_PAYMENT',
  CASE_REVIEW = 'CASE_REVIEW',
  CREATE_ACCOUNT = 'CREATE_ACCOUNT',
  FUNDS_OVERVIEW = 'FUNDS_OVERVIEW',
  FUND_DETAIL = 'FUND_DETAIL',
  PARTNER_DASHBOARD = 'PARTNER_DASHBOARD',
  PROGRAM_OVERVIEW = 'PROGRAM_OVERVIEW',
  SETTINGS = 'SETTINGS',
  VENDOR_DETAILS = 'VENDOR_DETAILS',
  VENDOR_OVERVIEW = 'VENDOR_OVERVIEW',
  ELIGIBILITY = 'ELIGIBILITY',
}

export const MakePortalRoutes = (): Record<PORTAL_ROUTES, string> => {
  const partnerId = typeof window !== 'undefined' ? window.location.pathname.split('/')[2] : '';
  return {
    HOME: `/${partnerId}`,
    ANALYTICS_DASHBOARD: `/${partnerId}/analytics`,
    APPLICANT_PROFILE: `/${partnerId}/applicants/:userId`,
    APPLICANTS: `/${partnerId}/applicants`,
    APPLICATION_PREVIEW: `/${partnerId}/programs/:programId/application-preview/:appConfigId`,
    APPLICATION_REVIEW_LEGACY: `/${partnerId}/cases/:caseId/applications/:applicationId`,
    APPLICATION_REVIEW: `/${partnerId}/programs/:programId/cases/:caseId/applications/:applicationId`,
    AUTH_SIGNIN: `/${partnerId}/auth/signin`,
    BULK_PAYMENT: `/${partnerId}/programs/:programId/bulk-payment`,
    CASE_REVIEW: `/${partnerId}/programs/:programId/cases/:caseId`,
    CREATE_ACCOUNT: `/${partnerId}/programs/:programId/create-account`,
    FUNDS_OVERVIEW: `/${partnerId}/funds`,
    FUND_DETAIL: `/${partnerId}/funds/:fundId`,
    PARTNER_DASHBOARD: `/${partnerId}/dashboard`,
    PROGRAM_OVERVIEW: `/${partnerId}/programs/:programId`,
    SETTINGS: `/${partnerId}/settings`,
    VENDOR_DETAILS: `/${partnerId}/vendors/:vendorId`,
    VENDOR_OVERVIEW: `/${partnerId}/vendors`,
    ELIGIBILITY: `/${partnerId}/rules`,
  };
};

export function makeRoute(route: PORTAL_ROUTES | string, params?: Record<string, string>): string {
  const portalRoutes = MakePortalRoutes();
  return Object.entries(params ?? {}).reduce(
    (path, [param, value]) => path.replace(`:${param}`, value),
    portalRoutes?.[route as PORTAL_ROUTES] || route,
  );
}

/**
 * Returns the appropriate home route based on the user's auth state.
 * Pure function that can be used anywhere (server components, middleware, etc.)
 */
export function getUserHomeRoute(user: { admin?: unknown } | null | undefined): string {
  if (!user) return '/signin';
  if (user.admin) return makeRoute(PORTAL_ROUTES.HOME);
  return '/404';
}
