import { PORTAL_ROUTES, makeRoute } from '@/app/Routes';

describe('makeRoute', () => {
  it('should handle ROUTE_PATHS constants', () => {
    vi.spyOn(window, 'location', 'get').mockReturnValue({
      pathname: '/partner/panopticon/',
    } as Location);
    expect(makeRoute(PORTAL_ROUTES.CASE_REVIEW, { caseId: '1234', programId: '5' })).toBe(
      '/panopticon/programs/5/cases/1234',
    );
  });
  it('should return the base route if no params are provided', () => {
    expect(makeRoute('/programs')).toBe('/programs');
  });

  it('should return the base route if an empty params object is provided', () => {
    expect(makeRoute('/programs', {})).toBe('/programs');
  });

  it('should return the base route if there is no match for the params', () => {
    expect(makeRoute('/programs', { applicationId: '1234' })).toBe('/programs');
  });

  it('should replace the param if a single param is provided', () => {
    expect(makeRoute('/programs/:programId', { programId: '1234' })).toBe('/programs/1234');
  });

  it('should replace the params if multiple params are provided', () => {
    expect(
      makeRoute('/programs/:programId/cases/:caseId', { programId: '1234', caseId: '5678' }),
    ).toBe('/programs/1234/cases/5678');
  });

  // We don't expect this to occur; if it does it's probably poor URL hygiene but it wouldn't
  // be the worst thing in the world if we had to change this logic
  it('will only replace the first instance of a param if there are multiple', () => {
    expect(makeRoute('/programs/:programId/subroute/:programId', { programId: '1234' })).toBe(
      '/programs/1234/subroute/:programId',
    );
  });
});
