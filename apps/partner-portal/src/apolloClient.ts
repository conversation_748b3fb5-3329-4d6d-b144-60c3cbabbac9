import { getApiUri } from '@/app/_utils/getApiUri';
import { ApolloClient, InMemoryCache, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { CustomHeaders } from '@bybeam/platform-types';
import createUploadLink from 'apollo-upload-client/createUploadLink.mjs';
import { BroadcastChannels } from './app/components/Broadcast';
import { auth } from './app/firebase';

const httpLink = createUploadLink({
  uri: `${getApiUri()}/graphql`,
  credentials: 'include',
});

const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    if (graphQLErrors.some(({ message }) => message === 'Unauthorized')) {
      BroadcastChannels?.signout?.postMessage('signout');
      return;
    }

    if (graphQLErrors.some(({ message }) => message === 'Forbidden')) {
      BroadcastChannels?.notification?.postMessage(
        'You do not have access to perform that action. If this persists, please contact Beam support.',
      );
      return;
    }

    if (graphQLErrors.some(({ message }) => message === 'Not Allowed')) {
      BroadcastChannels?.notification?.postMessage(
        'The platform is currently in read-only mode. If this persists, please contact Beam support.',
      );
      return;
    }
  }
});

// Required for added CSRF protection with graphql-upload + ApolloServer,
// see here: https://www.apollographql.com/docs/apollo-server/v3/data/file-uploads/
const headersLink = setContext(async (operation, prevContext) => {
  return await auth?.authStateReady().then(async () => {
    const token = (await auth?.currentUser?.getIdToken()) || null;
    const tenantId = auth?.currentUser?.tenantId || null;
    return {
      ...prevContext,
      headers: {
        ...prevContext.headers,
        ...(token && { [CustomHeaders.GCIPToken]: token }),
        ...(tenantId && { [CustomHeaders.GCIPTenantId]: tenantId }),
        'x-apollo-operation-name': operation.operationName,
      },
    };
  });
});

const documentMergePolicy = (_existing = [], incoming = []) => [...incoming];

// TODO: Include typeDefs from common graphql schema when available
const apolloClient = new ApolloClient({
  cache: new InMemoryCache({
    possibleTypes: {
      ApplicationField: [
        'AddressField',
        'CalculatedField',
        'CheckboxField',
        'CheckboxGroupField',
        'ComplexField',
        'DateField',
        'DocumentField',
        'DropdownField',
        'RadioListField',
        'TextField',
        'TypographyField',
      ],
      Payee: ['User', 'Vendor'],
    },
    typePolicies: {
      // Custom merge functions
      Application: { fields: { documents: { merge: documentMergePolicy } } },
      Case: { fields: { documents: { merge: documentMergePolicy } } },
      Vendor: { fields: { documents: { merge: documentMergePolicy } } },
      // Irregular keys
      ProgramApplicationConfiguration: { keyFields: ['programId', 'applicantTypeId'] },
      ProgramFundStats: { keyFields: ['fundId', 'programId'] },
      ApplicantProfileConfigs: { keyFields: ['partnerId', 'applicantTypeId'] },
    },
  }),
  connectToDevTools: true,
  link: from([errorLink, headersLink, httpLink]),
});
export default apolloClient;
