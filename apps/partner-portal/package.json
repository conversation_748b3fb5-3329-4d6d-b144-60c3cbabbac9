{"name": "@bybeam/partner-portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"start:dev": "next dev --webpack -p 3002", "build:prod": "NODE_OPTIONS='--max-old-space-size=4096' next build --webpack", "start:prod": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "chromatic": "chromatic --build-script-name storybook:build", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build"}, "dependencies": {"@apollo/client": "3.11.0", "@apollo/experimental-nextjs-app-support": "0.11.11", "@bybeam/doctopus-types": "workspace:*", "@bybeam/formatting": "workspace:*", "@bybeam/identity-client": "workspace:*", "@bybeam/platform-lib": "workspace:*", "@bybeam/platform-types": "workspace:*", "@bybeam/verification-types": "workspace:^", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.1", "@gorules/jdm-editor": "1.47.0", "@grafana/faro-web-sdk": "1.19.0", "@grafana/faro-web-tracing": "1.19.0", "@hookform/error-message": "2.0.1", "@mui/icons-material": "5.16.7", "@mui/material": "5.16.7", "@opentelemetry/api": "1.9.0", "@opentelemetry/sdk-trace-node": "1.30.1", "@preset-sdk/embedded": "0.1.13", "@radix-ui/react-accordion": "1.2.12", "@radix-ui/react-form": "0.1.8", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-navigation-menu": "1.2.14", "@radix-ui/react-popover": "1.1.15", "@radix-ui/react-select": "2.2.6", "@radix-ui/react-tooltip": "1.2.8", "@radix-ui/themes": "3.2.1", "@react-input/mask": "2.0.4", "@react-pdf/renderer": "4.3.1", "@schematichq/schematic-js": "0.1.14", "@statelyai/inspect": "0.4.0", "@tanstack/react-table": "8.10.7", "@vercel/otel": "1.12.0", "@xstate/react": "4.1.3", "apollo-upload-client": "18.0.1", "classnames": "2.5.1", "csv-parse": "5.5.3", "dayjs": "1.11.19", "firebase": "catalog:default", "flat": "5.0.2", "graphql": "16.11.0", "html-react-parser": "5.2.7", "imagekitio-react": "4.3.0", "isemail": "3.2.0", "lodash": "4.17.21", "next": "16.0.1", "next-runtime-env": "3.3.0", "posthog-js": "1.275.3", "react": "19.2.0", "react-dom": "19.2.0", "react-dropzone": "14.3.8", "react-headless-pagination": "1.1.6", "react-hook-form": "7.64.0", "react-intersection-observer": "9.16.0", "react-markdown": "8.0.7", "react-modal": "3.16.3", "react-month-picker": "2.2.1", "react-qr-code": "2.0.18", "react-string-replace": "1.1.1", "regenerator-runtime": "0.14.1", "sanitize-filename": "1.6.3", "sanitize-html": "2.17.0", "uuid": "9.0.1", "xstate": "5.23.0"}, "devDependencies": {"@bybeam/typescript-config": "workspace:*", "@babel/plugin-transform-runtime": "7.28.5", "@chromatic-com/storybook": "4.1.2", "@esbuild-plugins/node-globals-polyfill": "0.2.3", "@esbuild-plugins/node-modules-polyfill": "0.2.2", "@grafana/faro-webpack-plugin": "0.5.1", "@storybook/addon-docs": "10.0.2", "@storybook/addon-links": "10.0.2", "@storybook/addon-onboarding": "10.0.2", "@storybook/addon-vitest": "10.0.2", "@storybook/nextjs": "10.0.2", "@storybook/nextjs-vite": "10.0.2", "@testing-library/jest-dom": "6.9.1", "@testing-library/react": "16.3.0", "@types/apollo-upload-client": "18.0.0", "@types/flat": "5.0.5", "@types/lodash": "4.17.20", "@types/node": "catalog:default", "@types/react": "19.2.2", "@types/react-dom": "19.2.2", "@types/react-table": "7.7.20", "@types/sanitize-html": "2.16.0", "@types/uuid": "catalog:default", "@vitejs/plugin-react": "5.0.4", "@vitest/browser": "catalog:default", "@vitest/coverage-v8": "catalog:default", "autoprefixer": "10.4.21", "buffer": "6.0.3", "chromatic": "13.3.1", "graphql-tag": "2.12.6", "jsdom": "25.0.1", "mockdate": "catalog:default", "next-router-mock": "1.0.4", "playwright": "1.55.1", "postcss": "8.5.6", "postcss-flexbugs-fixes": "5.0.2", "postcss-import": "16.1.1", "postcss-nesting": "13.0.2", "postcss-preset-env": "10.4.0", "storybook": "10.0.2", "storybook-addon-apollo-client": "9.0.0", "tailwindcss": "3.4.0", "typescript": "catalog:default", "vite-plugin-graphql-loader": "catalog:default", "vite-tsconfig-paths": "5.0.1", "vitest": "catalog:default"}}