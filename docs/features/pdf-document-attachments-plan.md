# PDF Documentation Attachments Implementation Plan

## Overview

This document outlines the implementation plan for adding PDF document attachments to email notifications, specifically for case approval notices. The system will allow partners to upload PDF templates, configure field mappings, and automatically or manually send filled documents to applicants.

## User Story

**As a case manager, I would like to send PDF approval notices to applicants. The fields in the document map to application keys.**

## Context

- <PERSON><PERSON><PERSON> needs to send PDF notices upon case approval
- Partner will provide standard PDF notice templates with formatted fields
- System should generate final PDF with application data mapped to template fields
- Documents should be attached to existing email notifications via Mailgun

## Architecture Overview

### Core Components

1. **Documentation Template Storage** - PDF templates stored in GCS with metadata in database
2. **Field Mapping Engine** - Maps application data to PDF form fields using text replacement
3. **Documentation Generation Service** - Creates filled PDFs from templates and application data
4. **Email Integration** - Attaches generated PDFs to existing notification system
5. **Case Manager Interface** - UI for template management and approval controls

### Design Principles

- **Leverage Existing Patterns** - Build on W-9 PDF generation and Mailgun email system
- **Case Manager Control** - Similar interface to verification configs for field mapping
- **Single Attachment Focus** - One PDF attachment per notification (extensible later)
- **Event-Driven** - Integrate with existing case approval workflow

## Technical Implementation

### Database Schema

```sql
CREATE TABLE documentation_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  description TEXT,
  document_key VARCHAR NOT NULL, -- GCS path to PDF template
  partner_id UUID NOT NULL REFERENCES partners(id),
  program_id UUID REFERENCES programs(id), -- Optional program-specific
  
  -- Case manager controls
  field_mappings JSONB NOT NULL DEFAULT '{}', -- PDF field -> application key
  send_on_approval BOOLEAN DEFAULT false,     -- Auto-send toggle
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deactivated_at TIMESTAMP DEFAULT NULL,
);
```

### Key Entities

- **DocumentationTemplateEntity** - Template metadata and configuration
- **DocumentationService** - PDF generation and field mapping logic

### Field Mapping Strategy

- PDF templates must use formatted fields with application keys as text
- Text replacement engine substitutes `{{applicationKey}}` patterns
- Support for nested object access (e.g., `{{submitter.name}}`)
- Case managers can edit mappings via UI similar to verification configs

### Integration Points

1. **Case Approval Workflow** - Extend `TransitionCaseStatusOperation`
2. **Email Notifications** - Enhance `ApplicationApproved` notification
3. **Template Management** - New admin interface for partners
4. **Document Storage** - Use existing GCS integration patterns

## User Experience

### Template Setup (Partner Admin)
1. Upload PDF template with formatted fields
2. Configure field mappings using visual editor
3. Set auto-send preferences for case approval


### Applicant Experience
1. Receive email notification of case approval
2. PDF document attached with personalized information
3. Document contains all relevant approval details and next steps

## Technical Considerations

### Error Handling
- Template validation on upload
- Field mapping validation against application schema
- PDF generation failure fallback (email without attachment)

### Monitoring
- PDF generation success/failure metrics
- Template usage analytics

## Future Extensibility

While focused on PDF attachments initially, the system is designed for future expansion:

- **Multiple Document Types** - DOCX, HTML templates
- **Multiple Attachments** - Several documents per notification
- **Additional Triggers** - Payment notices, denial letters, status updates
- **Advanced Field Mapping** - Conditional logic, calculations, formatting

## Success Criteria

- [ ] Partners can upload and configure PDF templates
- [ ] PDF documents are generated with correct field mappings
- [ ] Documents are successfully attached to email notifications
- [ ] System handles errors gracefully without blocking case approval
- [ ] Performance meets existing notification system standards

## Dependencies

- Existing W-9 PDF generation system (pdf-lib)
- Mailgun email service with attachment support
- Google Cloud Storage integration
- Partner portal UI framework

