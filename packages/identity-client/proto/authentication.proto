syntax = "proto3";

package identity;

enum AuthenticationMechanism {
  UNKNOWN = 0;
  BASIC = 1;
  EMAIL_TOKEN = 2;
  SAML = 3;
  ANONYMOUS = 4;
}

message SessionRequest {
  optional string user_id = 1 [deprecated = true];
  string partner_id = 2;
  string token = 3;
  string tenant_id = 4;
  AuthenticationMechanism authentication_mechanism = 5;
  string headers = 6;
}

message ValidateRequest {
  string token = 1;
  string tenant_id = 2;
}

enum RecaptchaAction {
  UNKNOWN = 0;
  SIGNUP = 1;
  LOGIN = 2;
  RECOVER = 3;
}

message RecaptchaRequest {
  string token = 1;
  RecaptchaAction action = 2;
}

message RecaptchaResponse {
  float score = 1;
  string details = 2;
}

message Claims {
  string tenant_id = 1;
  string gcip_uid = 2;
  string user_id = 4;
  string partner_id = 3;
  optional string admin_id = 5;
  repeated string roles = 6;
  string session_id = 7;
  string identity_user_id = 8;
  string parent_partner_id = 9;
}

message SessionResponse {
  string message = 1;
  Claims claims = 2;
}


// Request = the raw 64-char secret that was generated by scripts/api-keys/generateApiKey.ts
message ValidateApiKeyRequest {
  string api_key = 1;
}

message ApiKeyClaims {
  string partner_id = 1;
  repeated string scopes = 2;
  string key_id = 3;
  string user_id = 4;
  optional string admin_id = 5;
  string identity_user_id = 6;
}

message ValidateApiKeyResponse {
  string message = 1;
  ApiKeyClaims claims = 2;
}