import { DocumentationTemplate, DocumentationTemplateField } from '@bybeam/platform-types';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import PartnerEntity from './PartnerEntity.js';
import ProgramEntity from './ProgramEntity.js';

@Entity('documentation_templates')
export class DocumentationTemplateEntity implements DocumentationTemplate {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @Column({ type: 'varchar', length: 255 })
  public name: string;

  @Column({ type: 'text' })
  public description: string;

  @Column({ type: 'varchar', length: 255, name: 'document_key' })
  public documentKey: string;

  @Column({ type: 'uuid', name: 'partner_id' })
  public partnerId: string;
  @ManyToOne(() => PartnerEntity, { eager: true })
  @JoinColumn({ name: 'partner_id' })
  public partner: PartnerEntity;

  @Column({ type: 'uuid', name: 'program_id', nullable: true })
  public programId?: string;
  @ManyToOne(() => ProgramEntity, { nullable: true })
  @JoinColumn({ name: 'program_id' })
  public program?: ProgramEntity;

  @Column({ type: 'jsonb', name: 'field_mappings' })
  public fieldMappings: Record<string, DocumentationTemplateField>;

  @CreateDateColumn({ type: 'timestamptz', name: 'created_at' })
  public createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz', name: 'updated_at' })
  public updatedAt?: Date;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deactivated_at' })
  public deactivatedAt?: Date;
}

export default DocumentationTemplateEntity;
