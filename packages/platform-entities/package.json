{"name": "@bybeam/platform-entities", "version": "0.0.1", "private": true, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "scripts": {"build": "tsc --build", "test": "vitest"}, "devDependencies": {"@bybeam/typescript-config": "workspace:*", "@vitest/coverage-v8": "catalog:default", "typescript": "catalog:default", "vitest": "catalog:default"}, "dependencies": {"@bybeam/notification-client": "workspace:^", "@bybeam/platform-types": "workspace:*", "typeorm": "catalog:default", "typeorm-encrypted": "0.8.0"}}