{"name": "@bybeam/scheduler-client", "version": "0.0.1", "description": "gRPC client for the scheduler", "private": true, "main": "dist/index.js", "type": "commonjs", "exports": {".": "./dist/index.js", "./*": "./dist/generated/*.js"}, "scripts": {"build": "pnpm generate && tsc --build", "generate": "sh ./scripts/generate.sh"}, "devDependencies": {"@tsconfig/node20": "20.1.6", "typescript": "catalog:default"}, "dependencies": {"@bybeam/typescript-config": "workspace:*", "@grpc/grpc-js": "1.10.10", "@grpc/proto-loader": "0.7.10", "google-proto-files": "4.0.0", "google-protobuf": "3.21.2"}}