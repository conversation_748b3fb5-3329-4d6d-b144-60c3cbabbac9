{"name": "@bybeam/cypress", "version": "0.0.0", "private": true, "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "scripts": {"local": "cypress open --e2e --browser chrome --env configFile=local", "dev": "cypress open --e2e --browser chrome --env configFile=dev", "run:e2e": "cypress run --e2e", "run:dev": "cypress run --e2e --browser chrome --env configFile=dev", "run:local": "cypress run --e2e --browser chrome --env configFile=local", "parallel:local": "cypress-parallel -s \"run:e2e\" -d ./cypress/e2e -t 3 -a '\" --env configFile=local \"' --verbose", "parallel:dev": "cypress-parallel -s \"run:e2e\" -d ./cypress/e2e -t 3 -a '\" --env configFile=local \"' --verbose"}, "devDependencies": {"@bybeam/platform-types": "workspace:*", "@types/cypress-dotenv": "3.0.0", "@types/fs-extra": "11.0.4", "@types/node": "catalog:default", "cypress": "13.13.0", "cypress-dotenv": "3.0.1", "cypress-multi-reporters": "2.0.5", "cypress-parallel": "0.15.0", "dotenv": "16.4.5", "fs-extra": "11.3.2", "typescript": "catalog:default"}, "dependencies": {"@bybeam/formatting": "workspace:^", "dayjs": "1.11.19"}}