{"name": "@bybeam/doctopus-entities", "version": "0.0.1", "private": true, "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "scripts": {"build": "tsc --build"}, "devDependencies": {"@bybeam/typescript-config": "workspace:*", "typescript": "catalog:default"}, "dependencies": {"@bybeam/doctopus-types": "workspace:*", "typeorm": "catalog:default"}}