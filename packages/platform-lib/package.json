{"name": "@bybeam/platform-lib", "version": "1.0.0", "description": "Library shared by platform services", "private": true, "type": "module", "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "exports": {"./*": "./dist/*.js"}, "scripts": {"build": "tsc --build", "test": "vitest"}, "dependencies": {"@bybeam/platform-types": "workspace:*", "@bybeam/identity-client": "workspace:^", "dayjs": "1.11.19", "deepmerge": "4.3.1", "password-validator": "5.3.0"}, "devDependencies": {"@bybeam/typescript-config": "workspace:*", "@tsconfig/node20": "20.1.6", "@vitest/coverage-v8": "catalog:default", "mockdate": "catalog:default", "typescript": "catalog:default", "vitest": "catalog:default"}}