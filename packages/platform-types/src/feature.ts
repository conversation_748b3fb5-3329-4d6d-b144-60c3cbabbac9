import PartnerFeature from './partnerFeature.js';
import ProgramFeature from './programFeature.js';

export type Features = (PartnerFeature | ProgramFeature)[];

export enum FeatureName {
  // Partner Features
  AnalyticsEmbed = 'Analytics: Embed',
  DocumentsClassify = 'Documents: Classify',
  AuthenticationBeamEmployees = 'Authentication: Beam Employees',
  AuthenticationMFA = 'Authentication: MFA',
  AuthenticationAnonymous = 'Authentication: Anonymous',
  AuthenticationHidePassword = 'Authentication: Hide Password Login',
  AuthenticationHidePartnerRedirect = 'Authentication: Hide Partner Redirect',
  Tags = 'Cases: Tags',
  BulkFileActions = 'Cases: Bulk File Actions',
  ApplicantComments = 'Applicant: Comments',
  SearchElasticsearch = 'Search: Elasticsearch',
  SearchUseEsDocument = 'Search: Use ES Document',
  UserValidation = 'User: Validation',

  // Program Features
  ApplicationScoring = 'Application: Scoring',
  ApplicationVerification = 'Application: Verification',
  MigrationLegacyUsers = 'Migration: Legacy Users',
  PaymentsApplicants = 'Payments: Applicants',
  PaymentsCheck = 'Payments: Check',
  PaymentsDownloadCheckPrint = 'Payments: Download Check Print',
  PaymentsClaimFunds = 'Payments: Claim Funds',
  PaymentsDirectDeposit = 'Payments: Direct Deposit',
  PaymentsExternalTracking = 'Payments: External Tracking',
  PaymentsMultiPayment = 'Payments: Multi Payment',
  PaymentsPartnerIssued = 'Payments: Partner Issued',
  PaymentsPrepaidCard = 'Payments: Prepaid Card',
  PaymentsVendors = 'Payments: Vendors',
  PaymentsZelle = 'Payments: Zelle',
  PaymentsRecurring = 'Payments: Recurring',
  ProgramsReferral = 'Programs: Referral',
  SearchAnswers = 'Search: Answers',
  TaxFormsCaseDisplay = 'TaxForms: Case Display',
  TaxFormsW9Collection = 'TaxForms: W9 Collection',
  VerificationDataLookup = 'Verification: Data Lookup',
  WorkflowCore = 'Workflow: Core',
  WorkflowExtended = 'Workflow: Extended',
  WorkflowAutoAssign = 'Workflow: Auto Assign',
  WorkflowCreateApplications = 'Workflow: Create Applications',
  WorkflowServicesAndOutcomes = 'Workflow: Services & Outcomes',
}

export interface Feature {
  id: string;
  name: FeatureName;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type CheckFeature = {
  programId: string;
  feature: FeatureName;
};

export default Feature;
