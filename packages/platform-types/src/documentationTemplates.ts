import { FileUpload } from 'graphql-upload/Upload.mjs';
import Partner from './partner.js';
import Program from './program.js';

export enum DocumentationTemplateFieldType {
  APPLICATION_ANSWER = 'applicationAnswer',
  TEXT = 'text',
}

export type DocumentationTemplateField = {
  value: string;
  type: DocumentationTemplateFieldType;
};

export type DocumentationTemplate = {
  id: string;
  name: string;
  description: string;
  documentKey: string;
  partnerId: string;
  programId?: string;
  fieldMappings: Record<string, DocumentationTemplateField>;
  createdAt: Date;
  updatedAt?: Date;
  deactivatedAt?: Date;
  partner: Partner;
  program?: Program;
};

export type CreateDocumentationTemplateInput = {
  name: string;
  description: string;
  file: FileUpload;
  programId?: string;
  fieldMappings: Record<string, DocumentationTemplateField>;
};
