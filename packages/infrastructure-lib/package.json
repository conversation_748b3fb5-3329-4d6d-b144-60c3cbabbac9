{"name": "@bybeam/infrastructure-lib", "version": "1.0.0", "description": "Library shared by services to facilitate infrastructure integration", "private": true, "type": "module", "engines": {"node": "20.10.0", "npm": ">=8.19.2"}, "exports": {"./*": "./dist/*.js"}, "scripts": {"build": "tsc --build", "emulator:seed": "PUBSUB_EMULATOR_HOST=http://0.0.0.0:8085 PUBSUB_PROJECT_ID=core-platform-local-beam node pubsub-emulator/init.js", "test": "vitest"}, "dependencies": {"@google-cloud/profiler": "6.0.3", "@opentelemetry/api": "1.7.0", "@opentelemetry/auto-instrumentations-node": "0.57.0", "@opentelemetry/exporter-metrics-otlp-http": "0.45.1", "@opentelemetry/exporter-trace-otlp-http": "0.45.1", "@opentelemetry/resources": "^1.23.0", "@opentelemetry/sdk-metrics": "1.23.0", "@opentelemetry/sdk-node": "0.50.0", "@opentelemetry/sdk-trace-base": "^1.21.0", "@opentelemetry/semantic-conventions": "^1.30.0", "opentelemetry-instrumentation-typeorm": "0.41.0", "pino": "9.0.0", "pino-cloud-logging": "1.0.6", "pino-http": "10.5.0"}, "devDependencies": {"@bybeam/typescript-config": "workspace:*", "@google-cloud/pubsub": "4.3.3", "@vitest/coverage-v8": "catalog:default", "pino-pretty": "13.1.2", "typescript": "catalog:default", "vitest": "catalog:default"}}