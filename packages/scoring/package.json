{"name": "@bybeam/scoring", "version": "0.0.1", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --build", "test": "vitest"}, "imports": {"#types*": "./src/@types/*"}, "devDependencies": {"@vitest/coverage-v8": "catalog:default", "typescript": "catalog:default", "vitest": "catalog:default"}, "dependencies": {"@bybeam/platform-entities": "workspace:*", "@bybeam/platform-types": "workspace:*", "@bybeam/typescript-config": "workspace:*", "dayjs": "1.11.19"}}