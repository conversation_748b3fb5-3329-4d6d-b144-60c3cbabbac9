-- Seed data for document_ai database programs
-- This file creates program entries for all test programs from core-db
-- Each program is configured to process driver's licenses using the default test document configuration

-- Get the default document configuration ID
-- This should match: af55451c-26d3-40a9-8127-bc2b2af029d1 (Test document configuration V1)

-- Insert programs for all test programs from core-db
-- Using core_program_id to link back to the programs table in core-db

-- Insert all test programs from core-db with their exact IDs and names
-- Programs are grouped by partner/source for clarity
-- Note: 'eb3b95a0-1b7a-4d68-abff-3c9daa874af8' (Partner-Issued Housing and Childcare Program) 
-- is already seeded in V20250728000000__program_documents_and_model.sql
INSERT INTO public.programs (id, core_program_id, name, created_at, deactivated_at) VALUES
    -- Westside County programs (partner: 40a541fb-10ad-4114-b7e0-34a93db822e4)
    (gen_random_uuid(), '1fa26621-47ee-4a49-9adf-3cc457406cf8', 'Cash Assistance', NOW(), NULL),
    (gen_random_uuid(), '5acf37fc-ce74-4c75-9d53-b3da4e9b0caa', 'Childcare subsidy', NOW(), NULL),
    (gen_random_uuid(), '1e6ab0ad-5f01-4a64-bddc-17e388497231', 'Workforce Stipend', NOW(), NULL),
    (gen_random_uuid(), '27c9c8d5-1b04-4ec5-87df-0f9b4c93fb23', 'Cash Assistance', NOW(), NULL),
    
    -- Panopticon (Edquity Test) programs (partner: fd1bc149-6e67-4cec-bb3e-9e6e48426114)
    -- SKIP: 'eb3b95a0-1b7a-4d68-abff-3c9daa874af8' already seeded in V20250728000000
    (gen_random_uuid(), 'f61b2c57-04b5-4c41-b066-42dd063a0c17', 'Recurring Food and Income Program', NOW(), NULL),
    (gen_random_uuid(), '58af831c-a6b3-4f2a-9431-a17992cc20e9', 'Closed Food and Income Program', NOW(), NULL),
    (gen_random_uuid(), '38f932da-b279-4bb9-b6d9-e35067124c34', 'External Tracking Tax Credits', NOW(), NULL),
    (gen_random_uuid(), '9fc08377-41c3-4fb4-80d2-14d98861ef93', 'Claim Housing and Income Program', NOW(), NULL),
    (gen_random_uuid(), '9e36b5fe-8b87-49ad-b335-07fbff6852d8', 'Multiparty Program', NOW(), NULL),
    
    -- CalGrows programs (partner: 18b9ba1f-178d-4e96-9594-7dbca93fc8d0)
    (gen_random_uuid(), 'e238e4aa-bec7-43c3-8d97-9eaffef3cc47', 'Learn and Earn Program', NOW(), NULL),
    (gen_random_uuid(), 'f333040e-1a31-49c8-aa03-e55d519e1fff', 'Career Builder Program', NOW(), NULL),
    
    -- New Hampshire program (partner: b89ebaef-f8d0-4575-a842-88044ceeccf3)
    (gen_random_uuid(), '601b6868-4c98-4d28-bda0-0d185035e76e', 'NHERAP', NOW(), NULL),
    
    -- UWKC and related programs
    (gen_random_uuid(), '741fc20b-489b-4eb4-9147-dcca63702ca8', 'UWKC Program', NOW(), NULL),
    (gen_random_uuid(), '91e1defd-161c-40c1-b708-cbb96dfab1ef', 'Claim Funds Program', NOW(), NULL),
    (gen_random_uuid(), 'b337852e-466c-4b02-9fcc-8059b60dc316', 'Partner Issued Payments Program', NOW(), NULL),
    (gen_random_uuid(), '671602dd-412a-44d3-9004-b906b2d912c6', 'Recurring Payments Program', NOW(), NULL),
    (gen_random_uuid(), '6d825036-ca88-4b3a-a3e1-a9673d24081d', 'Load Test Program', NOW(), NULL),
    (gen_random_uuid(), 'ac5ceab4-c453-479a-b598-72851f8a0682', 'External Payments Program', NOW(), NULL),
    
    -- Multi-Payment Program (from V20250226162904__multipayment-program.sql)
    (gen_random_uuid(), '793c6817-d586-4d4a-96d3-c85b7df51f6f', 'Multi-Payment Program', NOW(), NULL);

-- Configure all new programs to use the default test document configuration
-- This will enable driver's license processing for all programs
INSERT INTO public.program_document_configurations (program_id, document_configuration_id, created_at)
SELECT 
    p.id, 
    'af55451c-26d3-40a9-8127-bc2b2af029d1', -- Test document configuration V1
    NOW()
FROM public.programs p
WHERE p.core_program_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM public.program_document_configurations pdc 
    WHERE pdc.program_id = p.id
  );
