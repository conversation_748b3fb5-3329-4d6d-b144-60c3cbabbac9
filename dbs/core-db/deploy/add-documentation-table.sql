-- Deploy add-documentation-table

BEGIN;
SET search_path TO public;
SET ROLE migration;

CREATE TABLE documentation_templates
(
    id             UUID PRIMARY KEY         DEFAULT gen_random_uuid(),
    name           VARCHAR                                NOT NULL,
    description    TEXT,
    document_key   VARCHAR                                NOT NULL, -- GCS path to PDF template
    partner_id     UUID                                   NOT NULL,
    program_id     UUID,

    field_mappings JSONB                                  NOT NULL DEFAULT '{}',

    created_at     timestamp with time zone DEFAULT now() NOT NULL,
    updated_at     timestamp with time zone DEFAULT NOW() NOT NULL,
    deactivated_at timestamp with time zone DEFAULT NULL,

    FOREIGN KEY (partner_id) REFERENCES partners (id),
    FOREIGN KEY (program_id) REFERENCES programs (id)
);


CREATE TRIGGER documentation_templates BEFORE UPDATE ON documentation_templates FOR EACH ROW EXECUTE FUNCTION set_updated_at_timestamp();

COMMIT;