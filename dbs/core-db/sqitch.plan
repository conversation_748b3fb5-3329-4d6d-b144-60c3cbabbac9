%syntax-version=1.0.0
%project=beam-core-db
%uri=https://github.com/edquity/platform-core

initialize 2023-12-05T16:22:05Z System Administrator <root@js-mbp-m1> # coalesces existing migrations for core DB into a single initialization script for sqitch migration
add-tax-forms-deactivated-at 2023-12-07T15:20:01Z System Administrator <<EMAIL>> # adding deactivated at to tax_forms table
add-application-configurations-table 2024-01-05T20:47:16Z <PERSON> <slanden@Samanthas-Mac> # Add a new application_configurations table
document-classification-feature [initialize] 2024-01-17T17:39:22Z System Administrator <root@js-mbp-m1> # add new feature to send documents to doctopus for classification and ocr
add-email-templates 2024-01-16T18:14:45Z <PERSON> <slanden@Samanthas-Mac> # Create a new table for storing custom email templates
add-user-workflow-events 2024-01-18T21:13:41Z System Administrator <<EMAIL>> # Add workflow events for user create and update
create-applicant-types 2024-01-31T18:27:07Z System Administrator <<EMAIL>> # Create applicant types related tables
update-applicant-profiles 2024-01-31T22:17:49Z System Administrator <<EMAIL>> # Update applicant profiles with glocal applicant type id
backfill-program_applicant_types 2024-02-05T21:08:37Z Samantha Landen <slanden@Samanthas-Mac> # Backfill applicant types for all existing programs to include global Applicant type
update-table-applicant-profiles 2024-02-06T18:08:58Z System Administrator <<EMAIL>> # Alter applicant profiles table and make applicant type id column not nullable
add-case-link-tables 2024-02-06T19:43:32Z System Administrator <root@js-mbp-m1> # adds new tables for linking applications on multiparty programs
change-email-type-to-text 2024-02-12T17:33:47Z Samantha Landen <slanden@Samanthas-Mac> # Change the type of email_templates.type to plain text
all-email-template-fields 2024-02-22T15:38:05Z Samantha Landen <slanden@Samanthas-Mac> # Adds all email template configuration to the table, and support for non-partner-specific (default) templates
backfill-email-templates 2024-02-22T18:16:49Z Samantha Landen <slanden@Samanthas-Mac> # Backfill all existing email templates
update-table-case-participants 2024-02-27T15:23:51Z System Administrator <<EMAIL>> # Update table case participants by adding email
add_in_progress_case_status 2024-03-04T16:14:55Z Samantha Landen <slanden@Samanthas-Mac> # add a new InProgress value to the CaseStatus enum
backfill_in_progress 2024-03-07T22:26:20Z Samantha Landen <slanden@Samanthas-Mac> # Backfill In Progress status on existing cases
create-link-status-view 2024-03-07T21:22:47Z Samantha Landen <slanden@Samanthas-Mac> # Create a new view to assist with filtering on case participant link status
add-linking-workflow-events 2024-03-13T20:21:02Z System Administrator <<EMAIL>> # Add Linking workflow events
add-partner-features 2024-03-19T13:00:28Z System Administrator <root@js-mbp-m1> # rework after changes were deployed while this PR was open
partner-features-cleanup 2024-03-20T15:06:56Z System Administrator <root@js-mbp-m1> # remove old migrated program features
make_link_case_participant_optional 2024-03-27T20:02:21Z Samantha Landen <slanden@Samanthas-Mac> # Make case_participant_id on link_attempts table optional
identity-feature 2024-03-27T14:50:41Z System Administrator <root@js-mbp-m1> # add identity features
add-tags-tables 2024-04-30T14:31:58Z Samantha Landen <slanden@Samanthas-Mac> # Create basic tags tables
case-tags-join-table 2024-05-13T20:42:15Z Howie Bollinger <<EMAIL>> # Adds simple join table for Case and Tag objects
authentication-features 2024-05-16T19:06:57Z System Administrator <root@js-mbp-m1> # add authentication mechanism features
update-table-payment 2024-05-20T19:12:37Z System Administrator <<EMAIL>> # Add details (JSON) to the payment table
update-payment-pattern-enum 2024-06-04T21:07:34Z System Administrator <<EMAIL>> # Update paymentpattern enum, adding oneTime
add-email-case-comment-template 2024-07-02T21:51:10Z System Administrator <<EMAIL>> # Add email case comment template
add-comment-table 2024-07-05T18:51:58Z System Administrator <<EMAIL>> # Add comments table
update-workflow-events-table-with-user-ids 2024-07-16T18:11:10Z System Administrator <<EMAIL>> # makes adminId relation optional and adds user relation
add-notifications-table 2024-07-19T19:37:33Z System Administrator <<EMAIL>> # Adds notification table to help connect inbound comments
update-users-with-comms-preferences 2024-08-15T19:55:10Z System Administrator <<EMAIL>> # Update users table to include comms preferences column
add-release-authz-feature 2024-08-16T11:59:54Z System Administrator <root@js-mbp-m1> # add new feature for authz
backfill-user-ids-workflow-events 2024-08-22T16:32:02Z System Administrator <root@Samsaras-Mac> # add migration script
remove-release-identity 2024-08-27T16:53:24Z System Administrator <root@js-mbp-m1> # remove Release: Identity feature since it is used for all partners
add-applicant-comments-feature 2024-10-04T18:27:24Z System Administrator <<EMAIL>> # Add Applicant: Comments partner feature
application-verification-metadata 2024-10-03T16:52:11Z System Administrator <<EMAIL>> # add new json column for verification matching metadata
remove-release-authorization 2024-10-14T14:51:24Z System Administrator <root@MacBookPro> # remove release authorization feature flag
add-email-participant-case-comment-assignee-template 2024-10-14T16:45:16Z System Administrator <<EMAIL>> # This will add email template for ParticipantCaseCommentAssignee notification
add-email-participant-case-comment-support-template 2024-10-17T14:18:41Z System Administrator <<EMAIL>> # This will add email template for ParticipantCaseCommentSupport notification
changelog 2024-10-18T13:48:17Z System Administrator <root@MacBookPro> # add changelog table to hold updates and messages about partner portal
update-email-templates 2024-10-23T21:01:24Z System Administrator <<EMAIL>> # Update email templates to specify the domain for emails sent to advocates.
remove-roles-table 2024-10-24T19:21:50Z System Administrator <root@MacBookPro> # remove core db admin roles table as this is now controlled by spiceDB
add-table-saved-views 2024-12-02T19:04:43Z System Administrator <<EMAIL>> # Saved views table will be specific combination of search and filter criteria
add-notification-templates-table 2024-12-11T21:40:21Z System Administrator <<EMAIL>> # Adding notification templates table in order to support all channel templates in one central place
remove-email-templates-table 2024-12-17T17:05:33Z System Administrator <<EMAIL>> # Removing email_templates table
add-application-answer-note-table 2024-12-17T19:46:58Z System Administrator <<EMAIL>> # Adding a table to capture application answer level notes
add-field-review-table 2024-12-17T19:47:25Z System Administrator <<EMAIL>> # Add table to capture results of field review done per application field
add-sms-templates 2024-12-20T16:22:47Z System Administrator <<EMAIL>> # Adding notification templates for sms channel
alter-field-review-enum 2024-12-22T15:24:24Z Howie Bollinger <<EMAIL>> # Update fieldReview enum
add-review-enum-option 2024-12-30T16:08:58Z Howie Bollinger <<EMAIL>> # Adding a 'note only' option to the field review enum
add-template-unsubscribe-channel-confirmation 2025-01-03T15:18:26Z System Administrator <<EMAIL>> # Add notification template for UnsubscribeChannelConfirmation message
add-sensitive-answers-table 2025-01-09T03:52:44Z System Administrator <<EMAIL>> # This table is for storing sensitive application answers with encrypt values
remove-sensitive-answers-tables 2025-01-21T18:40:58Z System Administrator <<EMAIL>> # Reverting changes related to storing sensitive answers
update-application-answers-table-with-sensitive-value 2025-01-21T20:43:53Z System Administrator <<EMAIL>> # Alter application_answers table with adding sensitive_value
add-welcome-sms-template 2025-01-14T22:59:21Z System Administrator <<EMAIL>> # adds new global welcome sms template
update-template-unsubscribe-channel-confirmation 2025-01-15T16:55:37Z System Administrator <<EMAIL>> # updates langague for unsubscribe channel confirmation template
add-sms-help-template 2025-01-17T22:38:33Z System Administrator <<EMAIL>> # adds sms help template for when users reply help
update-latest-answers-view 2025-01-23T14:15:57Z System Administrator <<EMAIL>> # Update latest_answers in order to include sensitive_value
auth-option-feature-flags 2025-01-27T17:00:59Z System Administrator <root@MacBookPro> # adds feature flags for case worker redirect and hide password login
update-case-comment-message 2025-02-20T19:00:06Z System Administrator <<EMAIL>> # update case comment message for email and sms to remove redundant program name
add_application_verifications.application_version_id 2025-04-14T17:05:13Z Evan Volgas <<EMAIL>> # add missing indices
update-program-referral-notifications 2025-04-17T21:06:44Z System Administrator <<EMAIL>> # Alter program referral notification to have a correct condition for existing applications
search-features 2025-05-13T19:08:32Z System Administrator <root@MacBookPro> # add features to control search behavior in platform api
alter-funds-table 2025-05-19T21:20:53Z System Administrator <<EMAIL>> # Alter funds table adding dates
add-duplicate-document-stuff 2025-05-21T20:12:10Z Ben Crisman <<EMAIL>> # Adds a new duplicates table, a junction table to cross reference duplicates and documents, and adds a column to the documents table for a sha256 hash of the document content.
add-default-payment-field-key-to-program-funds 2025-06-16T17:05:00Z Charlie AI <<EMAIL>> # Adds default_payment_field_key column to program_funds table
move-default-payment-field-key-to-funds 2025-06-17T09:05:00Z Charlie AI <<EMAIL>> # Moves default_payment_field_key from program_funds to funds table
alter-payments-table-mailing-address 2025-06-13T14:48:16Z System Administrator <<EMAIL>> # Add mailing_address_type and mailing_address (JSONB) columns to payments table to support custom mailing addresses per payment, allowing users to override the default payee mailing address.
add-preset-info-to-analytics-resources-table 2025-06-20T14:14:20Z System Administrator <<EMAIL>> # Adds new columns to store workspace and dashboard ids
drop-users-password-column 2025-06-28T15:20:00Z Charlie AI <<EMAIL>> # drop password column from users table
drop-user_tokens-fk 2025-06-30T16:05:00Z Charlie AI <<EMAIL>> # drop FK on users pointing to user_tokens
remove-user-tokens 2025-06-30T16:15:31Z System Administrator <<EMAIL>> # remove user tokens not in use

add_rulesets_and_application_fields 2025-08-03T00:00:00Z Charlie AI <<EMAIL>> # add rulesets table + eligibility/score columns for rules-engine MVP
add_eligibility_reason 2025-08-10T00:00:00Z Charlie AI <<EMAIL>> # add eligibility_reason column for storing human-readable evaluation reason
add_ruleset_ref_on_application_versions 2025-08-11T00:00:00Z Charlie AI <<EMAIL>> # add ruleset_id + ruleset_version to application_versions for auditability
add-big-query-info-to-analytics-resources-table 2025-08-15T18:35:46Z System Administrator <<EMAIL>> # adds columns for the gcs table and dataset names so we can do direct downloads in the platform
add-form-definitions 2025-08-15T20:37:40Z Jeff Collar <<EMAIL>> # Adds a form definition table as part of the move away from appConfigs
add_last_evaluated_at_on_application_versions 2025-08-18T00:00:00Z Charlie AI <<EMAIL>> # add last_evaluated_at to application_versions for manual evals
add-table-analytics-reports 2025-08-20T17:23:36Z System Administrator <<EMAIL>> # Adding a table to manage scheduled report for analytics
update_eligibility_allowed_values 2025-08-22T00:00:00Z Charlie AI <<EMAIL>> # drop DB CHECK; enforce allow-list in platform-api
add-award-amount-max-to-funds 2025-08-25T15:45:00Z Charlie AI <<EMAIL>> # Add award_amount_max column (max per-disbursement, in cents) to funds
add_rulesets_author 2025-08-26T23:25:00Z Charlie AI <<EMAIL>> # add author column to rulesets for auditability
alter-application-answer-notes-primary-key 2025-09-11T21:37:32Z System Administrator <<EMAIL>> # Add primary key to application_answer_notes table
add-tag-automations-table 2025-09-09T17:38:16Z System Administrator <<EMAIL>> # Adding new table to store tag_automations
ENG-4699-duplicate-accounts 2025-09-23T19:50:37Z Ben Crisman <<EMAIL>> # Add duplicate accounts migration, this adds a new junction table that marries\nthe duplicates table with the application_versions table
add-new-application-report-template 2025-10-12T17:23:36Z System Administrator <<EMAIL>> # Adding new template for new application report
add-display-in-portal-to-analytics-resources-table 2025-10-13T19:21:42Z System Administrator <<EMAIL>> # adds a boolean flag to analytics resources table to toggle visibility in partner-portal
add-duplicate-detection-indexes 2025-10-19T21:30:00Z Howie Bollinger <<EMAIL>> # Add indexes to optimize duplicate detection query performance
ENG-4816-speed-up-ers-queries 2025-10-23T16:53:20Z Ben Crisman <<EMAIL>> # add indices for ers queries
add-anonymous-authentication-feature 2025-10-27T17:43:35Z System Administrator <<EMAIL>> # Add Authentication: Anonymous feature
add-documentation-table 2025-11-06T16:04:36Z System Administrator <<EMAIL>> # adding new table to store application answer based documentation templates that can be generated and sent via email
